
<template>
	<view class="gui-padding">
		<view class="gui-margin-top-large">
		</view>
		<view class="gui-flex gui-columns gui-align-items-center">
			<view class="">
				<canvas canvas-id="canvas" id="canvas" style="width:180rpx; height:180rpx;"></canvas>
			</view>
			<view class="my-30">
				<text class="gui-color-gray">就诊卡电子二维码</text>	
			</view>
		</view>
		<form >
			<!-- 姓名 -->
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">姓名</text>
				<text class=" gui-color-gray">{{res.userName}}</text>
			</view>
			
			<!-- 身份证号 -->
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">身份证号</text>
				<text class="gui-form-body gui-color-gray">{{res.idCard}}</text>
			</view>
			
			<!-- 就诊卡号 -->
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">就诊卡号</text>
				<text class="gui-form-body gui-color-gray">{{res.visitCardNum}}</text>
			</view>
			
			<!-- 设置默认就诊卡 -->
			<view class="gui-form-item gui-border-b gui-flex gui-rows gui-space-between">
				<text class="gui-form-label" style="width: 300rpx;">设为默认就诊人</text>
				<switch :checked="res.isDefaultPatient==1" v-model="res.isDefaultPatient" name="isDefaultPatient" @change="switchChange" color="#C59F79" />
			</view>
			<view class="gui-flex gui-columns gui-justify-content-center" style="margin-top: 45px;">
				<button type="primary" @tap="deleteCar"  class="bnt" style="border: 1px solid #C59F79;background:#FFFFFF;color: #C59F79;height: 40px;line-height: 40px;font-size: 34rpx;">删除就诊卡</button>
			</view>
		</form>
	</view>
</template>
<script>
	import { deleteCard,updatecard } from "@/api/my.js"
	import {getCard} from '@/api/home.js'
var QRCode = require('@/GraceUI5/js/qrcode.js');
export default {
	data() {
		return {
			// 密码可视 类型切换
			isPwd  : true,
			// picker 
			formData : {
				name4 : false
			},
			id:'',
			res:{}
		}
	},
	onLoad: function (options) {
		this.res = JSON.parse(decodeURIComponent(options.item));
		this.qrcode = new QRCode('canvas', {
			text         : this.res.visitCardNum,
			width        : uni.upx2px(180),
			height       : uni.upx2px(180)
		});
	},
	methods: {
		getCard(){
			getCard().then(res=>{
				var cardList = res.data.cardList;
				if(cardList.length>0){
					uni.setStorageSync('cardObj', cardList[0])
				}else{
					uni.setStorageSync('cardObj', {})
				}
			})
		},
		// switch 开关
		switchChange : function (e) {
			var value=1;
			var self=this;
			if(this.res.isDefaultPatient==1){
				value=0;
			}else{
				value=1;
			}
			var formData={
				id:self.res.id,
				isDefaultPatient:value,
			};
			var self=this;
			updatecard(formData).then(res=>{
				self.getCard();
				self.$common.msg(res.msg);
				setTimeout(function(){
					var pages = getCurrentPages();
					var prepage = pages[pages.length - 2]; //上一个页面
					prepage.$vm.ifOnShow = true;			
					uni.navigateBack();
				},1000);
			})
		},
		// 删除就诊卡
		deleteCar : function(){
			var self=this;
			uni.showModal({
				title: '温馨提示',
			  content: '确定删除此就诊卡吗？',
				confirmText:'确定',
				confirmColor:'#576B95',
				success: function (res) {
			       if(res.confirm) {
					   deleteCard({id:self.res.id}).then(res=>{
						   self.getCard();
						   self.$common.msg(res.msg,"success");
						   setTimeout(function(){
						   	var pages = getCurrentPages();
						   	var prepage = pages[pages.length - 2]; //上一个页面
						   	prepage.$vm.ifOnShow = true;			
						   	uni.navigateBack();
						   },1000);
					   })
					}
			    }
			});
		}
	}
}
</script>
<style>
.gui-card-img{width:250rpx; height:250rpx; border-radius:10rpx;}
.gui-text-small{line-height:50rpx;}
.gui-text-card{
	border: 1px solid #008AFF;
	border-radius: 5px;
	color:#008AFF;
	line-height: 30px;
	text-align: center;
}
.bnt{
	width: 60%;
	height: 30px;
	font-size: 12px;
	margin: 5rpx auto;
}
</style>