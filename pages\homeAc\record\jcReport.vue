<template>
	<view class="mx-40 my-20" style="height:  100vh;">
		<view class="gui-text-center gui-bold fs-30"> <text>{{list.applyItems}}</text></view>
			<view style="margin-top:15rpx" class="gui-flex gui-space-between">
					<view class="gui-text-small ">姓名：{{list.patientName}}</view>
					<view class="gui-text-small   ">性别：{{list.gender== 1 ?'男':'女'}}</view>
					<view class="gui-text-small ">年龄：{{list.age}}</view>
				</view>
				<view style="margin-top:15rpx" class="gui-flex gui-space-between">
						<view class="gui-text-small ">科室：{{list.applyDept}}</view>
						<view class="gui-text-small   ">医生：{{list.applyDoctor}}</view>
						<view class="gui-text-small "></view>
					</view>
					<view class="gui-table  gui-border-t" style="margin-top:50rpx; ">
						<view class=" gui-flex gui-rows gui-nowrap  ">
							<text class="gui-td1 p-20 td gui-border-r gui-border-b gui-td-text fs-30 gui-border-l  gui-text-center">临床诊断</text>
							<text class="gui-td2 gui-border-r gui-border-b gui-td-text gui-bold gui-text-center px-20"> </text>
						</view>
						<view class=" gui-flex gui-rows gui-nowrap  ">
							<text class="gui-td1 fs-30   p-20  td gui-border-r gui-border-b gui-td-text gui-align-items-center gui-border-l gui-flex gui-justify-content-center  gui-text-center">检查所见</text>
							<text  class="shsj gui-td2 gui-border-r gui-border-b gui-td-text py-40  px-20 fs-28 ">  {{list.observation}}</text>
						</view>
						<view style="margin-top:15rpx" class="gui-flex gui-space-between">
								<view class="gui-text-small ">申请医生：{{list.applyDoctor|| '-'}}</view>
								<view class="gui-text-small   "></view>
								<view class="gui-text-small ">检查日期：{{list.executeTime|| '-' }}</view>
							</view>
							<view style="margin-top:15rpx" class="gui-flex gui-space-between">
									<view class="gui-text-small ">报告人：{{list.reportor }}</view>
									<view class="gui-text-small   "></view>
									<view class="gui-text-small ">报告日期：{{list.reportTime }}</view>
								</view>

				
					</view>
					
	</view>
</template>

<script>
	import {getInspectInfo} from '@/api/home.js'
	export default {
		data() {
			return {
				info:'',
				list:[],
			}
		},
		onLoad: function(option) {
		this.info = JSON.parse(option.info);
		this.report();
	
			},
			
		methods: {
		report(){
			getInspectInfo(this.info.id).then(res=>{
				this.list=res.data;
			})
			// this.$common.RequestData({
			// 	url: this.$common.getInspectInfo+'/'+this.info.id,
			// 	data: {},
			// 	method: 'get',
			// }, res => {
			// 		console.log(res)
						
			
			// }, )
		},
		}
	}
</script>

<style scoped>
.demo{width:210rpx; height:88rpx; line-height:88rpx; text-align:center; margin:10rpx;}
.demo-auto-width{height:88rpx; line-height:88rpx; text-align:center; margin:10rpx;}
.gui-text-small{font-size:20rpx;}
.gui-td1{width:30%; display:flexbox;}
.gui-td2{width:70%; display:flexbox;}
.td{color:#7784eb;}
.shsj{text-indent:2em}
</style>
