 // 正式环境
 export default function cofing(e){
	var domain
	var socketUrl
	var tmplIds
	var tmplIdss
	var tmplIdsss
	var wxAppId //微信公众号appid
	var gzhName //微信公众号名称
	var gzhImg //微信公众号图片
	var webSrc
	var appName //医院名称（就诊卡显示的名字）
	//广中医
	if(e == 1){
		appName = '广西中医药大学第一附属医院';
		gzhName = '脾胃治未病';
		gzhImg = 'http://qiniu.starup.net.cn/img/gzy/%E5%B9%BF%E4%B8%AD%E5%8C%BB.jpg';
		wxAppId = 'wxbeffd33e9ec2b2b9';
		webSrc = 'https://fy.gzy.starup.net.cn/pages/login/webview?type=100'; //广中医公众号授权H5地址
		// domain = 'https://zyjk.zyy.nuojia.top/prod-api';//医院地址2023-09-060暂停使用
		domain = 'https://zwb.gzy.starup.net.cn/prod-api';//医院地址
		socketUrl = 'wss://zwb.gzy.starup.net.cn/ws';     //医院环境socket
		tmplIds = [
			'y3hni44E76jxT38YwzSnA_uAUYkrbZx_CAJRpwd7FSU',//宣教科普
			'tz6waOgWJ8l2cQjgINa4cWrMqbmIYARemMqQ_7rPmHo',//未读消息
			'33j9l8N5knEiNpN35uhz4qzzVLAHKMCjCcp6G7tB3B0'//监测逾期三天
		]; //广中医院地址
		tmplIdss = [
			'4lFV0CgAPnM7b0PhyI777t54K4r6mdBrnZupelzycqg',//监测
			'z83X56L_us3O7vm8O5eiyzrJSn4HxQbJpkHxHLdG8AA',//随访
			'y3hni44E76jxT38YwzSnA7s8AYwmFflS--9CdrMsphY'//调理建议
		];
		tmplIdsss = [
			'y_FtJ-3QV_hwNlkcmbUu6j5Xgi3MOJifri7fNHGmfxI',//活动报名
			'9LpCec0ThyOz-hh2DkroeNvIznoukVvFpwOK5GRUbVU',//复诊
			'xk5api2OPbK_6qK1kKM4ptH4aSarMgqgC9-LEktSLJc' //生日祝福
			];
	}else if(e == 2){
		// 贵阳
		appName = '贵阳市中医名医会馆';
		gzhName = '贵阳市中医名医会馆';
		gzhImg = 'https://img.starup.net.cn/xmkj/zwb/2023/09/01/a501cd7094e5422b878d999f26204ae3.jpg';
		wxAppId = 'wxf591803eb5713704';
		webSrc = 'https://gyjk.starup.net.cn/auth_h5/pages/index/webview?type=100'; //公众号授权H5地址
		domain = 'https://gyjk.starup.net.cn/prod-api';//贵阳医院地址
		socketUrl = 'wss://gyjk.starup.net.cn/ws';     //贵阳医院环境socket
		tmplIds = [
			'_J3eIP7_gWwxdYMYJ1BmKEdbwOFRDKyL8XjL1C9UlJw',//复诊
			'gWIxIsTXKb0YC_qCPWxtJz7sACgTjsc0ge_ZWkwteeQ',//监测逾期三天
			'9qgUmnYSrdseCHfOKbZkqaD1QYc9hapkhBC-UuJgNXQ'//消息未读提醒
		] //贵阳名医馆小程序
		tmplIdss = [
			'faD547X6u8uzglxggjQgi3OM0NVlkY4QV2YXIqtiEtM',//监测
			'M3sI_LBqvaXa3k7CKoSMM8YlxVrYAL5SJQff-sR1NG8',//随访问卷
			'50xcDeCQLKfgTLgYl_VOcMnDhaiVT63bYXWOMD4Hh2c'//调理建议+宣教科普
		];
		tmplIdsss = ['11']; //问卷填写通知
	}else if(e == 3){
		// 灵山
		appName = '灵山县中医医院';
		gzhName = '灵山县中医医院治未病科';
		gzhImg = 'https://img.starup.net.cn/xmkj/zwb/2023/09/01/78509be522a14db2b86928a9bf09d553.jpg';
		wxAppId = '';//wxbeffd33e9ec2b2b9 //需要公众号授权时才填入对应的AppId，否则留空
		webSrc = 'https://lszwb.frp.starup.net.cn/auth_h5'; //公众号授权H5地址
		domain = 'https://lszwb.frp.starup.net.cn/prod-api/';//公司外网地址
		socketUrl = 'wss://lszwb.frp.starup.net.cn/ws';     //公司环境socket
		tmplIds = [
			'BU5_m_gvOcS0-6SYVvDVAonpSDHdVMZgZlCM46Whw_w',//预约治疗
			'Jm93P3q9joUULq6k5X_Ij1WpJw_HttcRw4DbpXrkLa8',//监测逾期提醒
			'o9Ao2kwxRS6cjTv-_Z70bFtFAuOtA4qfBhYVL1a6QZ8'//复诊
		] ;
		tmplIdss = [
			'CGhiyFHdXQz57DVYsn1x_Tmwjnbi0sgpPs3D7_hgdS8', //健康指导提醒
			'MSZCOjC-e5tJWODdOP05i38YTrDbPKUZyJmIKwNDmok',//问卷填写通知
			'92ofXSfYlNIi1bp3A62v0KnEIj2I1G546_KJuzmSztU'//宣教任务
		];
		tmplIdsss = [
			// 'Yju3w2JgPlOpboLsDMKPVL6svnvu7eGoT5G4KyfS6Pw',//未读消息
			'roERwuJMZwFIWi0nUpCy1Z0XLPNJGSvGp1Xl4uyiYhA'//活动通知
			];
	}else if(e == 4){
		// 南宁中医院
		appName = '南宁市中医医院';
		gzhName = '南中医治未病健康管理';
		gzhImg = '';//https://img.starup.net.cn/xmkj/zwb/2023/09/01/78509be522a14db2b86928a9bf09d553.jpg
		wxAppId = '';//wxbeffd33e9ec2b2b9 // 需要微信公众号授权时才需要填入wxAppId，否则留空
		webSrc = ''; //公众号授权H5地址https:https://nnzyy.xm.starup.net.cn/auth_h5
		domain = 'https://zwb.nnszyy.cn:8180/prod-api/';//医院外网地址
		socketUrl = 'wss://zwb.nnszyy.cn:8180/ws';     //医院环境socket
		tmplIds = [
			'BAkfperxwblv1M9qlMUtsdpoveHPpUHwbs_bmWt5HBU',//预约成功通知---治疗预约
			'qfcceDfDfnI0l9_kU5Y3ok716Ge7dM4UK258BGSd1t0',//问卷填写通知
			'b7iTz2Mr3kusmSVNHKrXzd7duHv8u3mZ0y9hx8seMIU'//健康活动报名通知---活动和培训
		] ;
		tmplIdss = [
			'I8NPx70i_pk3J-izHwbkBkQJTCh2rMV137LOGrdsVFw', //健康指导提醒---宣教
			'I8NPx70i_pk3J-izHwbkBjLUJcNeLewAHwEHk4TU67w',//健康指导提醒---调理建议
			'ha12ZVvg8z-9qUiTZd2rKH8EJyx_hITCiXvtJYW6RW0'//健康计划通知---打卡监测类
		];
		tmplIdsss = [
			'IcOBYrZntHaIZ67iqd1lfFnRCIP0jfP0IYiepGf2IcM',//健康计划逾期三天提醒
			'rWjfqIweXRcB4j4nGlDpH1Fvu1S9kYi67vz2k5xRnu8',//复诊提醒
			'p_3gxG_lNtdaDH2XwPIn4C25vynnkOTWCai2cRJQ3Q4'//生日祝福提醒
			];
	}else if(e == 5){
		// 南宁七医院
		appName = '南宁市第七人民医院';
		gzhName = '南宁市第七人民医院';
		gzhImg = '';//https://img.starup.net.cn/xmkj/zwb/2023/09/01/78509be522a14db2b86928a9bf09d553.jpg
		wxAppId = '';//wxbeffd33e9ec2b2b9 // 需要微信公众号授权时才需要填入wxAppId，否则留空
		webSrc = ''; //公众号授权H5地址https:https://nnzyy.xm.starup.net.cn/auth_h5
		domain = 'https://shezhen.starup.net.cn/prod-api/';//医院外网地址
		socketUrl = 'wss://shezhen.starup.net.cn/ws';     //医院环境socket
		tmplIds = ['1'] ;
		tmplIdss = ['2'];
		tmplIdsss = ['3'];
	}else if(e == 6){
		// 广西中医药大学一附院智慧医院 -慢病管理
		appName = '广西中医药大学第一附属医院';
		gzhName = '广西中医药大学一附院智慧医院';
		gzhImg = 'http://oss.gzy.starup.net.cn/zyy/manbing/2023/12/08/********************************.png';
		wxAppId = 'wx5f126a213eaeaee1'; //wx5f126a213eaeaee1 // 需要微信公众号授权时才需要填入wxAppId，否则留空
		webSrc = 'https://fy.gzy.starup.net.cn/pages/login/webview?type=100'; //公众号授权H5地址
		//domain = 'https://zwb.gzy.starup.net.cn/mb-api';//医院外网地址
		domain = 'http://192.168.1.71:8880';
		// domain = 'http://192.168.1.71:8084/dev-api';
		socketUrl = 'wss://zwb.gzy.starup.net.cn/mb-ws';     //医院环境socket
		tmplIds = ['1'] ;
		tmplIdss = ['2'];
		tmplIdsss = ['3'];
	}
	return {
		domain,
		socketUrl,
		tmplIds,
		tmplIdss,
		tmplIdsss,
		wxAppId,
		gzhName,
		gzhImg,
		webSrc,
		appName
	}
}
