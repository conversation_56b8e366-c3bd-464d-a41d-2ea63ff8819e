<template>
	<view style="height: 100%;">
		<view class="head_A">
			<view class="report_head">
				<view class="">
					{{ userinfo.name || '-' }}({{userinfo.sex == 1 ?'男':'女'}})
				</view>
				<view class="hx" />
				<view class="head_img">
					<image src="../../static/empty_avatar.png" mode=""></image>
				</view>
				<view class="hx" />
				<view class="">
					{{ userinfo.age || '-' }}
				</view>
			</view>
		</view>
		<view class="head_B">
			记录日期：{{ userinfo.measureDate && $common.parseTime(userinfo.measureDate, '{y}-{m}-{d}') || '-' }}
		</view>
		<view class="head_C gui-padding gui-border-radius">
			<view class="gui-text-center">
				<text class="head_C_value">{{ userinfo.height || '-' }}</text>
				<view class="mt-15 head_C_tile gui-padding">身高cm</view>
			</view>
			<view class="gui-text-center">
				<text class="head_C_value">{{ userinfo.weight || '-' }}</text>
				<view class="mt-15 head_C_tile gui-padding">体重kg</view>
			</view>
		</view>
		<!-- 底部 -->
		<view class="propose gui-padding" >
			<view class="gui-flex gui-space-between mt-50">
				<view class=" ">
					<view class="propose_log">
						<image src="https://img.starup.net.cn/xmkj/zwb/img/hz_btn_cant.png"  mode=""></image>
						<text class="pl-10"> 结论建议:</text>
					</view>
				</view>
				<view class="" v-if="domainType !=3">
					<view class="gui-color-blue " @click="$common.navCloseTo('/pages/home/<USER>/chat?key=' + 1)"> <text class="gui-icons">&#xe626;</text>【立即咨询】 </view>
				</view>
			</view>
			<view class="propose_net mt-20">
				<view class="">
					<view class="fs-32">
						根据卫健委2018版身高对照表一一最新版。
						<text v-if="userinfo.heightState === 0">宝贝身高正常。</text>
						<text v-if="userinfo.heightState != 0">
							宝贝身高
							<text class="gui-color-blue">{{userinfo.heightState > 0 ? '高于' : '低于' || ''}}</text>
							正常身高{{ userinfo.pianCha || '' }}cm。
						</text>
						<text v-if="userinfo.weightState === 0">宝贝体重正常。</text>
						<text v-if="userinfo.weightState != 0">
							宝贝体重
							<text class="gui-color-blue">{{userinfo.weightState > 0 ? '高于' : '低于' || ''}}</text>
							正常体重{{ userinfo.tzPianCha || '' }}kg。
						</text>
					</view>
				</view>
				<view class="mt-20" v-if="userinfo.weightState != 0 ||userinfo.heightState != 0" >
					<text class="gui-color-red">提醒：</text>请遵从指导医生的健康管理方案，方案执行过程中可点击右上角咨询按钮联系医生！<!-- 请尽快咨询医生排查影响体重或者身高增长的因素! -->
				</view>
			</view>
		</view>

		<!-- 表格 -->
		<view class="propose gui-padding " style="height: unset;">
			<view class="propose_log mt-50">
				<image src="../../static/img/shuju.png" mode=""></image>
				<text class="pl-10">卫健委2018版身高、体重对照表——最新版</text>
			</view>
		</view>
		<view class="gui-margin-top gui-padding pb-50" >
			<litable :tableData="contents"></litable>
		</view>
	</view>
</template>
<script>
	// import wybTable from '@/components/wyb-table/wyb-table.vue'
	import litable from "./component/litable.vue"
	import {
		getAssessment,getHeightStandardList
	} from '@/api/children.js'
	export default {
		components: {
			litable
		},
		data() {
			return {
				domainType:this.$common.domainType,
				//患者信息
				userinfo: {},
				//对照表数据
				contents: [],
			}
		},
		onLoad() {
			// this.$forceUpdate()
			this.getAssessment()
		},
		methods: {
			//获取用户评估档案
			getAssessment() {
				var that = this
				getAssessment(uni.getStorageSync("cardObj").patientId).then(res => {
					//判断是否填数据
					if (res.data == null) {
						uni.navigateTo({
							url: '/pages/children/index'
						})
					} else {
						uni.setStorageSync('legao', res.data)
						that.userinfo = res.data
						this.loadAssessmentTable()
					}
				})
			},
			loadAssessmentTable() {
				getHeightStandardList(uni.getStorageSync("cardObj").patientId).then(res=>{
					this.contents = res.data
				})
			},
		}
	}
</script>

<style lang="less" scoped>
	page {

		height: 100% !important;
	}

	.dibu {
		width: 100%;
		display: flex;
		flex-direction: row;
		justify-content: center;

		.dibu_fu {
			display: flex;

			image {
				width: 100rpx;
				height: 100rpx;
			}

			text {
				display: block;
			}

		}

	}

	.Ahead_C_value {
		width: 80rpx;
		font-size: 24rpx;
		height: 30rpx;
		text-align: center;
		margin-left: 5rpx;
		display: inline-block;
		border-radius: 50rpx;
		background-color: aqua;
		top: -50rpx;
	}

	.head_A {
		width: 100%;
		height: 300rpx;
		margin: 0 auto;
		display: flex;
		justify-content: center;
		color: white;
		background-color: #C59F79;
		// background: url("https://img.starup.net.cn/xmkj/zwb/img/3.png") no-repeat no-repeat;
		// background-size: 100% 100%;
		// background-position: 50% 50%;

		.report_head {
			display: flex;
			flex-wrap: nowrap;
			text-align: center;
			align-items: center;
			justify-content: center; // padding: 20rpx 10rpx;
			height: 120rpx;
			margin: 10rpx 10rpx;
			font-size: 36rpx;

			.hx {
				width: 50rpx;
				display: block;
				height: 3rpx;
				margin: 0px 15rpx;
				font-size: 30rpx !important;
				background-color: #ffffff;
				line-height: 50rpx;
			}

			.head_img {
				display: block;
				height: 100rpx;
				width: 100rpx;
				color: white;

				image {
					background-image: none;
					max-width: 100rpx;
					max-height: 100rpx;
				}
			}
		}


	}

	.head_B {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: -23%;
		color: white;

	}

	.head_C {
		margin-top: 5% !important;
		width: 90%;
		height: 200rpx;
		background-color: #ffffff;
		margin: 0 auto;
		box-shadow: 1px 2px 4px #747474;
		border-bottom: 1px solid #7d7d7d;
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;


		.head_C_value {
			font-size: 34rpx;
			font-weight: bold;
		}

		.head_C_tile {
			font-size: 24rpx;
			text-align: center;
		}

		.A {
			background-color: red;
		}

		.B {
			background-color: pink !important;
		}
	}

	.propose {
		margin-top: 15rpx !important;
		width: 100%;
		// height: 300rpx;
		// background-color: pink;

		.propose_log {
			clear: both;

			image {
				display: block;
				width: 40rpx;
				height: 40rpx;
				float: left
			}

			text {
				// line-height: 80rpx;
			}
		}

		.propose_net {
			margin-top: 15rpx;
			width: 100%;
			padding: 30rpx;
			// height: 200rpx;
			height: auto;
			// background-color: yellow;
			text-indent: 2em;
			line-height: 52rpx;
			border: 1px solid #c8c8c8;
			overflow-x: scroll;

			text {
				text-indent: 2em;
				line-height: 52rpx;
			}
		}
	}
</style>
