
let wxAppIdType = false;
let domainType = 6//1是广中医-治未病、 2是贵阳、 3是灵山、 4是南宁中医院、 5是南宁七医院 、 6是广中医-慢病管理
import cofing from '@/formal.js'// 正式
// import cofing from '@/domain.js' //测试
import {getNoRead,getToken,getshuju} from '@/api/home.js'
import {msgCount} from '@/api/im.js'
cofing(domainType)
let {domain,socketUrl,tmplIds,tmplIdss,tmplIdsss,gzhImg,wxAppId,gzhName,webSrc,appName} = cofing(domainType)
// domain = 'http://192.168.1.74:8880';  //员工本机
// socketUrl = 'ws://192.168.1.74:9898/ws';
export default {
	wxAppIdType:false,//为了防止多次访问微信公众号授权页
	webSrc,
	gzhImg,
	gzhName,
	appName,
	domainType,//1是广中医 2是贵阳
	wxAppId,
	socketUrl,
	domain,
	systemlist:[],
	login: domain + '/wx/auth/login', //小程序登录接口
	postAuth: domain + '/wx/auth/postAuth', //小程序授权
	getToken: domain + '/wx/auth/token/', //获取token，参数openId
	medicationInfo: domain + '/zwb/medicationInfo/list',// 用药信息1不存在
	reportDetail: domain + '/zwb/contract/reportDetail',// 检查和检验信息1不存在
	// getDoctorList: domain + '/plague/user/getDoctorList', //咨询医生列表
	uploadFuJianFile: domain + '/oss/uniapp/upload', // 上传单个文件
	getWxOpenId: domain + '/wx/auth/getWxOpenId', //获取微信公众号openid
	updateWxOpenId: domain + '/wx/auth/updateWxOpenId', //保存微信公众号openid给数据库
	pregnancy: domain + '/wx/pregnancy', //经期设置1
	
	/* im板块接口 */
	friendList: domain + '/web/im/friendList', //好友列表 不存在
	friendAskList: domain + '/web/im/friendAskList', //好友请求列表 不存在
	friendAdd: domain + '/web/im/friendAdd', //好友添加 不存在
	friendAsktoDo: domain + '/web/im/friendAsktoDo', //好友添加处理 不存在
	sendMsg: domain + '/web/im/sendMsg', //发送消息
	setEndHh: domain + '/web/im/setEndHh', //结束聊天会话接口 不存在


	/**
	 * 请求封装
	 * @param {Object} res
	 * @param {Object} success
	 * @param {Object} fail
	 */
	RequestData: function(res, success, showLoading = true, fail, err) {
		var that = this;
		res.data = res.data ? res.data : {};
		let method = res.method ? res.method : "POST";
		let wxToken = uni.getStorageSync('token') ? uni.getStorageSync('token') : "";
		if(showLoading){
			uni.showLoading({
				title: "请求中..."
			})
		}
		var urldata={
			url: res.url,
			data: res.data,//http://127.0.0.1:29941/appservice/mainframe?t=1681093737813&cts=1681093737523,
			method: method
		};
		// uni.setStorageSync()
		uni.request({
			url: res.url,
			data: res.data,
			method: method,
			header: {
				"Content-Type": "application/json; charset=UTF-8",
				"Authorization": wxToken ? 'wx ' + wxToken : ""
			},
			// dataType: 'json',
			success: function(res) {
				uni.hideLoading();
				if (res.data.code == 0 || res.data.code == 200) {
					if(res != null){success(res.data);}
				} else if (res.data.code == 401) {
					that.getTokenInfo(urldata,success);
				} else {
					if (err) err();
					setTimeout(() => {
						that.msg(res.data.msg ? res.data.msg : res.data.msg,"none",3000);
					}, 500);
				}
			},
			fail: function(e) {
				uni.hideLoading();
				if (fail) fail();
			},
			complete: function() {
				// uni.hideLoading();
			}
		})
	},
	// 不需要token的请求封装
	RequestDataNo: function(res, success, fail) {
		var that = this;
		res.data = res.data ? res.data : {};
		let method = res.method ? res.method : "POST";
		// uni.showLoading({
		// 	title: "请求中..."
		// })
		uni.request({
			url: res.url,
			data: res.data,
			method: method,
			header: {
				"Content-Type": "application/json; charset=UTF-8",
			},
			success: function(res) {
				uni.hideLoading();
				if (res.data.code == 0 || res.data.code == 200) {
					success(res.data);
				} else {
					success(res.data);
					that.msg(res.data.msg ? res.data.msg : res.data.msg);
				}
			},
			fail: function(e) {
				uni.hideLoading();
				if (fail) fail();
			},
			complete: function() {
				// uni.hideLoading();
			}
		})
	},
	// 获取未读数
	getNoReads(data){
		console.log('日期入参==',data)
		var date = new Date();
		var yyyy=date.getFullYear();
		var M=date.getMonth() + 1;
		M=M<10?'0'+M:M;
		var d=date.getDate();
		d=d<10?'0'+d:d;
		var getNoReadData = yyyy+'-'+M+'-'+d
		if (data) {
			var getNoReadData = data
		}
		getNoRead({
			patientId:uni.getStorageSync("cardObj").patientId,
			date:getNoReadData
		}).then(res=>{
			console.log('未读数量=',res.data)
			let num = res.data;
			if(num <= 0){
				uni.removeTabBarBadge({index: 1})
			} else {
				uni.setTabBarBadge({
				  index: 1,
				  text: num + ""
				})
			}
		})
	},
	// 获取当前用户未读数
	getMsgNums: function(success,fali) {
		var that = this;
		if (uni.getStorageSync("userUid")) {
			msgCount({userUid: uni.getStorageSync("userUid")}).then(res=>{
				if (res.data <= 0) {
					fali(res.data)
				} else {
					success(res.data)
				}
			})
		}else{
			fali()
		}
	},
	// 获取token信息
	getTokenInfo: function(urldata,success){
		console.log('获取token')
		var openId = uni.getStorageSync("openId");
		var that=this;
		getToken({openId:openId}).then(res=>{
			if(res.code==200){
				uni.setStorageSync("token",res.data)
				getshuju(urldata).then(res=>{
					success(res)
				})
			}else{
				//无效数据清除重新进入页面判断是否注册
				uni.clearStorageSync()
				uni.reLaunch({
					url:'/pages/home/<USER>'
				})
			}
		})
	},

	// 获取角色信息
	role: function(){
		// var crowdType = uni.getStorageSync("cardObj").crowdType;
		// var crowdLabel = uni.getStorageSync("cardObj").crowdLabel;
		var crowdType = '2';
		var crowdLabel = 'a1d148f339f96248c3b976a3bd4c229a';
		if (crowdType != null && crowdLabel != null) {
			// var userRole = crowdType.charAt(0)//返回指定位置的字符 字符串中第一个字符的下标是 0
			//版本升级
			if (crowdType == '2' && crowdLabel.match('a1d148f339f96248c3b976a3bd4c229a') ) {
				uni.setTabBarItem({
				  "index": 2,
				  "text": '控糖',
				  "iconPath": 'static/img/zy_bar_woek.png',
				  "selectedIconPath": 'static/img/zy_bar_woekhl.png'
				})
			}
			else if(crowdType == '5' ){
				uni.setTabBarItem({
				  "index": 2,
				  "text": '乐高',
				  "iconPath": 'static/img/zy_health_data.png',
				  "selectedIconPath": 'static/img/zy_health_data_hl.png'
				})
			} else{
				uni.setTabBarItem({
				  "index": 2,
				  "text": '人群',
				  "iconPath": 'static/img/zy_mer_mmer.png',
				  "selectedIconPath": 'static/img/zy_mer_mmerhl.png'
				})
			}
		}else{
				uni.setTabBarItem({
				  "index": 2,
				  "text": '人群',
				  "iconPath": 'static/img/zy_mer_mmer.png',
				  "selectedIconPath": 'static/img/zy_mer_mmerhl.png'
				})
			}
	},

	// 判断是否登录
	isLogin: function() {
		if (uni.getStorageSync("token")) {
			return true;
		}
		return false;
	},
	// 判断登录和注册情况：用户微信是否注册在系统数据库中，已注册则自动登录，未注册提示注册
	isRegAndLogin: function(success){
		if(!uni.getStorageSync("token")){
			//没有登录信息 获取微信code登录测试
			let self = this;
			//获取小程序用户code用于登录
			uni.login({
				provider: 'weixin',
				success: function(loginRes) {
					console.log('loginRes', loginRes)
					self.code = loginRes.code
					//使用微信code登录
					self.RequestDataNo({
						url: self.login,
						data: {
							jsCode: loginRes.code
						},
					}, res => {
						if (res.code == 200) {
							uni.setStorageSync("openId", res.data.openId);

							if(res.data && res.data.user){
								//tag1:系统已存在用户流程
								uni.setStorageSync("token", res.data.token)
								uni.setStorageSync("user", res.data.user);
								success(2);//页面数据需要刷新
							}else {
								//tag2:新用户流程 - 提示授权微信登录
								success(0);
							}

						} else {
							self.$common.msg('授权异常，请刷新小程序重新进入');
						}
					});
				},
				fail:function(err) {
					console.log('我失败了',err)
				}
			});
		}else {
			if (domainType == 2) {
				//目前只有贵阳医院正式环境和星铭健康的测试环境有公众号的模板消息维护
				if (uni.getStorageSync("token") && uni.getStorageSync("user") && !uni.getStorageSync("user").wxOpenId) {
					success(0);
					return
				}
			}
			//已有登录信息
			success(1);
		}
	},
	// 授权获取订阅消息
	    openDyMsg: function(isShow = 0,MsgType) {
			var tmplIdData = []
			if (MsgType == 1) { //消息1
				tmplIdData = tmplIds
				var nowTime = uni.getStorageSync('nowTime')?uni.getStorageSync('nowTime'):''
				var nowdata = this.parseTime(new Date(),'{y}-{m}-{d}')
				if (nowTime == nowdata) {return}
			}
			if (MsgType == 2) { //消息2
				tmplIdData = tmplIdss
				var nowDay =uni.getStorageSync('nowDay')? uni.getStorageSync('nowDay'):''
				var nowDays = this.parseTime(new Date(),'{y}-{m}-{d}')
				if (nowDay == nowDays) {return}
			}
			if (MsgType == 3) { //消息3
				tmplIdData = tmplIdsss
				var nowThird = uni.getStorageSync('nowThird')?uni.getStorageSync('nowThird'):''
				var nowThirds = this.parseTime(new Date(),'{y}-{m}-{d}')
				if (nowThird == nowThirds) {return}
			}
			
			wx.requestSubscribeMessage({
			    tmplIds: tmplIdData,
			    success(res) {
					if (MsgType == 1) {uni.setStorageSync('nowTime',nowdata)}
					if (MsgType == 2) {uni.setStorageSync('nowDay',nowDays)}
					if (MsgType == 3) {uni.setStorageSync('nowThird',nowThirds)}
			        for (var key in res) {
			            if (key != 'errMsg') {
			                if (res[key] == 'reject') {
			                    // console.log('您已拒绝了订阅消息，如需重新订阅请前往设置打开。');
			                    if (isShow == 0) {
			                        return;
			                    }
			                    wx.showModal({
			                        title: '订阅消息',
			                        content: '您已拒绝了订阅消息，如需重新订阅请前往设置打开。',
			                        confirmText: '去设置',
			                        success: res => {
			                            if (res.confirm) {
			                                wx.openSetting({})
			                            }
			                        }
			                    })
			                    return
			                } else {
				
			                    wx.showToast({
			                        title: '订阅成功'
			                 })
			                }
			            }
			     }
			    },
			    fail(res) {
			        if (isShow == 0) {
			            return;
			        }
			        wx.showModal({
			            title: '订阅消息',
			            content: '您关闭了“接收订阅信息”，请前往设置打开！',
			            confirmText: '去设置',
			            showCancel: false,
			            success: res => {
			                if (res.confirm) {
			                    wx.openSetting({})
			                }
			            }
			        })
			    },
			})
	    },

	noLoginBox: function(){
		if (!uni.getStorageSync("token") || !uni.getStorageSync("user")) {
			console.log('调过来')
			uni.navigateTo({
				url: '/pages/login/index'
			})
		}
		if (domainType == 2) {
			//目前只有贵阳医院正式环境和星铭健康的测试环境有公众号的模板消息维护
			if (uni.getStorageSync("token") && uni.getStorageSync("user") && !uni.getStorageSync("user").wxOpenId && wxAppIdType == false) {
				wxAppIdType = true;
				uni.navigateTo({
					url: '/pages/login/gzhweb'
				})
			}
		}
	},

	//图片上传
	uploadFile: function(tempFilePath, success,fail,loadTitle) {
		let that = this;
		that.loadings(loadTitle);
		uni.uploadFile({
			url: that.uploadFuJianFile,
			filePath: tempFilePath,
			name: 'file',
			header: {
				"Content-Type": "application/json; charset=UTF-8",
				"Authorization": 'wx ' + uni.getStorageSync('token')
			},
			success: (res) => {
				var data = JSON.parse(res.data);
				if (data.code == 0 || data.code == 200) {
					success(data);
					// uni.hideLoading();
				} else {
					that.msg(data.msg);
				}
			},
			fail:(err)=>{
				fail(err)
			}
		});
	},

	// 文件上传2:商城专用
	uploadFileMall: function(success) {
		let that = this;
		that.loadings();
		uni.chooseImage({
			success: (chooseImageRes) => {
				const tempFilePaths = chooseImageRes.tempFilePaths;
				that.loadings();
				uni.uploadFile({
					url: that.upload,
					filePath: tempFilePaths[0],
					name: 'file',
					header: {
						"Authorization": uni.getStorageSync('token')
					},
					success: (res) => {
						var data = JSON.parse(res.data);
						if (data.code == 200) {
							success(data);
							uni.hideLoading();
						} else {
							that.msg(data.msg);
						}
					},
					complete: function() {
						uni.hideLoading();
					}
				});
			},
			fail: function() {
				uni.hideLoading();
			}
		});
	},
	/*儿童*/




	//是否为空判断
	isEmpty: function(obj) {
		if (obj == '') return true;
		if (obj == null) return true;
		if (obj == 'null') return true;
		if (obj === undefined) return true;
		return false;
	},

	// 打电话
	callPhone: function(phone){
		uni.makePhoneCall({
			phoneNumber:phone,
			success() {

			}
		})
	},

	//调整富文本图片大小
	adjustRichTextImageSize: function(text){
		if (text && text.length > 0) {
			var conet =text.replace(/\<img/gi, '<img style="width:100%;margin:auto;height:auto;"');
			return conet
		}
	},

	//提示
	msg: function(message, icon, duration) {
		if (!icon) icon = "none";
		if (!duration) duration = 3000;
		uni.showToast({
			title: message,
			icon: icon,
			duration: duration
		})
	},

	//模态窗
	model: function(title, content, success,confirmText) {
		uni.showModal({
			title: title ? title : '提示',
			content: content ? content : '这是一个模态弹窗',
			cancelText:"取消",
			success: function(res) {
				success(res);
			}
		});
	},

	//等待窗
	loadings: function(title) {
		uni.showLoading({
			title: title ? title : "加载中...",
		});
	},


	//保留当前页面，跳转到应用内的某个页面
	navTo: function(url) {
		uni.navigateTo({
			url: url
		});
	},

	//关闭当前页面，跳转到应用内的某个页面
	navCloseTo: function(url) {
		uni.redirectTo({
			url: url
		});
	},

	//跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面
	navTab: function(url) {
		uni.switchTab({
			url: url
		});
	},

	//关闭所有页面，打开到应用内的某个页面
	navLaunch: function(url) {
		uni.reLaunch({
			url: url
		});
	},

	//关闭当前页面，返回上一页面或多级页面
	navBack: function(index) {
		uni.navigateBack({
			delta: index
		});
	},

	// 日期格式化
	parseTime: function(time, pattern) {
		if (arguments.length === 0 || !time) {
			return null
		}
		const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
		let date
		if (typeof time === 'object') {
			date = time
		} else {
			if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
				time = parseInt(time)
			}
			if ((typeof time === 'number') && (time.toString().length === 10)) {
				time = time * 1000
			}
			date = new Date(time.substr(0, 19).replace(/T/g, ' ').replace(/-/g, '/'))
		}
		const formatObj = {
			y: date.getFullYear(),
			m: date.getMonth() + 1,
			d: date.getDate(),
			h: date.getHours(),
			i: date.getMinutes(),
			s: date.getSeconds(),
			a: date.getDay()
		}
		const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
			let value = formatObj[key]
			// Note: getDay() returns 0 on Sunday
			if (key === 'a') {
				return ['日', '一', '二', '三', '四', '五', '六'][value]
			}
			if (result.length > 0 && value < 10) {
				value = '0' + value
			}
			return value || 0
		})
		return time_str
	},
	/** H5压缩图片方法,  APP环境无效！！！！
		* @param {Object} imgSrc 图片url
		* @param {Object} callback 回调设置返回值
	**/
	translateImg:function(imgSrc, callback) {
		var thats = this;
		var img = new Image();
		img.src = imgSrc;
		img.onload = function() {
				var that = this;
				var h = that.height;
				// 默认按比例压缩
				var w = that.width;
				var canvas = document.createElement('canvas');
				var ctx = canvas.getContext('2d');
				var anw = document.createAttribute("width");
				anw.nodeValue = w;
				var anh = document.createAttribute("height");
				anh.nodeValue = h;
				canvas.setAttributeNode(anw);
				canvas.setAttributeNode(anh);
				ctx.drawImage(that, 0, 0, w, h);
				//压缩比例
				var quality = 0.1;
				var base64 = canvas.toDataURL('image/jpeg', quality);
				canvas = null;
				var blob=thats.base64ToBlob(base64);
				// console.log(blob)
				//Blob对象转blob地址
				var blobUrl=window.URL.createObjectURL(blob);
				callback(blobUrl);
		}
	},

}

