<template>
	<view class="imgCard">
		<video v-for="(item,i) in list" v-if="i == index" :key='index' :ref="'video'+index"  class="imgOrVideo" @timeupdate="timeupdate"  @play="playVideo" :src="list[index]"  controls :show-center-play-btn="true" auto-pause-if-navigate></video>
		<view style="margin-top: 40px;" v-for="item in [0,1]" @click="itemClick(item)">{{item}}</view>
	</view>
</template>
<script>
export default {
	data() {
		return {
			index:0,
			videoContent:null,
			show:false,
			list:['http://qiniu.starup.net.cn/586656442-1-208.mp4','http://qiniu.starup.net.cn/586656442-1-208.mp4']
		}
	},
	onLoad: function() {
		
	},
	methods: {
		timeupdate(e){
			if(e.detail.currentTime>5 && this.show == false){
				this.show = true
			}
		},
		itemClick(item){
			this.index = item
		},
		playVideo(){
			

			
		}
	}
}
</script>
<style>
</style>