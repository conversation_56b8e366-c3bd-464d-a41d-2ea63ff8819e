<template>
  <view>
    <view style=" margin-top:0rpx; ">
      <view class=" gui-bg-gray demo-nav2">
        <gui-switch-navigation :items="tabs" @change="navchange" textAlign="center" :isCenter="true"
                               activeDirection="center" activeLineBg="linear-gradient(to right, #ED6A0C, #ED6A0C)"
                               activeColor="#ED6A0C" activeLineHeight="4rpx" lineHeight="72rpx" activeLineWidth="66rpx">
        </gui-switch-navigation>
      </view>
    </view>
    <view style="height:20vh;"></view>
    <view class="gui-margin-top" style="text-align: center;">
      <text class="gui-h6 gui-color-gray"> {{ fromData.bloodSugar || 0 }}{{ ' ' }}mmol/L</text>
    </view>
    <view style="height:2vh;"></view>
    <view class=" pl-20 pr-20">
      <!-- 注意 max 属性的值应该大于等于 maxDefault 属性的值  decimal显示小数点  vleft 左边滑块显示 -->
      <gui-interval-slider :width="690" :height="30" :min="0.0" :max="35.0" :decimal="true" :vleft="false"
                           @change="change1"
                           bgColor="#dedede" activeColor="#02ded7" :minDefault="bloodSugar[0]"
                           :maxDefault="fromData.bloodSugar || bloodSugar[1]"/>
    </view>
    <view class="gui-margin-top" style="text-align: center;" v-if="monitorInfo && monitorInfo.length">
      <text class="gui-h6 gui-color-green"> 测量血糖标准：{{ standard }}</text>
    </view>
    <view style="height:80rpx;"></view>
    <view class="pl-20 pr-20" style="height: 100%; flex-direction: column;">
      <form @submit="submit">
        <view class="gui-form-item gui-border-b">
          <text class="gui-form-label">测量时间</text>
          <view class="gui-form-body">
			  <!-- @confirm="confirm4" -->
            <gui-datetime  :value="fromData.timeBegin" ref="timeBegin">
              <text class="demo  gui-color-gray gui-icons gui-color-white">{{ fromData.timeBegin }}</text>
            </gui-datetime>
          </view>
        </view>
        <view class="gui-form-item gui-border-b">
          <text class="gui-form-label">胰岛素(U)</text>
          <view class="gui-form-body">
            <input type="text" class="gui-form-input" v-model="fromData.insulinVolume" name="insulinVolume"
                   placeholder="请输入胰岛素量"/>
          </view>
        </view>
        <view class="gui-form-item gui-border-b">
          <text class="gui-form-label">添加备注</text>
          <view class="gui-form-body">
            <input type="text" class="gui-form-input" v-model="fromData.remark" name="remark"
                   placeholder="如有特殊情况 请备注"/>
          </view>
        </view>
        <view class="gui-margin-top-large gui-flex gui-rows gui-nowrap gui-justify-content-center">
          <button type="default" class="gui-button" style="width:200rpx;background-color: #C59F79;" formType="submit">
            <text class="gui-icons gui-color-white gui-button-text">保 存</text>
          </button>
        </view>
        <view style="height:80rpx;"></view>
      </form>
    </view>

  </view>
</template>

<script>
	import {addBloodGlucoseRecord,getBloodGlucoseRecord,getBloodGlucoseMonitorInfo} from '@/api/children.js'
export default {
  data() {
    return {
      currentIndex: 0,
      tabs: [
        {
          id: 0,
          dictLabel: '晨起'
        },
        {
          id: 1,
          dictLabel: '早餐后'
        },
        {
          id: 2,
          dictLabel: '上午'
        },
        {
          id: 3,
          dictLabel: '午餐前'
        },
        {
          id: 4,
          dictLabel: '午餐后'
        },
        {
          id: 5,
          dictLabel: '下午'
        },
        {
          id: 6,
          dictLabel: '晚餐前'
        },
        {
          id: 7,
          dictLabel: '晚餐后'
        },
        {
          id: 8,
          dictLabel: '睡前'
        }
      ],
      bloodSugar: [0, 6.5],
      fromData: {
        insulinVolume: '', //胰岛素
        timeBegin: '', //日期
        recordType: undefined, //记录类型
        remark: '', //备注
        bloodSugar: 6.5//血糖值
      },
      // 测量标准
      standard: '4.0~7.0',
      // progress2 : '0/100',
      demo2Val: '2022-10-19 16:38',
      // 血糖预警信息
      monitorInfo: []
    }
  },
  onLoad() {
    this.loadData(1)
    this.fromData.timeBegin = this.$common.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');
  },
  methods: {
    change1: function (e) {
      this.fromData.bloodSugar = e[1]
      this.$forceUpdate()
    },
    navchange: function (index) {
      this.currentIndex = index;
      this.loadData(index + 1)
    },
    swiperChange: function (e) {
      this.currentIndex = e.detail.current;
    },
    loadMonitorInfo() {
		getBloodGlucoseMonitorInfo().then(res=>{
			this.monitorInfo = res.data;
			const currentIndex = this.currentIndex + 1;
			let monitor;
			if (currentIndex === 1 || currentIndex === 4 || currentIndex === 7 || currentIndex === 9) {
			  monitor = this.monitorInfo.find(item => item.recordType === 1)
			} else if (currentIndex === 2 || currentIndex === 5 || currentIndex === 8) {
			  monitor = this.monitorInfo.find(item => item.recordType === 2)
			}
			if (monitor) {
			  this.$set(this, 'standard', `${monitor.start}~${monitor.end}`)
			} else {
			  monitor = this.monitorInfo.find(item => item.recordType === 10)
			  this.$set(this, 'standard', `${monitor.start}~${monitor.end}`)
			}
		})
    },
    loadData(index) {
		getBloodGlucoseRecord({
			recordType: index,
			imei: uni.getStorageSync("cardObj")?.idCard
		}).then(res=>{
			if (res.data) {
			  this.fromData = res.data
			  this.loadMonitorInfo()
			  if (!this.fromData.insulinVolume) {
			    this.fromData.insulinVolume = 0
			  }
			} else {
			  this.fromData = {
			    insulinVolume: '', //胰岛素
			    timeBegin: this.computingTime(), //日期
			    recordType: undefined, //记录类型
			    remark: '', //备注
			    bloodSugar: 6.5//血糖值
			  }
			}
		})
    },
    computingTime() {
      this.loadMonitorInfo()
      switch (this.currentIndex + 1) {
        case 1:
          return `${this.$common.parseTime(new Date(), '{y}-{m}-{d}')} 06:00:00`
        case 2:
          return `${this.$common.parseTime(new Date(), '{y}-{m}-{d}')} 08:00:00`
        case 3:
          return `${this.$common.parseTime(new Date(), '{y}-{m}-{d}')} 11:00:00`
        case 4:
          return `${this.$common.parseTime(new Date(), '{y}-{m}-{d}')} 12:00:00`
        case 5:
          return `${this.$common.parseTime(new Date(), '{y}-{m}-{d}')} 14:00:00`
        case 6:
          return `${this.$common.parseTime(new Date(), '{y}-{m}-{d}')} 15:00:00`
        case 7:
          return `${this.$common.parseTime(new Date(), '{y}-{m}-{d}')} 18:00:00`
        case 8:
          return `${this.$common.parseTime(new Date(), '{y}-{m}-{d}')} 20:00:00`
        case 9:
          return `${this.$common.parseTime(new Date(), '{y}-{m}-{d}')} 21:00:00`
      }
      this.$forceUpdate()
    },
    submit: function () {
		var that = this
      uni.showModal({
        title: '提示',
        content: '确认保存血糖信息吗？',
        cancelText: "取消",
        confirmText: "保存",
        confirmColor: '#576B95',
        cancelColor: '#000000',
        success: res => {
          if (res.confirm) {
            const fromData = JSON.parse(JSON.stringify(this.fromData))
            fromData.recordType = this.currentIndex + 1
            fromData.imei = uni.getStorageSync("cardObj")?.idCard || ''
            fromData.patientId = uni.getStorageSync("cardObj")?.patientId || ''
			addBloodGlucoseRecord(fromData).then(res=>{
				if (res.data) {
				  this.loadData(this.currentIndex + 1)
				  this.$common.msg("添加成功", "success")
				} else {
				  this.$common.msg(res.msg)
				}
				setTimeout(() => {
					uni.$emit('update-health-data')
					that.$common.navBack(1)
				}, 1000);
			})
          }
        }
      })
    },
    confirm4: function (res) {
      this.fromData.timeBegin = `${res[0]}-${res[1]}-${res[2]} ${res[3]}:${res[4]}:${res[5]}`;
    }
  }
}
</script>

<style scoped>
.gui-block-text {
  display: block;
  font-size: 30rpx !important;
}
</style>
