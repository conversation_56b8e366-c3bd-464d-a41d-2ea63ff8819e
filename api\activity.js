import Http from "../net/Http.js"
import noHttp from "../net/noHttp.js"

// 活动专区 - 查询所有活动
export function activityList(data){
	return Http.get('/wx/activity/list',data)
}

// 活动专区 - 查询活动详情
export function activityDetail(data,obj){
	return Http.get('/wx/activity/'+data,obj)
}

// 活动专区 - 查询活动详情 无token
export function activityDetailNo(data){
	return noHttp.get('/wx/activityOpen/'+data)
}

// 活动专区 - 报名活动
export function addActivityByPatient(data){
	return Http.post('/wx/activity/addActivityByPatient',data)
}
// 活动专区-转发 /wx/activity/share/{id}
export function activityshare(data){
	return Http.get('/wx/activity/share/'+data)
}