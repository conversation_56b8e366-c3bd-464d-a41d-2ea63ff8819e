<template>
  <view>
    <view class="gui-margin-top" v-if="status">
      <view class="my-list" v-if="products.length > 0">
        <navigator class="gui-list-items" v-for="(item,index) in products" :key="index" @click="infoData(item)">
          <image class="gui-list-icon gui-icons gui-color-red item_img" :src="item.image" mode=""></image>
          <view class="gui-list-body gui-border-b">
            <view class="gui-list-title">
              <text class="gui-list-title-text gui-primary-color gui-ellipsis gui-h6 "
                    style="width:400rpx; height:60rpx;">
                {{ item.name || '' }}
              </text>
            </view>
            <text class="gui-list-body-desc gui-color-gray" style="font-size: 28rpx;">
              {{ item.carbohydrate || 0 }}碳水 {{ item.calorie || 0 }}千卡/100g
            </text>
          </view>
          <text class="gui-list-arrow-right gui-icons gui-color-gray-light">&#xe601;</text>
        </navigator>
      </view>
    </view>
    <view v-else>
      <gui-empty>
        <view slot="img" class="gui-flex gui-rows gui-justify-content-center">
          <image class="gui-empty-img" src="../../static/kong.png"></image>
        </view>
        <text slot="text"
              class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#9DABFF;">
          暂无数据 ......
        </text>
      </gui-empty>
    </view>
  </view>
</template>

<script>
	import {sugarControlList} from "@/api/diabetes"
export default {
  data() {
    return {
      products: [],
      status: false
    }
  },
  onLoad(option) {
    uni.setNavigationBarTitle({
      title: option.name
    })
	sugarControlList({
		controlId: option.id
	}).then(res=>{
		this.products = res.rows
		if (JSON.stringify(this.products) === '[]') {
		  this.status = false
		} else {
		  this.status = true
		}
	})
  },
  methods: {
    infoData(item) {
      this.$common.navTo(`../diabetes/info?id=${encodeURIComponent(item.id)}`);
    }
  }
}
</script>
<style lang="less">
.gui-list-arrow-right {
  padding-right: 24rpx;
  color: #000 !important;
}

.gui-list-items {
  padding: 0rpx 10rpx !important;
  // border-bottom: 1px solid #a7a7a7;
}

.item_img {
  display: block;

}

.classification {
  .cationInfo {
    .cationInfo_item {
      width: 100%;
      height: 150rpx;
      border: 1px solid red;

      // display: flex;
      // justify-content: center;
      // flex-direction: column;
      .itme_image {
        image {
          display: block;
          // align-items: center;
          width: 100rpx;
          max-width: 100rpx;
          max-height: 110rpx;
          height: 110rpx;
          margin: 0rpx 10rpx;
        }
      }

      .itme_title {
      }

      .itme_tips {
      }
    }
  }
}
</style>
