<template>
	<view class="gui-padding">
		<view class="gui-margin-top-large"></view>
		<!-- <view class="gui-form-item gui-flex gui-rows gui-space-between">
			<text class="gui-form-label" style="width: 300rpx;">开通订阅消息功能</text>
			<switch :checked="formData.defaultFlag==1?true:false" name="defaultFlag" @change="switchChange"
				color="#C59F79" />
		</view> -->
		<view class="gui-margin-top">
			<view class=""> 订阅消息说明</view>
			<text class="gui-h6 gui-color-gray">当订阅次数为 0 时，无法接受到以下该类消息。</text></br>
			<text class="gui-h6 gui-color-gray">当订阅次数少于 5 时，以红色提醒。</text>
		</view>
		<view class="gui-margin-top gui-flex gui-space-between gui-align-items-center">
			<view>
				<text class="gui-list-title-text">健康监测提醒</text></br>
				<text class="gui-list-title-text fs-20 gui-color-red">共订阅 3 次</text>
			</view>
			<view @click="openDy">
				<text class="gui-color-white fs-20 gui-bg-green px-30 b-radius-10 py-20">订阅 +1</text>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 密码可视 类型切换
				isPwd: true,
			}
		},
		methods: {
			openDy(){
				this.$common.openDyMsg();
			},
			// switch 开关
			switchChange: function(e) {
				this.formData.defaultFlag = this.formData.defaultFlag==1?0:1;
			},
		}
	}
</script>
<style scoped>
	.demo{width:210rpx; margin:10rpx;}
	.button{width:90rpx; height:90rpx; line-height:90rpx; border-radius:8rpx; margin:10rpx;}
	.button-text{font-size:38rpx; text-align:center;}
</style>
