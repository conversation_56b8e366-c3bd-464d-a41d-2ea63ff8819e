import Http from "../net/Http.js"
import noHttp from "../net/noHttp.js"

// 已读未读
export function isReadMsgI(data){
	return Http.post('/web/im/isReadMsg',data)
}
// 获取会话列表
export function mySessionList(data){
	return Http.post('/wx/im/mySessionList',data)
}
// 获取未读消息总数
export function msgCount(data){
	return Http.post('/web/im/msgCount',data)
}
// 标记已读
export function msgReaded(data){
	return Http.post('/web/im/msgReaded',data)
}
// 会话消息历史记录
export function sessionRecord(data){
	return Http.post('/web/im/sessionRecord',data)
}
// 获取用户的im用户信息
export function getImUser(data){
	return Http.post('/web/im/getMiniImUser',data)
}

// 用户撤回消息
export function revokeMessage(data){
	return Http.post('/web/im/revokeMessage',data)
}