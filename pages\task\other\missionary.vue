<template>
	<view>
		<view v-if="obj.taskInfo.status === -1">
			<view style="padding:50rpx 0;">
				<text slot="text"class="gui-block-text gui-text-center gui-margin-top text-zhuti" >此内容已经被撤回...</text>
			</view>
		</view>
		<view v-else>
			<!-- 视频播放区域 -->
			<view v-show="obj.articleInfo.videoUrl">
				<video id="myVideo" 
				class="gui-course-video" 
				:src="obj.articleInfo.videoUrl" 
				:poster="obj.articleInfo.filePath" controls></video>
			</view>

			<view v-if="obj.articleInfo.content && obj.articleInfo.articleType != 3">
				<view class="gui-h3 fs-36 font-bold p-20">{{obj.articleInfo.articleName || '-'}}</view>
				<view class="d-flex jc-between px-20 fs-30 text-grey-74 my-15">
					<view>下发人：{{obj.taskInfo.createBy || '-'}}</view>
					<view>下发时间：{{obj.taskInfo.createTime || '-'}}</view>
				</view>
				
				<view v-if="obj.articleInfo.content && obj.articleInfo.articleType != 3" class="p-20" style="line-height: 50rpx;text-align: justify;word-wrap:break-word">
					<!-- <u-parse :content="$common.adjustRichTextImageSize(obj.articleInfo.content)"></u-parse> -->
					<mp-html :content="obj.articleInfo.content" />
				</view>
			</view>
			<view v-if="obj.articleInfo.content && obj.articleInfo.articleType == 3">
				<web-view :webview-styles="webviewStyles" :src="obj.articleInfo.content.replace(/<[^>]+>/g, '')"></web-view>
			</view>
			<!-- <view @click="waibulian" v-if="obj.articleInfo.content && obj.articleInfo.articleType != 2" class="p-20" style="line-height: 50rpx;text-align: justify;word-wrap:break-word" v-html="$common.adjustRichTextImageSize(obj.articleInfo.content)"></view> -->
			<!-- <view @click="waibulian"  class="p-20" style="line-height: 50rpx;text-align: justify;word-wrap:break-word;color: #0081FF;" > 外部链接，可点击查看详情</view> -->
			
			<!-- <view class="mb-40 mx-20 p-20" style="background-color: #F9F9FB;">2023-5-16上午，韦总说宣教文章只需查看增加阅读量
				<view style="color: #9AA7B9;" class="fs-30 mt-15">反馈情况</view>
				<view class="d-flex ai-center jc-around">
					<view @click="change(item.value)" :class="type == item.value ? 'blj-box2' : 'blj-box'" v-for="(item,index) in fedList" :key="index">
						<image :src="type == item.value ? item.img2 : item.img" mode="widthFix" class="w70 h70 mb-15"></image>
						<view :class="type == item.value ? 'text-white' : 'text-black'">{{item.options}}</view>
					</view>
				</view>
				<view v-if="type == 3" class="w-100 box-size-border pos-relative bg-white">
					<textarea v-model="reson" :disabled="obj.taskInfo.status != 0" placeholder="请输入不理解原因" maxlength="200" class="box-size-border w-100 p-20 fs-30" placeholder-class="fs-28" />
					<view class="pos-absolute text-grey-b2" style="bottom: 15rpx;right: 30rpx;">{{reson.length}}/200</view>
				</view>
				医生回复
				<view v-if="obj.replyDoubt.createBy">
					<view class="text-zhuti mt-40 px-20" style="font-weight: bold;">{{obj.replyDoubt.createBy}}回复：{{obj.replyDoubt.content}}</view>
					<view class="text-zhuti fs-30 px-20 mt-20" style="font-weight: bold;">回复时间：{{obj.replyDoubt.createTime}}</view>
				</view>
			</view>
			<view style="height: 180rpx;"></view>
			<view v-if="obj.taskInfo.status == 0" class="bg-white w-100 pos-fixed box-size-border box-shadows py-20 px-40 gui-border-radius-small" style="height: 130rpx; bottom: 0;left: 0;z-index: 9;">
				<view @click="save" class="bg-zhuti text-white w-100 h-100 d-flex ai-center jc-center">提交反馈</view>
			</view> -->
		</view>
	</view>
</template>

<script>
	import uParse from '@/components/u-parse/u-parse.vue'
	import {getArticle} from '@/api/task.js'
	import {getFollowTaskInfo,readFollowTaskAirtcle} from '@/api/task.js'
	export default {
		components:{uParse},
		data() {
			return {
				id:"",
				taskType:'',
				type:"",
				reson:"",
				obj:{
					articleInfo:{},
					replyDoubt:{},
					taskInfo:{},
				},
				textareaVal: '', // 文本内容
				maxWords: 50, // 最多字符数
				textareatimer: null, // 延迟记录
				fedList: [{
						options: '完全理解',
						img: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_ok.png',
						img2: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_okw.png',
						value:"1"
					},
					{
						options: '基本理解',
						img: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_lit.png',
						img2: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_litw.png',
						value:"2"
					},
					{
						options: '不理解',
						img: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_qu.png',
						img2: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_quw.png',
						value:"3"
					},
				],
				fedIndex: '',
			}
		},
		onLoad(options) {
			this.id = options.id;
			this.taskType = options.taskType
			this.getDetail();
		},
		methods: {
			getIntegral(){//文章阅读量的接口
				getArticle({
					id:this.id,
					patientId:uni.getStorageSync('cardObj').patientId,
					status:''
				}).then(res=>{
					// this.model.readNum = res.data.readNum
				})
			},
			getDetail(){
				getFollowTaskInfo({
					id:this.id,
					taskType:this.taskType
				}).then(res=>{
					this.obj = res.data
					this.type = res.data.taskInfo.isUnderstand ? res.data.taskInfo.isUnderstand : ""
					this.reson = res.data.taskInfo.incomReason ? res.data.taskInfo.incomReason : ""
					this.save();
					this.getIntegral();
				})
			},
			change(value){
				if(this.obj.taskInfo.status != 0 ) {return}
				this.type = value;
			},
			save() {
				// if (!this.type) { return this.$common.msg("请选择反馈信息") }
				// if (this.type == 3 && !this.reson) { return this.$common.msg("请输入不理解的原因") }
				readFollowTaskAirtcle({
					patientId:uni.getStorageSync('user').id, // 系统用户id
					articleType:this.obj.articleInfo.articleType,
					id:this.id,
					taskType:this.taskType,
					isUnderstand:'1',
					incomReason:this.reson
				}).then(res=>{
					// this.$common.msg("反馈成功","success")
					// setTimeout(()=>{
					// 	this.$common.navTab('/pages/task/index')
					// 	// this.getDetail();
					// },1000)
				})
			},
			 delHtmlTag (str) {
			       return str.replace(/<[^>]+>/g, '').replace(/&nbsp;/ig, "")
			      },

			waibulian(){
				var aboutus = this.obj.articleInfo.content.replace(/<[^>]+>/g, '')
				uni.setStorageSync('aboutus',aboutus);
				setTimeout(()=>{
					this.$common.navTo('/pages/task/other/aboutus')
				},300)
			}
		}
	}
</script>

<style scoped>
	.gui-course-video{width:750rpx;}
	.modal-btns {
		line-height: 88rpx;
		font-size: 26rpx;
		text-align: center;
		width: 200rpx;
	}

	.submit {
		width: 50%;
		line-height: 70rpx;
		position: fixed;
		bottom: 10rpx;
		left: 25%;
	}
	
	.blj-box{
		flex: 1;
		text-align: center;
		background-color: #fff;
		border-radius: 10rpx;
		padding: 30rpx 0;
		margin:30rpx 10rpx;
	}
	
	.blj-box2{
		flex: 1;
		text-align: center;
		background-color: #8FC17B;
		border-radius: 10rpx;
		padding: 30rpx 0;
		margin:30rpx 10rpx;
	}
</style>
