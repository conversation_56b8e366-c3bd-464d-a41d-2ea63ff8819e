<template>
	<view>
		<view class="gui-grids gui-flex gui-rows gui-wrap demo-nav2 pb-20">
			<gui-segmented-control ref="tabRef" style="width: 95%;margin: auto;" :items="tabs" @change="navchange"></gui-segmented-control>
		</view>
		<view>
			<view v-if="current == 0" class="pb-30">
				<lx-calendar :status="statusTime" v-if="current == 0" v-model="date" @change="changeDate"></lx-calendar>
				<view>
					<view v-for="(item,index) in list" :key="index" @tap="detail(item)" class="mb-40" @change="changelist">
						<view class="d-flex ai-center mx-30 mt-20" v-if="item.taskType!= 4">
							<view class="mr-20"><gui-tags :margin="0" :text="current == 0 ? $common.parseTime(item.createTime,'{h}:{i}') : $common.parseTime(item.createTime,'{m}-{d} {h}:{i}')" :size="22" bgClass="gui-bg-white" color="#9AA7B9" borderColor="#9AA7B9"></gui-tags></view>
							<text v-if="item.taskType == 3" style="color: #9AA7B9;font-size: 28rpx;">监测</text>
							<text v-if="item.taskType == 2" style="color: #9AA7B9;font-size: 28rpx;">宣教</text>
							<text v-if="item.taskType == 1" style="color: #9AA7B9;font-size: 28rpx;">问卷</text>
							<!-- <text v-if="item.taskType == 4" style="color: #9AA7B9;font-size: 28rpx;">康养</text> -->
							<text v-if="item.taskType == 5" style="color: #9AA7B9;font-size: 28rpx;">膏方</text>
						</view>
						<view class="d-flex ai-center jc-between p-30 pos-relative m-30" style="background-color: #F5F6F8;" v-if="item.taskType!= 4">
							<image src="https://img.starup.net.cn/xmkj/zwb/img/zy_jkjy_jil.png" mode="widthFix" class="w50 h50"></image>
							<text class="ml-20 ellipsis-1 flex-1 fs-28 font-bold">{{item.questionnaireName=="血糖"?item.timePoint:''}}{{item.questionnaireName}}</text>
							<gui-tags v-if="item.status == 0" :margin="0" text="待执行" :size="26" bgClass="bg-red2"></gui-tags>
							<gui-tags v-if="item.status == 1" :margin="0" text="已完成" :size="26" bgClass="gui-bg-green"></gui-tags>
							<gui-tags v-if="item.status == 2" :margin="0" text="已中止" :size="26" bgClass="bg-grey-74"></gui-tags>
							<gui-tags v-if="item.status == 3" :margin="0" text="已过期" :size="26" bgClass="bg-grey-74"></gui-tags>
							<gui-tags v-if="item.status == -1" :margin="0" text="已撤回" :size="26" bgClass="bg-grey-74"></gui-tags>
							<text v-if="item.isRead == 0" class="gui-badge-point"></text>
							<!-- <text v-if="item.isRead == 0" class="gui-badge bg-red2 text-white text-center pos-absolute" style="top: 0;right: 0;">1</text> -->
						</view>
					</view>
					<view v-if="list.length<= 0">
						<gui-empty>
							<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
								<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
							</view>
							<text slot="text"
							class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
						</gui-empty>
					</view>
				</view>
			</view>
			<view v-else class="gui-bg-gray demo-nav2 mt-20">
				<!-- 已完成 -->
				<gui-switch-navigation :items="navItems" @change="changenav" :currentIndex="currentIndex"
				textAlign="center" activeDirection="center"  padding="30rpx" :size="0" :margin="8"
				activeLineBg="linear-gradient(to right, #C59F79, #C59F79)" activeColor="#C59F79"
				activeLineHeight="4rpx" lineHeight="72rpx" activeLineWidth="80rpx"></gui-switch-navigation>
				<view class="">
					<swiper :current="currentIndex" @change="swiperChange"
					:style="{height:mainHeight+'px', width:'750rpx'}">
					<!-- 轮播项目数量对应 上面的选项标签 -->
						<swiper-item>
							<scroll-view :scroll-y="true":class="domainType ==1?'':'gui-bg-white'" :style="{height:mainHeight+'px'}">
								<view class="gui-flex" style="min-height: 78vh;">
									<view style="min-height: 78vh;width: 120rpx; background-color: #F5F6F8;text-align: center;" class="h-100 gui-flex gui-columns gui-align-items-center">
										<view class="w-100 p-10 py-15" @click="toJiance(10)" :class="toJianceIndex==10?'gui-bg-white gui-bold':''">舌面象</view>
										<view class="w-100 p-10" @click="toJiance(11)" :class="toJianceIndex==11?'gui-bg-white gui-bold':'text-grey-74'">体 重<br/>管 理</view>
									</view>
									<view class="h-100 ml-20">
										<!-- 舌面象 -->
										<view v-if="toJianceIndex==10" style="width: 600rpx;">
											<view style="min-height: 78vh;" v-if="healthData.lingualFacies.length > 0"  class="gui-bg-white" :class="domainType ==1?'top-radius-40 pb-10 pt-40':'pt-20'">
												<view class="gui-flex px-40 " v-for="(item,index) in healthData.lingualFacies" :key="index" >
													<view class="time-line">
														<view v-if="domainType !=1" class="l-icon"></view>
														<view v-if="domainType ==1" class="gui-absolute">
															<text class="gui-icons fs-36">&#xe64c;</text>
														</view>
														<view class="time-line-i"></view>
													</view>
													<view style="flex: 1;padding-left: 30rpx;height: 110rpx;" @tap="changeTo(item)">
														<view class="l-time visitDate-row" >
															<view>
																{{$common.parseTime(item.timeBegin,'{y}-{m}-{d} {h}:{i}')}}
																<span class=" ml-20 fs-34">舌面</span>
															</view>
															<view class="gui-accordion-title gui-flex gui-rows gui-nowrap gui-align-items-center"
																>
																<text class="fs-34" :class="domainType ==1?'text-zhuti-zyy':'gui-color-blue'">详情</text>
																<text class="gui-accordion-icon gui-block-text gui-icons gui-color-gray gui-text-right"
																	>&#xe601;</text>
															</view>
														</view>
													</view>
												</view>
											</view>
											<view v-if="healthData.lingualFacies.length <= 0">
												<gui-empty>
													<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
														<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
													</view>
													<text slot="text"
													class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
												</gui-empty>
											</view>
										</view>
										<!-- 体重管理 -->
										<view v-if="toJianceIndex==11" style="width: 600rpx;">
											<view v-if="weightTable.length > 0">
												<view class="gui-border-l mt-20 w-100 gui-flex">
													<view class="w-20 gui-border-t">
														<view class="h80 line-h80 gui-text-center gui-border-r gui-border-b ellipsis-1 text-zhuti"> 记录时间 </view>
														<view class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1 text-zhuti"> 身高(cm) </view>
														<view class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1 text-zhuti"> 体重(kg) </view>
														<view v-if="weightConfig.includes('1')" class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1 text-zhuti"> 腰围(cm) </view>
														<view v-if="weightConfig.includes('2')" class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1 text-zhuti"> 腹围(cm) </view>
														<view v-if="weightConfig.includes('3')" class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1 text-zhuti"> 臀围(cm) </view>
														<view class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1 text-zhuti"> BMI </view>
														<view v-if="weightConfig.includes('4')" class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1 text-zhuti"> 腰臀比 </view>
														<view v-if="weightConfig.includes('5')" class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1 text-zhuti"> PBF(%) </view>
														<view v-if="weightConfig.includes('6')" class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1 text-zhuti"> 内脏脂肪指数(cm²) </view>
													</view>
													<view style="max-width: 60%;min-width: 20%;" class="gui-border-t">
														<scroll-view scroll-x="true" style="max-width: 400rpx;">
															<view class="gui-flex">
																<view class="" v-for="(item, index) in weightTable" :key="index">
																	<view style="min-width: 120rpx;" class="px-10 h80 gui-text-center gui-border-r gui-border-b text-zhuti">
																		<text>{{$common.parseTime(item.measureTime,'{y}-') || '-'}}</text><br/>
																		<text>{{$common.parseTime(item.measureTime,'{m}-{d}') || '-'}}</text>
																	</view>
																	<view class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1">{{item.height || '0'}}</view>
																	<view class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1">{{item.weight || '0'}}</view>
																	<view v-if="weightConfig.includes('1')" class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1">{{item.waistline || '0'}}</view>
																	<view v-if="weightConfig.includes('2')" class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1">{{item.abdomen || '0'}}</view>
																	<view v-if="weightConfig.includes('3')" class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1">{{item.hipline || '0'}}</view>
																	<view class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1">{{item.BMI || '0'}}</view>
																	<view v-if="weightConfig.includes('4')" class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1">{{item.waisHipRatio || '0'}}</view>
																	<view v-if="weightConfig.includes('5')" class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1">{{item.pbf || '0'}}</view>
																	<view v-if="weightConfig.includes('6')" class="h50 line-h50 gui-text-center gui-border-r gui-border-b ellipsis-1">{{item.visceralFatIndex || '0'}}</view>
																</view>
															</view>
														</scroll-view>
													</view>
													<view class="w-20 gui-border-t">
														<view class="h80 line-h80 gui-text-center gui-border-r gui-border-b ellipsis-1 text-zhuti">变化</view>
														<view class="h50 gui-text-center gui-border-r gui-border-b ellipsis-1">
															{{weightTable.length>1?getSummaries(Number(weightTable[0].height),Number(weightTable[weightTable.length-1].height),1):'0'}}
															<text class="pl-5 gui-color-green" v-if="getSummaries(Number(weightTable[0].height),Number(weightTable[weightTable.length-1].height),2) === '下'" >↓</text>
															<text class="pl-5 gui-color-red" v-if="getSummaries(Number(weightTable[0].height),Number(weightTable[weightTable.length-1].height),2) === '上'">↑</text>
														</view>
														<view class="h50 gui-text-center gui-border-r gui-border-b ellipsis-1">
															{{weightTable.length>1?getSummaries(Number(weightTable[0].weight),Number(weightTable[weightTable.length-1].weight),1):'0'}}
															<text class="pl-5 gui-color-green" v-if="getSummaries(Number(weightTable[0].weight),Number(weightTable[weightTable.length-1].weight),2) === '下'" >↓</text>
															<text class="pl-5 gui-color-red" v-if="getSummaries(Number(weightTable[0].weight),Number(weightTable[weightTable.length-1].weight),2) === '上'">↑</text>
														</view>
														<view v-if="weightConfig.includes('1')" class="h50 gui-text-center gui-border-r gui-border-b ellipsis-1">
															{{weightTable.length>1?getSummaries(Number(weightTable[0].waistline),Number(weightTable[weightTable.length-1].waistline),1):'0'}}
															<text class="pl-5 gui-color-green" v-if="getSummaries(Number(weightTable[0].waistline),Number(weightTable[weightTable.length-1].waistline),2) === '下'" >↓</text>
															<text class="pl-5 gui-color-red" v-if="getSummaries(Number(weightTable[0].waistline),Number(weightTable[weightTable.length-1].waistline),2) === '上'">↑</text>
														</view>
														<view v-if="weightConfig.includes('2')" class="h50 gui-text-center gui-border-r gui-border-b ellipsis-1">
															{{weightTable.length>1?getSummaries(Number(weightTable[0].abdomen),Number(weightTable[weightTable.length-1].abdomen),1):'0'}}
															<text class="pl-5 gui-color-green" v-if="getSummaries(Number(weightTable[0].abdomen),Number(weightTable[weightTable.length-1].abdomen),2) === '下'" >↓</text>
															<text class="pl-5 gui-color-red" v-if="getSummaries(Number(weightTable[0].abdomen),Number(weightTable[weightTable.length-1].abdomen),2) === '上'">↑</text>
														</view>
														<view v-if="weightConfig.includes('3')" class="h50 gui-text-center gui-border-r gui-border-b ellipsis-1">
															{{weightTable.length>1?getSummaries(Number(weightTable[0].hipline),Number(weightTable[weightTable.length-1].hipline),1):'0'}}
															<text class="pl-5 gui-color-green" v-if="getSummaries(Number(weightTable[0].hipline),Number(weightTable[weightTable.length-1].hipline),2) === '下'" >↓</text>
															<text class="pl-5 gui-color-red" v-if="getSummaries(Number(weightTable[0].hipline),Number(weightTable[weightTable.length-1].hipline),2) === '上'">↑</text>
														</view>
														<view class="h50 gui-text-center gui-border-r gui-border-b ellipsis-1">
															{{weightTable.length>1?getSummaries(Number(weightTable[0].BMI),Number(weightTable[weightTable.length-1].BMI),1):'0'}}
															<text class="pl-5 gui-color-green" v-if="getSummaries(Number(weightTable[0].BMI),Number(weightTable[weightTable.length-1].BMI),2) === '下'" >↓</text>
															<text class="pl-5 gui-color-red" v-if="getSummaries(Number(weightTable[0].BMI),Number(weightTable[weightTable.length-1].BMI),2) === '上'">↑</text>
														</view>
														<view v-if="weightConfig.includes('4')" class="h50 gui-text-center gui-border-r gui-border-b ellipsis-1">
															{{weightTable.length>1?getSummaries(Number(weightTable[0].waisHipRatio),Number(weightTable[weightTable.length-1].waisHipRatio),1):'0'}}
															<text class="pl-5 gui-color-green" v-if="getSummaries(Number(weightTable[0].waisHipRatio),Number(weightTable[weightTable.length-1].waisHipRatio),2) === '下'" >↓</text>
															<text class="pl-5 gui-color-red" v-if="getSummaries(Number(weightTable[0].waisHipRatio),Number(weightTable[weightTable.length-1].waisHipRatio),2) === '上'">↑</text>
														</view>
														<view v-if="weightConfig.includes('5')" class="h50 gui-text-center gui-border-r gui-border-b ellipsis-1">
															{{weightTable.length>1?getSummaries(Number(weightTable[0].pbf),Number(weightTable[weightTable.length-1].pbf),1):'0'}}
															<text class="pl-5 gui-color-green" v-if="getSummaries(Number(weightTable[0].pbf),Number(weightTable[weightTable.length-1].pbf),2) === '下'" >↓</text>
															<text class="pl-5 gui-color-red" v-if="getSummaries(Number(weightTable[0].pbf),Number(weightTable[weightTable.length-1].pbf),2) === '上'">↑</text>
														</view>
														<view v-if="weightConfig.includes('6')" class="h50 gui-text-center gui-border-r gui-border-b ellipsis-1">
															{{weightTable.length>1?getSummaries(Number(weightTable[0].visceralFatIndex),Number(weightTable[weightTable.length-1].visceralFatIndex),1):'0'}}
															<text class="pl-5 gui-color-green" v-if="getSummaries(Number(weightTable[0].visceralFatIndex),Number(weightTable[weightTable.length-1].visceralFatIndex),2) === '下'" >↓</text>
															<text class="pl-5 gui-color-red" v-if="getSummaries(Number(weightTable[0].visceralFatIndex),Number(weightTable[weightTable.length-1].visceralFatIndex),2) === '上'">↑</text>
														</view>
													</view>
												</view>
												<view class="fs-28 gui-color-gray mt-15">注：数据仅展示首次及最新两次记录。</view>
											</view>
											<view v-if="weightTable.length <= 0">
												<gui-empty>
													<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
														<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
													</view>
													<text slot="text"
													class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
												</gui-empty>
											</view>
										</view>
									</view>
								</view>
							</scroll-view>
						</swiper-item>
						<swiper-item>
							<!-- 使用滚动区域来实现主体内容区域 -->
							<scroll-view :scroll-y="true" class="gui-bg-white" :style="{height:mainHeight+'px'}">
								<view class="view_block" >
									<view class="title gui-flex gui-space-between">
										<text>血压（单位：mmHg）最近10次</text>
										<view class="gui-flex gui-align-items-center">
											<view class="gui-flex gui-align-items-center">
												<text style="display: block;height: 10rpx;width: 20rpx;background-color: #1890FF; margin-right: 5rpx;"></text>
												舒张压
											</view>
											<view class="gui-flex gui-align-items-center">
												<text style="display: block;height: 10rpx;width: 20rpx;background-color: #91CB74;margin-right: 5rpx;"></text>
												收缩压
											</view>
										</view>
									</view>
									<view class="charts-box">
										<qiun-data-charts :opts="opts" type="mix" :canvas2d="true" :ontouch="true" :chartData="xyaData" />
									</view>
									<view class="mt-60 pr-20">
										<view class="fs-30 mb-40">血压指标值参考范围</view>
										<view class="">
											<view class="gui-flex w-100 mb-40">
												<view class="mr-10 w-20">
													舒张压：
												</view>
												<view class="w-80">
													<progresdemo :textList="szList"></progresdemo>
												</view>
											</view>
											<view class="gui-flex w-100">
												<view class="mr-20 w-20">
													收缩压：
												</view>
												<view class="w-80">
													<progresdemo :textList="ssList"></progresdemo>
												</view>
											</view>
										</view>
									</view>
								</view>
							</scroll-view>
						</swiper-item>
						<swiper-item>
							<!-- 使用滚动区域来实现主体内容区域 -->
							<scroll-view :scroll-y="true" class="gui-bg-white" :style="{height:mainHeight+'px'}">
								<view class="view_block">
									<view class="title">
										<text>血糖（单位：mmol/L）</text>
									</view>
									<view class="charts-box">
										<qiun-data-charts :opts="opts" :canvas2d="true" :ontouch="true" type="mix" :chartData="xtangData" />
									</view>
									<view class="mt-60 px-20">
										<view class="fs-30 mb-40">血糖指标值参考范围</view>
										<view class="">
											<view class="gui-flex w-100 mb-40">
												<view class="mr-20 w-20">
													空腹：
												</view>
												<view class="w-80">
													<progresdemo :textList="kfuList"></progresdemo>
												</view>
											</view>
											<view class="gui-flex w-100">
												<view class="mr-20 w-20">
													餐后：
												</view>
												<view class="w-80">
													<progresdemo :textList="canhList"></progresdemo>
												</view>
											</view>
										</view>
									</view>
								</view>
							</scroll-view>
						</swiper-item>
						<swiper-item>
							<!-- 使用滚动区域来实现主体内容区域 -->
							<scroll-view :scroll-y="true" class="gui-bg-white" :style="{height:mainHeight+'px'}">
								<view class="view_block">
									<view class="title">
										<text>脉搏（单位：次/分）</text>
									</view>
									<view class="charts-box">
										<qiun-data-charts :opts="opts" :canvas2d="true" :ontouch="true" type="mix" :chartData="mboData" />
									</view>
									<view class="mt-60 px-20">
										<view class="fs-30 mb-40">脉搏指标值参考范围</view>
										<view class="">
											<view class="gui-flex w-100 mb-40">
												<view class="mr-20 w-20">
													脉搏：
												</view>
												<view class="w-80">
													<progresdemo :textList="mboList">456</progresdemo>
												</view>
											</view>
										</view>
									</view>
								</view>
							</scroll-view>
						</swiper-item>
						<swiper-item>
							<!-- 使用滚动区域来实现主体内容区域 -->
							<scroll-view :scroll-y="true" class="gui-bg-white" :style="{height:mainHeight+'px'}">
								<view v-if="list.length > 0">
									<view v-for="(item,index) in list" :key="index" @tap="detail(item)" class="mb-40" @change="changelist">
										<view class="d-flex ai-center mx-30 mt-20" v-if="item.taskType!= 4">
											<view class="mr-20"><gui-tags :margin="0" :text=" $common.parseTime(item.createTime,'{m}-{d} {h}:{i}')" :size="22" bgClass="gui-bg-white" color="#9AA7B9" borderColor="#9AA7B9"></gui-tags></view>
											<text v-if="item.taskType == 2" style="color: #9AA7B9;font-size: 28rpx;">宣教</text>
										</view>
										<view class="d-flex ai-center jc-between p-30 pos-relative m-30" style="background-color: #F5F6F8;" v-if="item.taskType!= 4">
											<image src="https://img.starup.net.cn/xmkj/zwb/img/zy_jkjy_jil.png" mode="widthFix" class="w50 h50"></image>
											<text class="ml-20 ellipsis-1 flex-1 fs-28 font-bold">{{item.questionnaireName}}</text>
											<gui-tags v-if="item.status == 1" :margin="0" text="已完成" :size="26" bgClass="gui-bg-green"></gui-tags>
										</view>
									</view>
								</view>
								<view v-if="list.length<= 0">
									<gui-empty>
										<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
											<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
										</view>
										<text slot="text"
										class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
									</gui-empty>
								</view>
							</scroll-view>
						</swiper-item>
						<swiper-item>
							<!-- 使用滚动区域来实现主体内容区域 -->
							<scroll-view :scroll-y="true" class="gui-bg-white" :style="{height:mainHeight+'px'}">
								<view v-if="list.length > 0">
									<view v-for="(item,index) in list" :key="index" @tap="detail(item)" class="mb-40" @change="changelist">
										<view class="d-flex ai-center mx-30 mt-20" v-if="item.taskType!= 4">
											<view class="mr-20"><gui-tags :margin="0" :text=" $common.parseTime(item.createTime,'{m}-{d} {h}:{i}')" :size="22" bgClass="gui-bg-white" color="#9AA7B9" borderColor="#9AA7B9"></gui-tags></view>
											<text v-if="item.taskType == 1" style="color: #9AA7B9;font-size: 28rpx;">问卷</text>
										</view>
										<view class="d-flex ai-center jc-between p-30 pos-relative m-30" style="background-color: #F5F6F8;" v-if="item.taskType!= 4">
											<image src="https://img.starup.net.cn/xmkj/zwb/img/zy_jkjy_jil.png" mode="widthFix" class="w50 h50"></image>
											<text class="ml-20 ellipsis-1 flex-1 fs-28 font-bold">{{item.questionnaireName}}</text>
											<gui-tags v-if="item.status == 1" :margin="0" text="已完成" :size="26" bgClass="gui-bg-green"></gui-tags>
										</view>
									</view>
								</view>
								<view v-if="list.length<= 0">
									<gui-empty>
										<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
											<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
										</view>
										<text slot="text"
										class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
									</gui-empty>
								</view>
							</scroll-view>
						</swiper-item>
					</swiper>
				</view>
			</view>
		</view>
		<view class="h50"></view>
		<view v-if="current == 0" class="insetbnt gui-color-white gui-flex gui-align-items-center gui-justify-content-center" @click="insetbnt">
			<view class="gui-flex gui-align-items-center gui-justify-content-center ">
				<text class="gui-icons fs-26">&#xe6c7;</text>
				<text class="fs-24 pl-5">舌面</text>
			</view>
		</view>
		<!-- 舌面象弹窗 -->
		<gui-modal ref="guimodal" title="舌面象详情" titleStyle="line-height:100rpx;font-weight:700; font-size:30rpx;">
			<view slot="content" class="gui-padding pb-30">
				<view class="w-100">
					<view class="gui-border-b pb-20">
						<image style="width: 29%;height: 70px;margin-right: 10rpx; margin-left: 10rpx;" :src="item" v-for="(item,index) in imgs" :key="index" @click="clickImg(index)"/>
					</view>
					<view class="mt-20 ml-10 fs-30">记录时间 ：{{$common.parseTime(lingualobj.timeBegin,'{y}-{m}-{d} {h}:{i}')}}</view>
					<view class="mt-10 ml-10 fs-32">描 述 ：{{lingualobj.lingualRemark || '-'}}</view>
				</view>
			</view>
		</gui-modal>
	</view>
</template>

<script>
	import { taskList,healthData,getTaskDateStatus,getWeightByPatientId,getWeightDataItemConfig } from "@/api/task.js"
	import {getAssessment} from '@/api/children.js'
	import lxCalendar from '@/components/lx-calendar/lx-calendar.vue'
	import progresdemo from "@/components/progresdemo.vue"
	export default {
		components: { lxCalendar,progresdemo },
		data() {
			return {
				statusTime:[],//打卡日期组件显示待执行状态
				bloodSugar: [0, 6.5],
				fromData: {
				  insulinVolume: '', //胰岛素
				  timeBegin: '', //日期
				  recordType: undefined, //记录类型
				  remark: '', //备注
				  bloodSugar: 6.5//血糖值
				},
				kfuList:[
					{start: '0', end: '3.9', value: '过低'},
					{start: '3.9', end: '6.1', value: '健康'},
					{start: '6.1', end: '7', value: '超标'},
					{start: '7', end: '35', value: '高危'},
				],
				canhList:[
					{start: '0', end: '5', value: '过低'},
					{start: '5', end: '7.8', value: '健康'},
					{start: '7.8', end: '12', value: '超标'},
					{start: '12', end: '35', value: '高危'},
				],
				szList:[
					{start: '0', end: '60', value: '低危'},
					{start: '60', end: '90', value: '健康'},
					{start: '90', end: '110', value: '中危'},
					{start: '110', end: '150', value: '高危'},
				],
				ssList:[
					{start: '0', end: '90', value: '低危'},
					{start: '90', end: '140', value: '健康'},
					{start: '140', end: '160', value: '中危'},
					{start: '160', end: '180', value: '高危'},
				],
				mboList:[
					{start: '0', end: '60', value: '低危'},
					{start: '60', end: '120', value: '健康'},
					{start: '120', end: '130', value: '中危'},
					{start: '130', end: '150', value: '高危'},
				],
				navItems : [
					{ id: 3, dictLabel: "监测" },
					{ id: 0, dictLabel: "血压" },
					{ id: 1, dictLabel: "血糖" },
					{ id: 2, dictLabel: "脉搏" },
					{ id: 4, dictLabel: "宣教" },
					{ id: 5, dictLabel: "问卷" },
				],
				// 选中选项的 索引
				currentIndex : 0,
				// 核心区域高度
				mainHeight   : 600,
				// 选项卡标签
				tabs: [{
					status: 0,
					dictLabel: '今日任务'
				},
				// {
				// 	status: 0,
				// 	dictLabel: '待打卡'
				// },
				{
					status: 1,
					dictLabel: '已完成'
				}],
				date:"",
				// 选中选项的 索引
				current: 0,
				loadingStatus:false, //  ， true： 请求中   false :请求结束
				loadState: 0 ,  // 0 : 默认0，有下一页   1 ：请求中  2： 加载完毕
				page:1,
				size:20,
				zongSize:0,
				list:[],
				guimodalTitle:'舌面象详情',
				toJianceIndex:10,//10舌面象 11身高体重
				weightTable:[],//已完成状态-体重管理数据
				chartData: {
					categories: [],
					series: [{
						name: "",
						data: []
					}]
				},
				/* 健康数据 */
				healthData: {
					/* 血糖 */
					sugar: {
						/* 最新血糖 */
						latestSugar: 0,
						/* 最新血糖 */
						latestTime: '00:00',
						/* 区间数据 */
						section: []
					},
					/* 血压 */
					cholesterol: {
						/* 最新舒张压 */
						newDbpCholestero: 0,
						/* 最新收缩压 */
						newSbpCholestero: 0,
						/* 平均血压 */
						averageCholestero: 0,
						/* 最高舒张压 */
						maxDbpCholestero: 0,
						/* 最低舒张压 */
						minDbpCholestero: 0,
						/* 最高收缩压 */
						maxSbpCholestero: 0,
						/* 最低收缩压 */
						minSbpCholestero: 0,
						/* 血压区间 */
						section: []
					},
					lingualFacies:[],//舌面象

				},
				xyaData: {
					categories: [],
					series: [{
						name: "舒张压",
						data: []
					}, {
						name: "收缩压",
						data: []
					}]
				},
				xtangData: {
					categories: [],
					series: [{
						name: "血糖",
						data: []
					}]
				},
				mboData: {
					categories: [],
					series: [{
						name: "脉搏",
						data: []
					}]
				},
				opts: {
					enableScroll: true,
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [15, 20, 10, 10],
					legend: {
						show: false //是否显示图例
					},
					xAxis: {
						disableGrid: true,
						rotateLabel: true,
						rotateAngle: 40,//底部文字倾斜角度
						// scrollShow:true,//显示滚动条
						itemCount: 11//屏幕显示数量
					},
					yAxis: {
						disabled: false,
						disableGrid: false,
						splitNumber: 5,
						gridType: "dash",
						dashLength: 4,
						padding: 10,
						showTitle: false
					},
					extra: {
						line: {
							type: "curve",
							width: 2
						},

						length: {
							width: 15
						},

					}
				},
				lingualobj:{},//舌面象信息
				imgs:[],////舌面象图片数组
				weekList:[],//当前周的日期

			}
		},
		created() {
			this.date = this.$common.parseTime(new Date(),'{y}-{m}-{d}');
		},
		onShow() {
			this.$common.role();
			// 如果是从首页消息那边跳过来的就切换成待执行
			if (uni.getStorageSync('index')) {
				this.current = 1
				uni.removeStorageSync('index')
			}
			this.inits();
		},
		//下拉刷新
		onPullDownRefresh() {
			this.init();
			setTimeout(function() {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		//上拉加载
		onReachBottom: function() {
			//避免多次触发
			if(this.loadingStatus || this.loadState == 2){return}
			this.getList();
		},
		methods: {
			clickImg(index) {//图片放大预览
				uni.previewImage({
					current: index,     // 当前显示图片的索引值
					urls: this.imgs,    // 需要预览的图片列表，photoList要求必须是数组
					loop:true,          // 是否可循环预览
				})
			},
			gethealthData(e){
				healthData({
					patientId: uni.getStorageSync("cardObj").patientId || '',
					healthDataType:e,//1.血糖 2.计步 3.睡眠 4.心率 5.血氧 6.血压 7.脉搏 8.呼吸 9.体温 10.舌面象
					range:this.size
					// date:this.data
				}).then(res =>{
					var resData = res?.data
					this.$set(this, 'healthData', resData)
					this.$set(this.healthData, 'lingualFacies', resData.lingualFacies?resData.lingualFacies : [])
					var data = [];
					var dataSpot = [];
					var	series = [];
					var	categories = [];
					if (e == 6) {
							// this.$set(this.healthData.cholesterol, 'connection', sphygmomanometer?.status && true || false)//设备相关
						/* 血压 */
						var dbpData = [];
						var	sbpData = [];
						var dbpDataSpot = []
						var sbpDataSpot = []
						/* 处理展示数据 */
						this.healthData.cholesterol.section.forEach(item => {
							dbpData.push(item.dbp)
							sbpData.push(item.sbp)
							if (item.dbp > 90) {
								dbpDataSpot.push(item.dbp)
							} else if (item.dbp < 60) {
								dbpDataSpot.push(item.dbp)
							} else {
								dbpDataSpot.push(null)
							}
							if (item.sbp > 140) {
								sbpDataSpot.push(item.sbp)
							} else if (item.sbp < 90) {
								sbpDataSpot.push(item.sbp)
							} else {
								sbpDataSpot.push(null)
							}
							categories.push( this.$common.parseTime(new Date(item.timeBegin.replace(/-/g, '/')),'{m}/{d} {h}:{i}'));
						});
						/* 补充数据 */
						series.push({
							type: "line",
							name: "舒张压",
							data: JSON.parse(JSON.stringify(dbpData))
						}, {
							type: "line",
							name: "收缩压",
							data: JSON.parse(JSON.stringify(sbpData))
						});
						/* 处理预警 */
						dbpData.forEach((item, index) => {
							if (item > 90) {
								dbpData[index] = null
								series.push({
									point: true,
									type: "point",
									data: dbpDataSpot,
									color: "#f04864",
									textColor: "#f04864",
									format: 'linSpotFormatter'
								})
							} else if (item < 60) {
								dbpData[index] = null
								series.push({
									point: true,
									type: "point",
									data: dbpDataSpot,
									color: "#f04864",
									textColor: "#f04864",
									format: 'linSpotFormatter'
								})
							}
						})
						sbpData.forEach((item, index) => {
							if (Number(item) > Number(140)) {
								sbpData[index] = null
								series.push({
									point: true,
									type: "point",
									data: sbpDataSpot,
									color: "#f04864",
									textColor: "#f04864",
									format: 'linSpotFormatter'
								})
							} else if (Number(item) > Number(90)) {
								sbpData[index] = null
								series.push({
									point: true,
									type: "point",
									data: sbpDataSpot,
									color: "#f04864",
									textColor: "#f04864",
									format: 'linSpotFormatter'
								})
							}
						})
						this.$set(this, 'xyaData', {
							series: series,
							categories: categories,
						})
					}else if (e == 1) {
						this.healthData.sugar.section.forEach(item => {
							data.push(item.bloodSugar)
							console.log('item.recordType',item.recordType)
							if (item.recordType == 1) {//空腹
								if (Number(item.bloodSugar) > Number(6.1)) {
									dataSpot.push(item.bloodSugar)
								} else if (Number(item.bloodSugar) < Number(3.9)) {
									dataSpot.push(item.bloodSugar)
								} else{
									dataSpot.push(null)
								}
							} else {//餐后为 0
								if (Number(item.bloodSugar) > Number(7.8)) {
									dataSpot.push(item.bloodSugar)
								} else if (Number(item.bloodSugar) < Number(5)) {
									dataSpot.push(item.bloodSugar)
								} else{
									dataSpot.push(null)
								}
							}
							categories.push(this.$common.parseTime(new Date(item.timeBegin.replace(/-/g, '/')),'{m}/{d} {h}:{i} ') + item.pointTime);
						});
						/* 补充数据 */
						series.push({
							name: "血糖",
							type: "line",
							data: JSON.parse(JSON.stringify(data))
						})
						/* 处理预警 */
						data.forEach((item, index) => {
							if (index == 0) {
								if (item > Number(6.1)) {
									data[index] = null
									series.push({
										point: true,
										type: "point",
										data: dataSpot,
										color: "#f04864",
										textColor: "#f04864",
										format: 'linSpotFormatter'
									})
								}else if (item < Number(3.9)) {
									data[index] = null
									series.push({
										point: true,
										type: "point",
										data: dataSpot,
										color: "#f04864",
										textColor: "#f04864",
										format: 'linSpotFormatter'
									})
								}
							} else {
								if (item > Number(7.8)) {
									data[index] = null
									series.push({
										point: true,
										type: "point",
										data: dataSpot,
										color: "#f04864",
										textColor: "#f04864",
										format: 'linSpotFormatter'
									})
								}else if (item < Number(5)) {
									data[index] = null
									series.push({
										point: true,
										type: "point",
										data: dataSpot,
										color: "#f04864",
										textColor: "#f04864",
										format: 'linSpotFormatter'
									})
								}
							}
						})
						this.$set(this, 'xtangData', {
							series: series,
							categories: categories
						})
					} else if (e == 7) {
						this.healthData.pulse.forEach(item => {
							data.push(item.pulse)
							if (item.pulse > 120) {
								dataSpot.push(item.pulse)
							} else if (item.pulse < 60) {
								dataSpot.push(item.pulse)
							}{
								dataSpot.push(null)
							}
							categories.push(this.$common.parseTime(new Date(item.timeBegin.replace(
									/-/g, '/')),
								'{m}/{d} {h}:{i}'));
						});
						/* 补充数据 */
						series.push({
							name: "脉搏",
							type: "line",
							data: JSON.parse(JSON.stringify(data))
						})
						data.forEach((item, index) => {
								if (item > 120) {
									data[index] = null
									series.push({
										point: true,
										type: "point",
										data: dataSpot,
										color: "#f04864",
										textColor: "#f04864",
										format: 'linSpotFormatter'
									})
								}else if (item < 60) {
									data[index] = null
									series.push({
										point: true,
										type: "point",
										data: dataSpot,
										color: "#f04864",
										textColor: "#f04864",
										format: 'linSpotFormatter'
									})
								}

						})
						this.$set(this, 'mboData', {
							series: series,
							categories: categories
						})
					}
				})
			},
			changeTo(e) {
				this.lingualobj = e
				this.imgs = this.lingualobj.lingualFiles?.split(',')
				if (e) {//舌面象页面
					this.$refs.guimodal.open();
					// this.$common.navTo('/pages/task/tongue?id='+e.id+'&taskType=3')
				}
			},
			// previewImage(e, list) {
			// 	uni.previewImage({
			// 		current: e, // 当前显示图片的索引值
			// 		urls: list, // 需要预览的图片列表，photoList要求必须是数组
			// 	})
			// },
			// change1: function (e) {
			//   this.fromData.bloodSugar = e[1]
			//   this.$forceUpdate()
			// },
			changenav(index){
				//0.血压 1.血糖 2.脉搏 3.舌面象 4.宣教 5.问卷
				this.currentIndex = index;
				switch (index) {//1.血糖 2.计步 3.睡眠 4.心率 5.血氧 6.血压 7.脉搏 8.呼吸 9.体温 10.舌面象
					case 0:
						this.gethealthData(6)
						break;
					case 1:
						this.gethealthData(1)
						break;
					case 2:
						this.gethealthData(7)
						break;
					case 3:
						this.gethealthData(this.toJianceIndex)
						break;
					case 4:
						this.init(2) //宣教
						break;
					case 5:
						this.init(1)//问卷
						break;
				}
			},
			swiperChange : function(e){
				this.currentIndex = e.detail.current;
			},
			toJiance(e){//e：10舌面象 e:11身高体重
				console.log('监测选择==',e)
				this.toJianceIndex = e
				if (e == 10) {
					this.gethealthData(10)
				} else{
					if (!uni.getStorageSync("cardObj")) {return}
					this.getConfig()//获取字典1-腰围,2-腹围,3-髫围,4-腰鹮比,5-PBF,6-内脏脂肪指数，7治疗方案
					getWeightByPatientId(uni.getStorageSync("cardObj").patientId).then(res =>{
						this.weightTable = res.data.map(item =>{
							let height = item.height/100
							item.BMI = (item.weight/(height*height)).toFixed(2)
							return item
						})
					})
				}
			},
			getConfig(){//获取字典1-腰围,2-腹围,3-髫围,4-腰鹮比,5-PBF,6-内脏脂肪指数，7治疗方案
				getWeightDataItemConfig().then(res=>{
					this.weightConfig = res.data
				})
			},
			getSummaries(value1,value2,type){//计算体重管理中的数据变化
				if (Number.isFinite(value1) && Number.isFinite(value1)) {
					if (value1 > value2) {
						if (type==1) {
							return (value1 - value2).toFixed(2)
						} else{
							return '下'
						}

					}else if(value1 < value2){
						if (type==1) {
							return (value2 - value1).toFixed(2)
						} else{
							return '上'
						}
					} else{
						if (type==1) {
							return '0'
						} else{
							return '无'
						}

					}
				} else{
					if (type==1) {
						return '-'
					} else{
						return '无'
					}
				}
			},
			changeDate(e,week){
				this.weekList = week;
				this.date = e.fulldate;
				this.init();
			},
			getStatus(){
				// if (!uni.getStorageSync("cardObj").patientId) {return}
				getTaskDateStatus({
					patientId:uni.getStorageSync("cardObj").patientId || '',
					memberId:uni.getStorageSync("user").id,
					startDate:this.weekList.length>0?this.$common.parseTime(this.weekList[0].date,'{y}-{m}-{d}'):'',
					endDate:this.weekList.length>0?this.$common.parseTime(this.weekList[6].date,'{y}-{m}-{d}'):'',
				}).then(res =>{//执行状态小标识
					this.statusTime = res.data;
					this.$forceUpdate()
				})
			},
			// 初始化
			init(e){
				if (this.current == 0) {
					this.getStatus()//今日任务--日期组件--执行状态小标签
				}
				this.page = 1;
				this.loadingStatus = false;
				this.loadState = 0;
				// this.navchange(this.current)
				this.getList(e?e:'');
			},
			inits(){
				if (!uni.getStorageSync("token") || !uni.getStorageSync("user")) {
					uni.showModal({
						title: '提示',
						content: '您没有授权登录！',
						confirmText: '去登录',
						confirmColor: '#576B95',
						success: function(res) {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/login/index'
								})
							} else if (res.cancel) {
								uni.switchTab({
									url: '/pages/home/<USER>'
								})
							}
						}
					});
				} else {
					this.navchange(this.current)
					// this.init();
				}
			},
			getList(e) {
				this.loadingStatus  = true;
				taskList({
					pageNum:this.page,
					pageSize:this.size,
					memberId: uni.getStorageSync("user")?.id || '',
					patientId: uni.getStorageSync("cardObj").patientId || '',
					date: this.current == 0 ? this.date : "",
					status:this.tabs[this.current].status,
					taskType:e?e:'',
				}).then(res=>{
					this.list = this.page == 1 ? res.rows : this.list.concat(res.rows);
					this.zongSize = res.total;
					if(this.list.length >= this.zongSize){
						this.loadState = 2;
						this.loadingStatus  = false;
						return;
					}
					this.page++;
					this.loadState = 0;
					this.loadingStatus  = false;
				})
				this.$common.getNoReads(this.current == 0 ? this.date : "");
			},
			// 切换
			navchange: function(index) {
				this.current = index;
				if (index == 1) {
					this.changenav(this.currentIndex)
				}else{
					this.init()
				}
			},
			//上传舌面象
			insetbnt(){
				if (uni.getStorageSync('cardObj') && uni.getStorageSync('cardObj').patientId) {
					this.$common.navTo('/pages/task/tongue')
				} else {
					this.$common.msg("请先绑定就诊卡")
				}
			},
			// 跳转详情页
			detail(item) {
				// var i = item.questionnaireId
				if (item.taskType == 2) {
					// 宣教
					this.$common.navTo('/pages/task/other/missionary?id='+item.id+'&taskType='+item.taskType)
				} else if(item.taskType == 1) {
					// 问卷
					if (item.status == 1 && item.questionType ==2) {
						this.$common.navLaunch(
							'/pages/homeAc/evaluation/details?openType=1&questionId=' + item.questionnaireId +'&taskId='+item.taskId+'&patientId='+item.patientId)
					} else{
						this.$common.navTo('/pages/task/other/questionnaire?id='+item.id+'&taskType='+item.taskType)
					}
				}else if(item.taskType == 4) {
					// 康养
					this.$common.navTo('/pages/task/other/treatment?id='+item.id+'&taskType='+item.taskType)
				}else if (item.taskType == 5) {
					// 膏方
					this.$common.navTo('/pages/task/other/medication?id=' + item.id + '&taskType=' + item.taskType)
				}else {
					// 监测
					let i = parseInt(item.questionnaireId);
					// this.$common.navTo('/pages/task/other/monitor?id='+item.id+'&taskType='+item.taskType+'&key='+i) 后期改版可以把1-6整合在一个页面
					switch (i) {
						case 1:
							this.$common.navTo('/pages/task/other/aXueya?id='+item.id+'&taskType='+item.taskType)
							break;
						case 2:
							this.$common.navTo('/pages/task/other/bMaibo?id='+item.id+'&taskType='+item.taskType)
							break;
						case 3:
							this.$common.navTo('/pages/task/other/cXuetang?id='+item.id+'&taskType='+item.taskType)
							break;
						case 4:
							this.$common.navTo('/pages/task/other/dXueyang?id='+item.id+'&taskType='+item.taskType)
							break;
						case 5:
							let newdate = this.$common.parseTime(new Date(),'{y}-{m}-{d}')
							if(newdate == this.date){
								this.$common.navTo('/pages/task/other/eTiwen?id='+item.id+'&taskType='+item.taskType)
							}
							break;
						case 6:
							this.$common.navTo('/pages/task/other/fHuxi?id='+item.id+'&taskType='+item.taskType)
							break;
						case 7:
							// this.$common.msg("正在升级，敬请期待~")
							// 本本升级
							this.$common.navTo('/pages/task/tongue?id='+item.id+'&taskType='+item.taskType)
							break;
						case 8:
							// 身高体重
							this.$common.navTo('/pages/children/add?id='+item.id+'&taskType='+item.taskType+'&timedata='+ this.date)
							// this.$common.msg("正在升级，敬请期待~")
							// if(item.status == 0){
							// 	getAssessment(uni.getStorageSync("cardObj").patientId).then(res=>{
							// 		//判断是否填数据
							// 		if (res.data == null) {
							// 			this.$common.navLaunch('/pages/children/index?id='+item.id+'&taskType='+item.taskType+'&timedata='+ this.date)
							// 			return
							// 		}
							// 		this.$common.navTo('/pages/children/add?id='+item.id+'&taskType='+item.taskType+'&timedata='+ this.date)
							// 	})

							// }else{
							// 	this.$common.navTab('/pages/index/index')
							// }
							// this.$common.navTo('/pages/task/tongue?id='+item.id+'&taskType='+item.taskType)
							break;
						case 12://减重记录
							if(item.status == 0){
								this.$common.navTo('/pages/children/add?id='+item.id+'&taskType='+item.taskType+'&timedata='+ this.date+'&openType=3')
							}else{
								this.current = 1;
								this.toJianceIndex = 11;
								this.toJiance(this.toJianceIndex)
								this.navchange(this.current)
                this.$refs['tabRef'].currentIn = this.current
								this.$forceUpdate()
							}
							break;
					}
					uni.setStorageSync('jcData',item)
				}
			}
		}
	}
</script>

<style scoped lang="less">
	.line-h50{
		line-height: 50rpx;
	}
	.line-h80{
		line-height: 80rpx;
	}
	.time-line {
		display: flex;
		flex-direction: column;
		width: 30rpx;
		position: relative;
		justify-content: center;
		align-items: center;

		.time-line-i {
			width: 2rpx;
			background-color: #aaa;
			height: 100%;
		}
	}
	.gui-accordion-icon {
		width: 50rpx;
		font-size: 32rpx;
	}
	.l-icon {
		background: #008AFF;
		width: 25rpx;
		height: 25rpx;
		border-radius: 25rpx;
		position: absolute;
		top: 0;
	}
	.visitDate-row {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
	}

	.demo-nav{padding:15rpx 30rpx;}
	.demo-text{line-height:200rpx; padding-bottom:3000px;}
	.shadow {
		box-shadow: 0px 3rpx 10rpx 0rpx #F1F1F1;
	}
	.shadow2 {
		box-shadow: 0px 20rpx 20rpx 0rpx #f7f7f7;
	}
	.sub-title {
		border: 1px solid #41bcb3;
		padding: 4rpx 6rpx;
		color: #41bcb3;
		border-radius: 10rpx;
	}

	.tab-card-demo-text {
		line-height: 388rpx;
		font-size: 26rpx;
	}
	.img {
		width: 150rpx;
		border-radius: 10rpx;
		height: 150rpx;
		margin: 10rpx;
	}
	.insetbnt{
		/* display: flex; */
		align-items: center;
		width: 110rpx;
		height: 110rpx;
		/* line-height: 100rpx; */
		border-radius: 110rpx;
		background-color: #C59F79;
		box-shadow:5rpx 10rpx 10rpx gainsboro;
		position: fixed;
		bottom: 180rpx;
		right: 40rpx;
	}
	.demo-nav2{width:750rpx; box-sizing: border-box; padding-bottom:0;}
	.view_block {
		background-color: #fff;
		padding: 16rpx 20rpx 10rpx 20rpx;
		border-radius: 20rpx;
		margin-bottom: 40rpx;

		.title {
			margin: 10rpx 10rpx;

			text:nth-child(1) {
				// background-color: red;
				font-size: 20rpx;
				margin-left: 15rpx;
				padding: 0rpx 10rpx;
			}

			image {
				width: 30rpx;
				height: 30rpx;
				margin: 0rpx 10rpx;
			}

			.font-small {
				// background-color: pink;
				font-size: 28rpx;
				color: #3c3c3c;
				padding: 0rpx 10rpx;
			}
		}


	}
	.charts-box {
		width: 100%;
		height: 300px;
	}
</style>
