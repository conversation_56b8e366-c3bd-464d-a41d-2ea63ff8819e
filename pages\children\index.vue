<template>
	<!-- 页面主体 -->
	<view style="padding:20rpx 20rpx 10rpx 40rpx;">
		<form @submit="submit">
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">姓名</text>
				<view class="gui-form-body">
					<input type="text" class="gui-form-input" :disabled="true" v-model="formData.name" name="name"
						placeholder="请输入姓名" />
				</view>
			</view>
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">性别</text>
				<view class="gui-form-body">
					<picker v-model="formData.sexo" :range="gender" @change="pickerChange">
						<view class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center">
							<text class="gui-text">{{gender[genderIndex]}}</text>
							<text class="gui-form-icon gui-icons gui-text-center gui-color-gray">&#xe603;</text>
						</view>
					</picker>
					<!-- <input type="text" class="gui-form-input" v-model="formData.sex" name="sex" placeholder="请输入性别" /> -->
				</view>
			</view>
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">生日</text>
				<view class="gui-form-body">
					<gui-datetime @confirm="confirm2" :value="formData.birthday" :isTime="false">
						<input class="demo gui-border-radius-large" type="text" v-model="formData.birthday"
							placeholder="请选择" />
					</gui-datetime>
				</view>
			</view>
			<view v-if="showInput" class="gui-form-item gui-border-b">
				<text class="gui-form-label">当前身高</text>
				<view class="gui-form-body">
					<input type="text" class="gui-form-input" v-model="formData.height" name="height"
						placeholder="请输入身高" />
				</view>
			</view>
			<view v-if="showInput" class="gui-form-item gui-border-b">
				<text class="gui-form-label">当前重量</text>
				<view class="gui-form-body">
					<input type="text" class="gui-form-input" v-model="formData.weight" name="weight"
						placeholder="请输入重量" />
				</view>
			</view>
			<view v-if="showInput" class="gui-form-item gui-border-b">
				<text class="gui-form-label">测量日期</text>
				<view class="gui-form-body">
					<input type="text" class="gui-form-input" v-model="formData.measureDate" name="measureDate"
						placeholder="请输入日期" />
				</view>
			</view>

			<view class="mt-200 gui-flex gui-rows gui-nowrap gui-justify-content-center">
				<button type="default" class="gui-button" style="width:400rpx;background-color: #C59F79;"
					formType="submit">
					<text class="gui-icons gui-color-white gui-button-text ">保存</text>
					<!-- <text v-else class="gui-icons gui-color-white gui-button-text ">保存生成评估</text> -->
				</button>
			</view>
			<view style="height:80rpx;"></view>
		</form>
	</view>


</template>

<script>
	import { assessmentPost } from '@/api/children.js'
	import {addTaskPerform,getFollowTaskInfo} from '@/api/task.js'
	export default {
		data() {
			return {
				showtask:false,
				showInput:true,
				sex: [{
						value: 1,
						label: '男'
					},
					{
						value: 2,
						label: '女'
					}

				],
				gender: ['请选择', '男', '女'],
				genderIndex: uni.getStorageSync("cardObj").sex?uni.getStorageSync("cardObj").sex:0,
				// Atype:uni.getStorageSync('type'),
				// 表单数据存储
				formData: {
					patientId: uni.getStorageSync("cardObj").patientId,
					name: uni.getStorageSync("cardObj").userName,
					sex: uni.getStorageSync("cardObj").sex ,
					birthday: uni.getStorageSync("cardObj").birthday, //默认从就诊卡上取 没有
					height: '',
					weight: '',
					measureDate: this.$common.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}'),
					parentHeight: '',
					motherHeight: '',
					createBy:uni.getStorageSync("cardObj").userName
				},
				Abirthday: ''

			}
		},
		onLoad(options){
			if (options.typaId) {
				this.showInput = false;
			}
			this.id = options.id;
			this.taskType = options.taskType;
			if (options.timedata) {
				this.showtask = true;
				this.formData.measureDate = this.$common.parseTime(options.timedata, '{y}-{m}-{d}');
				getFollowTaskInfo({
					id:this.id,taskType:this.taskType
				}).then(res=>{})
			} 
		},
		// created() {
		// 	this.formData.measureDate = this.$common.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');
		// },
		methods: {
			confirm2: function(res) {
				// let that = this
				console.log(res)
				this.Abirthday = res[0] + '-' + res[1] + '-' + res[2];
				this.formData.birthday = this.Abirthday
			},
			// newdata() {
			// 	// var time = new Date()
			// 	//  this.$common.parseTime(time,'{m}-{d} {h}:{i}')
			// 	// console.log("日期1",time)
			// 	// time.toLocaleDateString();
			// 	// console.log("日期",time.toLocaleDateString() + time.toLocaleTimeString())
			// 	//  let data = time.toLocaleDateString() + time.toLocaleTimeString()
			// 	// this.formData.currentDate = data
			// },
			// 监听输入框输入事件
			inputting: function(e) {
				var name2Val = e.detail.value;
				console.log(name2Val);
			},
			// 表单提交
			submit: function(e) {
				
				uni.setStorageSync("type", 2)
				// this.newdata()
				// 部分能够获得的表单数据 在 e 对象保存
				// 不支持表单直接获取的数据通过事件记录
				// 最终整理获得完整的表单记录
				// this.formData = e.detail.value
				// this.formData.patientId = uni.getStorageSync("cardObj").patientId
				// this.formData.createBy = uni.getStorageSync("cardObj").userName
				// if (this.Abirthday) {
				// 	this.formData.birthday = this.Abirthday
				// }
				// this.formData.sex = uni.getStorageSync("cardObj").sex
				// console.log(this.formData);
				if (!this.formData.sex || this.formData.sexo == '请选择') {
					return this.$common.msg('请选择性别')
				}
				if(this.showInput){
					if (!this.formData.height || this.formData.height == '') {
						return this.$common.msg('请选择身高')
					}
					if (!this.formData.weight || this.formData.sexo == '') {
						return this.$common.msg('请选择体重')
					}
				}
				delete this.formData.sexo
				assessmentPost(this.formData).then(res=>{
					if (!this.showInput) {
						//修改
						this.$common.navTab('/pages/index/index')
						return
					}
					if (this.showtask) {
						//首次推送打卡身高体重的患者
						addTaskPerform({
							planId: this.id,
							taskType: this.taskType,
							patientId: uni.getStorageSync('cardObj').patientId, // 就诊卡用户id
						}).then(res=>{
							this.$common.msg("提交成功", "success")
							setTimeout(() => {
								// uni.setStorageSync('tyoeid',1);
								this.$common.navTab('/pages/index/index')
								// this.$common.navCloseTo('/pages/children/report')
							}, 1000)
						})
					} else{
						//首次打开乐高的患者
						// this.$emit("out","1")
						setTimeout(() => {
							// uni.setStorageSync('tyoeid',1);
							this.$common.navTab('/pages/index/index')
						}, 1000)
					}
					
					
				})
				// this.$common.RequestData({
				// 	url: this.$common.assessment,
				// 	method: 'POST',
				// 	data: this.formData,
				// }, res => {
					
				// })

			},
			// picker 切换
			pickerChange: function(e) {
				this.genderIndex = e.detail.value;
				this.formData.sexo = this.gender[this.genderIndex];
				this.formData.sex = e.detail.value;
			},
			// onUnload() {
			// 	uni.reLaunch({
			// 		url: '/pages/index/index'
			// 	})
			// },
		}


	}
</script>
<style>

</style>
