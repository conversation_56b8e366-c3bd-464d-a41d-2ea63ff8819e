<template>
	<view class="gui-schedule-wrap">
		<view class="gui-flex gui-rows gui-space-between gui-align-items-center">
			<picker mode="date" :value="currentDayIn" :start="startDate" :end="endDate" @change="selectDate">
				<text class="gui-schedule-header-date gui-icons">{{cYear}} 年 {{cMonthStr}} 月 &#xe603;</text>
			</picker>
			<text class="gui-border gui-schedule-today" @tap="gotoToday">返回今天</text>
		</view>
		<view class="gui-flex gui-rows gui-nowrap">
			<text class="gui-schedule-weeks gui-color-gray gui-block-text" v-for="(item, index) in weeks"
				:key="index">{{item}}</text>
		</view>
		<view class="gui-flex gui-rows gui-wrap">
			<view class="gui-schedule-7item gui-flex gui-rows gui-justify-content-center" v-for="(item, index) in days"
				:key="index">
				<view class="gui-date-ditems gui-flex gui-columns gui-justify-content-center" v-if="item != ''"
					:class="[{'activeBgColor':currentDayIn == (cYear+'-'+cMonthStr+'-'+item.date)},{'bg-gran':item.nl == ''},{'bg-plum':item.nl == '1'},{'bg-pink':item.nl == '2'},{'bg-purple':item.nl == '3'}]"
					@click="chooseDate(cYear+'-'+cMonthStr+'-'+item.date, item.date)">
					<text class="gui-date-day gui-block-text" :class="[currentDayIn == (cYear+'-'+cMonthStr+'-'+item.date) ? 
					'gui-d-current-txt' : '']">{{item.date}}</text>
				</view>
			</view>
		</view>
		<view class="gui-border-b gui-schedule-line"></view>
		<view class="gui-flex gui-rows gui-space-around gui-bg-gray">
			<view class="gui-flex gui-rows gui-justify-content-center gui-align-items-center"><text
					class="iocn bg-plum"></text>月经期</view>
			<view class="gui-flex gui-rows gui-justify-content-center gui-align-items-center"><text
					class="iocn bg-pink"></text>预测期</view>
			<view class="gui-flex gui-rows gui-justify-content-center gui-align-items-center"><text
					class="iocn bg-purple"></text>易孕期</view>
			<view class="gui-flex gui-rows gui-justify-content-center gui-align-items-center"><text
					class="iocn bg-blue"></text>当前选择</view>
		</view>
	</view>
</template>
<script>
	import {getPregnancyByPatientId,editPregnancy} from '@/api/index.js'
	import guiCalendar from './gui-calendar.js';
	export default {
		name: "gui-schedule",
		data() {
			return {
				cYear: 2020,
				cMonth: 1,
				cDay: 10,
				cMonthStr: '01',
				weeks: ['一', '二', '三', '四', '五', '六', '日'],
				days: [],
				currentDayIn: '',
				stardata: '',
				enddata: '',
				siwid:0
			}
		},
		props: {
			// 当前默认日期
			currentDate: {
				type: String,
				default: ""
			},
			bgColor: {
				type: String,
				default: "#F8F8F8"
			},
			activeBgColor: {
				type: String,
				default: "#008AFF"
			},
			isLunar: {
				type: Boolean,
				default: true
			},
			switype: {
				type: Number,
				default: 0
			},
			startDate: {
				type: String,
				default: '1950-01-01'
			},
			endDate: {
				type: String,
				default: '2050-01-01'
			},
			pointColor: {
				type: String,
				default: "#FF0036"
			}

		},
		watch: {
			switype:{
				handler(newVal, oldVal) {
					this.siwid = newVal;
				},
				deep: true
			}
		},
		created: function() {
			this.currentDayIn = this.currentDate;
			this.stardata = this.currentDate
			this.initTime();
		},
		methods: {
			//设置实际月经期
			getdata(e){
				if (e == true) {
					this.stardata = this.currentDayIn
					this.enddata = ''
				} else {
					this.stardata = ''
					this.enddata = this.currentDayIn
				}
				let fromdata = {
					patientId: uni.getStorageSync("cardObj").patientId, //患者ID
					month:this.$common.parseTime(new Date(this.currentDayIn), '{y}-{m}'),
					actualStartDate:this.stardata,
					actualEndDate:this.enddata
				}
				editPregnancy(fromdata).then(res=>{
					if (res.data == null) {return}
					let data = res.data;
					//预测最近一次
					let dqnew = data.menstruationDates.
					filter(item => this.$common.parseTime(new Date(item), '{y}-{m}') === this.$common.parseTime(
						new Date(this.currentDayIn), '{y}-{m}'))
					//预测下一次
					let netnew = data.nextMenstruationDates.
					filter(item => this.$common.parseTime(new Date(item), '{y}-{m}') === this.$common.parseTime(
						new Date(this.currentDayIn), '{y}-{m}'))
					//易孕期
					let prnew = data.ovulationDates.
					filter(item => this.$common.parseTime(new Date(item), '{y}-{m}') === this.$common.parseTime(
						new Date(this.currentDayIn), '{y}-{m}'))
					if (this.days != null) {
						let dayStr = []
						this.days.forEach((item, index) => {
							if (item != '') {
								let datastr = this.cYear + '-' + this.cMonth + '-' + item.date
								item.datastr = datastr
								item.nl = ''
							}
						});
						let ycdata = dqnew.concat(netnew)
						//月经期
						if (data.actualDates != null) {
							let yjnew = data.actualDates.
							filter(item => this.$common.parseTime(new Date(item), '{y}-{m}') === this.$common.parseTime(
								new Date(this.currentDayIn), '{y}-{m}'));
							yjnew.forEach(yjarr => {
								let yjkey = this.days.findIndex(yjdata => yjdata?.datastr === yjarr)
								// 1.月经期 2.预测期 3.易孕期
								this.days[yjkey].nl = '1'
							})
							//预测期
							let a=new Set(yjnew);
							let b=new Set(ycdata);
							let sjdata = Array.from(new Set([...b].filter(x => !a.has(x))));
							sjdata.forEach(sjarr => {
								let yckey = this.days.findIndex(sjdata => sjdata?.datastr === sjarr)
								this.days[yckey].nl = '2'
							});
						}else{
							//预测期
							ycdata.forEach(ycarr => {
								let yckey = this.days.findIndex(ycdata => ycdata?.datastr === ycarr)
								this.days[yckey].nl = '2'
							});
						}
						//易孕期
						prnew.forEach(prarr => {
							let prkey = this.days.findIndex(prdata => prdata?.datastr === prarr)
							this.days[prkey].nl = '3'
						})
					}
				})
			},
			//获取经期预测数据
			getList(daytiam) {
				var monthtime = this.$common.parseTime(new Date(daytiam), '{y}-{m}')
				var querydata = {
					patientId: uni.getStorageSync("cardObj").patientId, //患者ID
					month: monthtime
				}
				getPregnancyByPatientId(querydata).then(res=>{
					if (!res.data || JSON.stringify(res.data) == "{}") {
						this.$common.navTo('/pages/index/period')
						let todays = this.$common.parseTime(new Date(), '{y}-{m}-{d}')
						uni.setStorageSync('timedata',todays)
						return
					} else{
						let data = res.data;
						//预测最近一次
						let dqnew = data.menstruationDates.
						filter(item => this.$common.parseTime(new Date(item), '{y}-{m}') === monthtime )
						//预测下一次
						let netnew = data.nextMenstruationDates.
						filter(item => this.$common.parseTime(new Date(item), '{y}-{m}') === monthtime)
						//易孕期
						let prnew = data.ovulationDates.
						filter(item => this.$common.parseTime(new Date(item), '{y}-{m}') === monthtime)
						if (this.days != null) {
							let dayStr = []
							this.days.forEach((item, index) => {
								if (item != '') {
									let datastr = this.cYear + '-' + this.cMonth + '-' + item.date
									item.datastr = datastr
									item.nl = ''
								}
							});
							let ycdata = dqnew.concat(netnew)
							//月经期
							if (data.actualDates != null) {
								let yjnew = data.actualDates.
								filter(item => this.$common.parseTime(new Date(item), '{y}-{m}') === this.$common.parseTime(
									new Date(this.currentDayIn), '{y}-{m}'));
								yjnew.forEach(yjarr => {
									let yjkey = this.days.findIndex(yjdata => yjdata?.datastr === yjarr)
									// 1.月经期 2.预测期 3.易孕期
									this.days[yjkey].nl = '1'
								})
								//预测期
								let a=new Set(yjnew);
								let b=new Set(ycdata);
								let sjdata = Array.from(new Set([...b].filter(x => !a.has(x))));
								sjdata.forEach(sjarr => {
									let yckey = this.days.findIndex(sjdata => sjdata?.datastr === sjarr)
									this.days[yckey].nl = '2'
								});
							}else{
								//预测期
								ycdata.forEach(ycarr => {
									let yckey = this.days.findIndex(ycdata => ycdata?.datastr === ycarr)
									this.days[yckey].nl = '2'
								});
							}
							//易孕期
							prnew.forEach(prarr => {
								let prkey = this.days.findIndex(prdata => prdata?.datastr === prarr)
								this.days[prkey].nl = '3'
							})
						}
					}
				})
			},
			initTime: function() {
				if (this.currentDayIn == '') {
					var dateObj = new Date();
					this.cYear = Number(dateObj.getFullYear());
					this.cMonth = Number(dateObj.getMonth() + 1);
					this.cMonthStr = this.cMonth < 10 ? '0' + this.cMonth : this.cMonth;
					this.cDay = dateObj.getDate();
					this.cDay = this.cDay < 10 ? '0' + this.cDay : this.cDay;
					this.currentDayIn = this.cYear + '-' + this.cMonthStr + '-' + this.cDay;
					this.changeMonth();
				} else {
					var dates = this.currentDayIn.split(' ');
					if (!dates[1]) {
						dates[1] = '';
					}
					var dayArr = dates[0].split('-');
					this.cYear = Number(dayArr[0]);
					this.cMonth = dayArr[1];
					this.cDay = dayArr[2];
					var reg = new RegExp('^0[0-9]+$');
					if (reg.test(this.cMonth)) {
						this.cMonth = this.cMonth.substr(1, 1);
					}
					this.cMonth = Number(this.cMonth);
					this.cMonthStr = this.cMonth < 10 ? '0' + this.cMonth : this.cMonth;
					this.currentDayIn = dates[0];
					this.currentTimeIn = dates[1];
					this.changeMonth();
				}
			},
			changeMonth: function() {
				var daysList = [];
				var days = this.getDaysInOneMonth();
				var startWeek = this.getDay();
				var forSteps = 0;
				for (var i = (0 - startWeek); i < days; i++) {
					if (i >= 0) {
						daysList[forSteps] = {
							date: i >= 9 ? i + 1 : '0' + (i + 1),
							nl: ''
						};
						daysList[forSteps].nl = guiCalendar.getLunarInfo(this.cYear, this.cMonth, i + 1);
					} else {
						daysList[forSteps] = '';
					}
					forSteps++;
				}
				//当月日历数组
				this.days = daysList;
			},
			getDaysInOneMonth: function() {
				var d = new Date(this.cYear, this.cMonth, 0);
				return d.getDate();
			},
			getDay: function() {
				var d = new Date(this.cYear, this.cMonth - 1, 0);
				return d.getDay();
			},
			selectDate: function(e) {
				this.currentDayIn = e.detail.value;
				this.stardata = e.detail.value;
				this.initTime();
				this.$emit('selectDate', e.detail.value);
			},
			chooseDate: function(sedDate) {
				this.currentDayIn = sedDate;
				this.stardata = sedDate;
				this.$emit('chooseDate', sedDate);
			},
			scheduleTap: function(e) {
				var id = e.currentTarget.dataset.id;
				this.$emit('scheduleTap', id);
			},
			gotoToday: function() {
				var dateObj = new Date();
				this.cYear = Number(dateObj.getFullYear());
				this.cMonth = Number(dateObj.getMonth() + 1);
				this.cMonthStr = this.cMonth < 10 ? '0' + this.cMonth : this.cMonth;
				this.cDay = dateObj.getDate();
				this.cDay = this.cDay < 10 ? '0' + this.cDay : this.cDay;
				this.currentDayIn = this.cYear + '-' + this.cMonthStr + '-' + this.cDay;
				this.changeMonth();
				this.$emit('toToday', this.currentDayIn);
				uni.setStorageSync('today', this.currentDayIn)
				this.getList(this.currentDayIn)
			}
		}
	}
</script>
<style scoped>
	.iocn {
		display: block;
		width: 20rpx;
		height: 20rpx;
		border-radius: 20rpx;
		margin-right: 10rpx;
	}

	.bg-pink {
		background-color: #ffd3f2;
	}

	.bg-purple {
		background-color: #b390ff;
	}

	.bg-plum {
		background-color: #ff3078 !important;
		color: #FFFFFF !important;
	}

	.bg-gran {
		background-color: #F8F8F8;
	}

	.activeBgColor {
		background-color: #008AFF !important;
		/* border: 6rpx solid #008AFF !important; */
	}

	.gui-schedule-wrap {
		width: 690rpx;
	}

	.gui-schedule-header-date {
		height: 88rpx;
		line-height: 88rpx;
		color: #2B2E3D;
		font-size: 32rpx;
	}

	.gui-schedule-7item {
		width: 98rpx;
		margin-bottom: 22rpx;
		position: relative;
	}

	.gui-schedule-weeks {
		width: 98rpx;
		height: 88rpx;
		font-size: 26rpx;
		line-height: 88rpx;
		text-align: center;
	}

	.gui-date-ditems {
		width: 50rpx;
		height: 50rpx;
		border-radius: 50rpx;
	}

	.gui-d-current-txt {
		color: #FFFFFF !important;
	}

	.gui-d-current-view {
		border: 1px solid ##008AFF !important;
	}

	.gui-date-day {
		height: 32rpx;
		line-height: 32rpx;
		text-align: center;
		font-size: 26rpx;
	}

	.gui-date-nl {
		height: 24rpx;
		line-height: 26rpx;
		color: #888888;
		font-size: 20rpx;
		text-align: center;
	}

	.gui-schedule-line {
		height: 20rpx;
		border-color: #F8F8F8;
	}

	.gui-schedule-time-list {
		margin-top: 20rpx;
	}

	.gui-schedule-timer {
		width: 88rpx;
		font-size: 22rpx;
		line-height: 36rpx;
	}

	.gui-schedule-body {
		width: 200rpx;
		flex: 1;
		border-color: #F8F8F8;
		margin-top: 15rpx;
	}

	.gui-schedules {
		padding: 10rpx;
		line-height: 30rpx;
		font-size: 22rpx;
		margin-top: 15rpx;
		border-radius: 8rpx;
	}

	.gui-schedule-time-list-wrap {
		padding: 20rpx;
	}

	.gui-schedule-today {
		line-height: 50rpx;
		height: 50rpx;
		font-size: 22rpx;
		color: #828282;
		padding-left: 20rpx;
		padding-right: 20rpx;
		border-color: #F1F2F3;
	}

	.gui-schedule-point {
		width: 82rpx;
		height: 82rpx;
		border-radius: 82rpx;
		background-color: #FF0036;
		position: absolute;
		top: 0rpx;
		left: 8rpx;
	}
</style>
