@charset "UTF-8";
.fs-10 {
  font-size: 10rpx; }

.fs-12 {
  font-size: 12rpx; }

.fs-14 {
  font-size: 14rpx; }

.fs-16 {
  font-size: 16rpx; }

.fs-20 {
  font-size: 20rpx; }

.fs-24 {
  font-size: 24rpx; }
.fs-26 {
  font-size: 26rpx; }
.fs-28 {
  font-size: 28rpx; }

.fs-30 {
  font-size: 30rpx; }

.fs-32 {
  font-size: 32rpx; }

.fs-36 {
  font-size: 36rpx; }

.fs-40 {
  font-size: 40rpx; }

.fs-50 {
  font-size: 50rpx; }

.fs-60 {
  font-size: 60rpx; }
.text-zhuti{
	color: #C59F79;
}
.zhuti-border{border-style:solid; border-width:1rpx; border-color:#C59F79;}
.text-zhutis{
	color: #714e35;
}
.bg-zhuti{
	background-color: #C59F79;
}
.text-orange {
  color: #F68512; }
  
.text-blues {
  color: #007aff; }

.bg-orange {
  background-color: #F68512; }

.bg-blues {
  background-color: #007aff; }

.text-white {
  color: #fff !important; }

.bg-white {
  background-color: #fff; }

.text-black {
  color: #111; }

.bg-black {
  background-color: #111; }

.text-black-6 {
  color: #666; }

.bg-black-6 {
  background-color: #666; }

.text-red {
  color: #F82020; }

.bg-red {
  background-color: #F82020; }
.bg-red2 {
  background-color: #F66829; }
.text-grey-b2 {
  color: #b2b2b2; }

.bg-grey-b2 {
  background-color: #b2b2b2; }

.text-grey-ee {
  color: #eee; }

.bg-grey-ee {
  background-color: #eee; }

.text-grey-f9 {
  color: #f9f9f9; }

.bg-grey-f9 {
  background-color: #f9f9f9; }

.text-grey-dc {
  color: #dcdcdc; }

.bg-grey-dc {
  background-color: #dcdcdc; }

.text-grey-74 {
  color: #747474; }

.bg-grey-74 {
  background-color: #747474; }

.text-blue {
  color: #4088ff; }

.bg-blue {
  background-color: #4088ff; }

.text-yellow {
  color: #FFFF00; }

.bg-yellow {
  background-color: #FFFF00; }

.text-egg {
  color: #f3cfa9; }

.bg-egg {
  background-color: #f3cfa9; }

.text-egg-white {
  color: #fff8ed; }

.bg-egg-white {
  background-color: #fff8ed; }

.text-green {
  color: #00baad; }

.bg-green {
  background-color: #00baad; }

.text-green-light {
  color: #cdead7; }

.bg-green-light {
  background-color: #cdead7; }

.text-green-white {
  color: #f4fdf8; }

.bg-green-white {
  background-color: #f4fdf8; }

.text-purple {
  color: #d46fe5; }

.bg-purple {
  background-color: #d46fe5; }

.text-left {
  text-align: left; }

.text-right {
  text-align: right; }

.text-center {
  text-align: center; }

.text-justify {
  text-align: justify; }

.w-10 {
  width: 10%; }

.w-20 {
  width: 20%; }

.w-25 {
  width: 25%; }

.w-30 {
  width: 30%; }

.w-40 {
  width: 40%; }

.w-50 {
  width: 50%; }

.w-60 {
  width: 60%; }

.w-70 {
  width: 70%; }

.w-80 {
  width: 80%; }

.w-90 {
  width: 90%; }

.w-100 {
  width: 100%; }

.w5 {
  width: 5rpx; }

.b-radius-5 {
  border-radius: 5rpx; }

.w10 {
  width: 10rpx; }

.b-radius-10 {
  border-radius: 10rpx; }

.w15 {
  width: 15rpx; }

.b-radius-15 {
  border-radius: 15rpx; }

.w20 {
  width: 20rpx; }

.b-radius-20 {
  border-radius: 20rpx; }

.w30 {
  width: 30rpx; }

.b-radius-30 {
  border-radius: 30rpx; }

.w40 {
  width: 40rpx; }

.b-radius-40 {
  border-radius: 40rpx; }

.w50 {
  width: 50rpx; }

.b-radius-50 {
  border-radius: 50rpx; }

.w60 {
  width: 60rpx; }

.b-radius-60 {
  border-radius: 60rpx; }

.w70 {
  width: 70rpx; }

.b-radius-70 {
  border-radius: 70rpx; }

.w80 {
  width: 80rpx; }

.b-radius-80 {
  border-radius: 80rpx; }

.w90 {
  width: 90rpx; }

.b-radius-90 {
  border-radius: 90rpx; }

.w100 {
  width: 100rpx; }

.b-radius-100 {
  border-radius: 100rpx; }

.w120 {
  width: 120rpx; }

.b-radius-120 {
  border-radius: 120rpx; }

.w150 {
  width: 150rpx; }

.b-radius-150 {
  border-radius: 150rpx; }

.w200 {
  width: 200rpx; }
	
.w300 {
  width: 300rpx; }

.b-radius-200 {
  border-radius: 200rpx; }

.h-10 {
  height: 10%; }

.h-20 {
  height: 20%; }

.h-25 {
  height: 25%; }

.h-30 {
  height: 30%; }

.h-40 {
  height: 40%; }

.h-50 {
  height: 50%; }

.h-60 {
  height: 60%; }

.h-70 {
  height: 70%; }

.h-80 {
  height: 80%; }

.h-90 {
  height: 90%; }

.h-100 {
  height: 100%; }

.h5 {
  height: 5rpx; }

.b-radius-5 {
  border-radius: 5rpx; }

.h10 {
  height: 10rpx; }

.b-radius-10 {
  border-radius: 10rpx; }

.h15 {
  height: 15rpx; }

.b-radius-15 {
  border-radius: 15rpx; }

.h20 {
  height: 20rpx; }

.b-radius-20 {
  border-radius: 20rpx; }

.h30 {
  height: 30rpx; }

.b-radius-30 {
  border-radius: 30rpx; }

.h40 {
  height: 40rpx; }

.b-radius-40 {
  border-radius: 40rpx; }

.h50 {
  height: 50rpx; }

.b-radius-50 {
  border-radius: 50rpx; }

.h60 {
  height: 60rpx; }

.b-radius-60 {
  border-radius: 60rpx; }

.h70 {
  height: 70rpx; }

.b-radius-70 {
  border-radius: 70rpx; }

.h80 {
  height: 80rpx; }

.b-radius-80 {
  border-radius: 80rpx; }

.h90 {
  height: 90rpx; }

.b-radius-90 {
  border-radius: 90rpx; }

.h100 {
  height: 100rpx; }

.b-radius-100 {
  border-radius: 100rpx; }

.h120 {
  height: 120rpx; }

.b-radius-120 {
  border-radius: 120rpx; }

.h150 {
  height: 150rpx; }

.b-radius-150 {
  border-radius: 150rpx; }

.h200 {
  height: 200rpx; }
.h300 {
  height: 300rpx; }
.h400 {
	height: 400rpx; }
.b-radius-200 {
  border-radius: 200rpx; }

.mt-5 {
  margin-top: 5rpx; }

.m-5 {
  margin: 5rpx; }

.mx-5 {
  margin-left: 5rpx;
  margin-right: 5rpx; }

.my-5 {
  margin-top: 5rpx;
  margin-bottom: 5rpx; }

.mt-10 {
  margin-top: 10rpx; }

.m-10 {
  margin: 10rpx; }

.mx-10 {
  margin-left: 10rpx;
  margin-right: 10rpx; }

.my-10 {
  margin-top: 10rpx;
  margin-bottom: 10rpx; }

.mt-15 {
  margin-top: 15rpx; }

.m-15 {
  margin: 15rpx; }

.mx-15 {
  margin-left: 15rpx;
  margin-right: 15rpx; }

.my-15 {
  margin-top: 15rpx;
  margin-bottom: 15rpx; }

.mt-20 {
  margin-top: 20rpx; }

.m-20 {
  margin: 20rpx; }

.mx-20 {
  margin-left: 20rpx;
  margin-right: 20rpx; }

.my-20 {
  margin-top: 20rpx;
  margin-bottom: 20rpx; }

.mt-30 {
  margin-top: 30rpx; }

.m-30 {
  margin: 30rpx; }

.mx-30 {
  margin-left: 30rpx;
  margin-right: 30rpx; }

.my-30 {
  margin-top: 30rpx;
  margin-bottom: 30rpx; }

.mt-40 {
  margin-top: 40rpx; }

.m-40 {
  margin: 40rpx; }

.mx-40 {
  margin-left: 40rpx;
  margin-right: 40rpx; }

.my-40 {
  margin-top: 40rpx;
  margin-bottom: 40rpx; }

.mt-50 {
  margin-top: 50rpx; }

.m-50 {
  margin: 50rpx; }

.mx-50 {
  margin-left: 50rpx;
  margin-right: 50rpx; }

.my-50 {
  margin-top: 50rpx;
  margin-bottom: 50rpx; }

.mt-60 {
  margin-top: 60rpx; }

.m-60 {
  margin: 60rpx; }

.mx-60 {
  margin-left: 60rpx;
  margin-right: 60rpx; }

.my-60 {
  margin-top: 60rpx;
  margin-bottom: 60rpx; }

.mt-70 {
  margin-top: 70rpx; }

.m-70 {
  margin: 70rpx; }

.mx-70 {
  margin-left: 70rpx;
  margin-right: 70rpx; }

.my-70 {
  margin-top: 70rpx;
  margin-bottom: 70rpx; }

.mt-80 {
  margin-top: 80rpx; }

.m-80 {
  margin: 80rpx; }

.mx-80 {
  margin-left: 80rpx;
  margin-right: 80rpx; }

.my-80 {
  margin-top: 80rpx;
  margin-bottom: 80rpx; }

.mt-90 {
  margin-top: 90rpx; }

.m-90 {
  margin: 90rpx; }

.mx-90 {
  margin-left: 90rpx;
  margin-right: 90rpx; }

.my-90 {
  margin-top: 90rpx;
  margin-bottom: 90rpx; }

.mt-100 {
  margin-top: 100rpx; }

.m-100 {
  margin: 100rpx; }

.mx-100 {
  margin-left: 100rpx;
  margin-right: 100rpx; }

.my-100 {
  margin-top: 100rpx;
  margin-bottom: 100rpx; }

.mt-120 {
  margin-top: 120rpx; }

.m-120 {
  margin: 120rpx; }

.mx-120 {
  margin-left: 120rpx;
  margin-right: 120rpx; }

.my-120 {
  margin-top: 120rpx;
  margin-bottom: 120rpx; }

.mt-150 {
  margin-top: 150rpx; }

.m-150 {
  margin: 150rpx; }

.mx-150 {
  margin-left: 150rpx;
  margin-right: 150rpx; }

.my-150 {
  margin-top: 150rpx;
  margin-bottom: 150rpx; }

.mt-200 {
  margin-top: 200rpx; }

.m-200 {
  margin: 200rpx; }

.mx-200 {
  margin-left: 200rpx;
  margin-right: 200rpx; }

.my-200 {
  margin-top: 200rpx;
  margin-bottom: 200rpx; }

.mb-5 {
  margin-bottom: 5rpx; }

.m-5 {
  margin: 5rpx; }

.mx-5 {
  margin-left: 5rpx;
  margin-right: 5rpx; }

.my-5 {
  margin-top: 5rpx;
  margin-bottom: 5rpx; }

.mb-10 {
  margin-bottom: 10rpx; }

.m-10 {
  margin: 10rpx; }

.mx-10 {
  margin-left: 10rpx;
  margin-right: 10rpx; }

.my-10 {
  margin-top: 10rpx;
  margin-bottom: 10rpx; }

.mb-15 {
  margin-bottom: 15rpx; }

.m-15 {
  margin: 15rpx; }

.mx-15 {
  margin-left: 15rpx;
  margin-right: 15rpx; }

.my-15 {
  margin-top: 15rpx;
  margin-bottom: 15rpx; }

.mb-20 {
  margin-bottom: 20rpx; }

.m-20 {
  margin: 20rpx; }

.mx-20 {
  margin-left: 20rpx;
  margin-right: 20rpx; }

.my-20 {
  margin-top: 20rpx;
  margin-bottom: 20rpx; }

.mb-30 {
  margin-bottom: 30rpx; }

.m-30 {
  margin: 30rpx; }

.mx-30 {
  margin-left: 30rpx;
  margin-right: 30rpx; }

.my-30 {
  margin-top: 30rpx;
  margin-bottom: 30rpx; }

.mb-40 {
  margin-bottom: 40rpx; }

.m-40 {
  margin: 40rpx; }

.mx-40 {
  margin-left: 40rpx;
  margin-right: 40rpx; }

.my-40 {
  margin-top: 40rpx;
  margin-bottom: 40rpx; }

.mb-50 {
  margin-bottom: 50rpx; }

.m-50 {
  margin: 50rpx; }

.mx-50 {
  margin-left: 50rpx;
  margin-right: 50rpx; }

.my-50 {
  margin-top: 50rpx;
  margin-bottom: 50rpx; }

.mb-60 {
  margin-bottom: 60rpx; }

.m-60 {
  margin: 60rpx; }

.mx-60 {
  margin-left: 60rpx;
  margin-right: 60rpx; }

.my-60 {
  margin-top: 60rpx;
  margin-bottom: 60rpx; }

.mb-70 {
  margin-bottom: 70rpx; }

.m-70 {
  margin: 70rpx; }

.mx-70 {
  margin-left: 70rpx;
  margin-right: 70rpx; }

.my-70 {
  margin-top: 70rpx;
  margin-bottom: 70rpx; }

.mb-80 {
  margin-bottom: 80rpx; }

.m-80 {
  margin: 80rpx; }

.mx-80 {
  margin-left: 80rpx;
  margin-right: 80rpx; }

.my-80 {
  margin-top: 80rpx;
  margin-bottom: 80rpx; }

.mb-90 {
  margin-bottom: 90rpx; }

.m-90 {
  margin: 90rpx; }

.mx-90 {
  margin-left: 90rpx;
  margin-right: 90rpx; }

.my-90 {
  margin-top: 90rpx;
  margin-bottom: 90rpx; }

.mb-100 {
  margin-bottom: 100rpx; }

.m-100 {
  margin: 100rpx; }

.mx-100 {
  margin-left: 100rpx;
  margin-right: 100rpx; }

.my-100 {
  margin-top: 100rpx;
  margin-bottom: 100rpx; }

.mb-120 {
  margin-bottom: 120rpx; }

.m-120 {
  margin: 120rpx; }

.mx-120 {
  margin-left: 120rpx;
  margin-right: 120rpx; }

.my-120 {
  margin-top: 120rpx;
  margin-bottom: 120rpx; }

.mb-150 {
  margin-bottom: 150rpx; }

.m-150 {
  margin: 150rpx; }

.mx-150 {
  margin-left: 150rpx;
  margin-right: 150rpx; }

.my-150 {
  margin-top: 150rpx;
  margin-bottom: 150rpx; }

.mb-200 {
  margin-bottom: 200rpx; }

.m-200 {
  margin: 200rpx; }

.mx-200 {
  margin-left: 200rpx;
  margin-right: 200rpx; }

.my-200 {
  margin-top: 200rpx;
  margin-bottom: 200rpx; }

.mr-5 {
  margin-right: 5rpx; }

.m-5 {
  margin: 5rpx; }

.mx-5 {
  margin-left: 5rpx;
  margin-right: 5rpx; }

.my-5 {
  margin-top: 5rpx;
  margin-bottom: 5rpx; }

.mr-10 {
  margin-right: 10rpx; }

.m-10 {
  margin: 10rpx; }

.mx-10 {
  margin-left: 10rpx;
  margin-right: 10rpx; }

.my-10 {
  margin-top: 10rpx;
  margin-bottom: 10rpx; }

.mr-15 {
  margin-right: 15rpx; }

.m-15 {
  margin: 15rpx; }

.mx-15 {
  margin-left: 15rpx;
  margin-right: 15rpx; }

.my-15 {
  margin-top: 15rpx;
  margin-bottom: 15rpx; }

.mr-20 {
  margin-right: 20rpx; }

.m-20 {
  margin: 20rpx; }

.mx-20 {
  margin-left: 20rpx;
  margin-right: 20rpx; }

.my-20 {
  margin-top: 20rpx;
  margin-bottom: 20rpx; }

.mr-30 {
  margin-right: 30rpx; }

.m-30 {
  margin: 30rpx; }

.mx-30 {
  margin-left: 30rpx;
  margin-right: 30rpx; }

.my-30 {
  margin-top: 30rpx;
  margin-bottom: 30rpx; }

.mr-40 {
  margin-right: 40rpx; }

.m-40 {
  margin: 40rpx; }

.mx-40 {
  margin-left: 40rpx;
  margin-right: 40rpx; }

.my-40 {
  margin-top: 40rpx;
  margin-bottom: 40rpx; }

.mr-50 {
  margin-right: 50rpx; }

.m-50 {
  margin: 50rpx; }

.mx-50 {
  margin-left: 50rpx;
  margin-right: 50rpx; }

.my-50 {
  margin-top: 50rpx;
  margin-bottom: 50rpx; }

.mr-60 {
  margin-right: 60rpx; }

.m-60 {
  margin: 60rpx; }

.mx-60 {
  margin-left: 60rpx;
  margin-right: 60rpx; }

.my-60 {
  margin-top: 60rpx;
  margin-bottom: 60rpx; }

.mr-70 {
  margin-right: 70rpx; }

.m-70 {
  margin: 70rpx; }

.mx-70 {
  margin-left: 70rpx;
  margin-right: 70rpx; }

.my-70 {
  margin-top: 70rpx;
  margin-bottom: 70rpx; }

.mr-80 {
  margin-right: 80rpx; }

.m-80 {
  margin: 80rpx; }

.mx-80 {
  margin-left: 80rpx;
  margin-right: 80rpx; }

.my-80 {
  margin-top: 80rpx;
  margin-bottom: 80rpx; }

.mr-90 {
  margin-right: 90rpx; }

.m-90 {
  margin: 90rpx; }

.mx-90 {
  margin-left: 90rpx;
  margin-right: 90rpx; }

.my-90 {
  margin-top: 90rpx;
  margin-bottom: 90rpx; }

.mr-100 {
  margin-right: 100rpx; }

.m-100 {
  margin: 100rpx; }

.mx-100 {
  margin-left: 100rpx;
  margin-right: 100rpx; }

.my-100 {
  margin-top: 100rpx;
  margin-bottom: 100rpx; }

.mr-120 {
  margin-right: 120rpx; }

.m-120 {
  margin: 120rpx; }

.mx-120 {
  margin-left: 120rpx;
  margin-right: 120rpx; }

.my-120 {
  margin-top: 120rpx;
  margin-bottom: 120rpx; }

.mr-150 {
  margin-right: 150rpx; }

.m-150 {
  margin: 150rpx; }

.mx-150 {
  margin-left: 150rpx;
  margin-right: 150rpx; }

.my-150 {
  margin-top: 150rpx;
  margin-bottom: 150rpx; }

.mr-200 {
  margin-right: 200rpx; }

.m-200 {
  margin: 200rpx; }

.mx-200 {
  margin-left: 200rpx;
  margin-right: 200rpx; }

.my-200 {
  margin-top: 200rpx;
  margin-bottom: 200rpx; }

.ml-5 {
  margin-left: 5rpx; }

.m-5 {
  margin: 5rpx; }

.mx-5 {
  margin-left: 5rpx;
  margin-right: 5rpx; }

.my-5 {
  margin-top: 5rpx;
  margin-bottom: 5rpx; }

.ml-10 {
  margin-left: 10rpx; }

.m-10 {
  margin: 10rpx; }

.mx-10 {
  margin-left: 10rpx;
  margin-right: 10rpx; }

.my-10 {
  margin-top: 10rpx;
  margin-bottom: 10rpx; }

.ml-15 {
  margin-left: 15rpx; }

.m-15 {
  margin: 15rpx; }

.mx-15 {
  margin-left: 15rpx;
  margin-right: 15rpx; }

.my-15 {
  margin-top: 15rpx;
  margin-bottom: 15rpx; }

.ml-20 {
  margin-left: 20rpx; }

.m-20 {
  margin: 20rpx; }

.mx-20 {
  margin-left: 20rpx;
  margin-right: 20rpx; }

.my-20 {
  margin-top: 20rpx;
  margin-bottom: 20rpx; }

.ml-30 {
  margin-left: 30rpx; }

.m-30 {
  margin: 30rpx; }

.mx-30 {
  margin-left: 30rpx;
  margin-right: 30rpx; }

.my-30 {
  margin-top: 30rpx;
  margin-bottom: 30rpx; }

.ml-40 {
  margin-left: 40rpx; }

.m-40 {
  margin: 40rpx; }

.mx-40 {
  margin-left: 40rpx;
  margin-right: 40rpx; }

.my-40 {
  margin-top: 40rpx;
  margin-bottom: 40rpx; }

.ml-50 {
  margin-left: 50rpx; }

.m-50 {
  margin: 50rpx; }

.mx-50 {
  margin-left: 50rpx;
  margin-right: 50rpx; }

.my-50 {
  margin-top: 50rpx;
  margin-bottom: 50rpx; }

.ml-60 {
  margin-left: 60rpx; }

.m-60 {
  margin: 60rpx; }

.mx-60 {
  margin-left: 60rpx;
  margin-right: 60rpx; }

.my-60 {
  margin-top: 60rpx;
  margin-bottom: 60rpx; }

.ml-70 {
  margin-left: 70rpx; }

.m-70 {
  margin: 70rpx; }

.mx-70 {
  margin-left: 70rpx;
  margin-right: 70rpx; }

.my-70 {
  margin-top: 70rpx;
  margin-bottom: 70rpx; }

.ml-80 {
  margin-left: 80rpx; }

.m-80 {
  margin: 80rpx; }

.mx-80 {
  margin-left: 80rpx;
  margin-right: 80rpx; }

.my-80 {
  margin-top: 80rpx;
  margin-bottom: 80rpx; }

.ml-90 {
  margin-left: 90rpx; }

.m-90 {
  margin: 90rpx; }

.mx-90 {
  margin-left: 90rpx;
  margin-right: 90rpx; }

.my-90 {
  margin-top: 90rpx;
  margin-bottom: 90rpx; }

.ml-100 {
  margin-left: 100rpx; }

.m-100 {
  margin: 100rpx; }

.mx-100 {
  margin-left: 100rpx;
  margin-right: 100rpx; }

.my-100 {
  margin-top: 100rpx;
  margin-bottom: 100rpx; }

.ml-120 {
  margin-left: 120rpx; }

.m-120 {
  margin: 120rpx; }

.mx-120 {
  margin-left: 120rpx;
  margin-right: 120rpx; }

.my-120 {
  margin-top: 120rpx;
  margin-bottom: 120rpx; }

.ml-150 {
  margin-left: 150rpx; }

.m-150 {
  margin: 150rpx; }

.mx-150 {
  margin-left: 150rpx;
  margin-right: 150rpx; }

.my-150 {
  margin-top: 150rpx;
  margin-bottom: 150rpx; }

.ml-200 {
  margin-left: 200rpx; }

.m-200 {
  margin: 200rpx; }

.mx-200 {
  margin-left: 200rpx;
  margin-right: 200rpx; }

.my-200 {
  margin-top: 200rpx;
  margin-bottom: 200rpx; }

.pt-5 {
  padding-top: 5rpx; }

.p-5 {
  padding: 5rpx; }

.px-5 {
  padding-left: 5rpx;
  padding-right: 5rpx; }

.py-5 {
  padding-top: 5rpx;
  padding-bottom: 5rpx; }

.pt-10 {
  padding-top: 10rpx; }

.p-10 {
  padding: 10rpx; }

.px-10 {
  padding-left: 10rpx;
  padding-right: 10rpx; }

.py-10 {
  padding-top: 10rpx;
  padding-bottom: 10rpx; }

.pt-15 {
  padding-top: 15rpx; }

.p-15 {
  padding: 15rpx; }

.px-15 {
  padding-left: 15rpx;
  padding-right: 15rpx; }

.py-15 {
  padding-top: 15rpx;
  padding-bottom: 15rpx; }

.pt-20 {
  padding-top: 20rpx; }

.p-20 {
  padding: 20rpx; }

.px-20 {
  padding-left: 20rpx;
  padding-right: 20rpx; }

.py-20 {
  padding-top: 20rpx;
  padding-bottom: 20rpx; }

.pt-30 {
  padding-top: 30rpx; }

.p-30 {
  padding: 30rpx; }

.px-30 {
  padding-left: 30rpx;
  padding-right: 30rpx; }

.py-30 {
  padding-top: 30rpx;
  padding-bottom: 30rpx; }

.pt-40 {
  padding-top: 40rpx; }

.p-40 {
  padding: 40rpx; }

.px-40 {
  padding-left: 40rpx;
  padding-right: 40rpx; }

.py-40 {
  padding-top: 40rpx;
  padding-bottom: 40rpx; }

.pt-50 {
  padding-top: 50rpx; }

.p-50 {
  padding: 50rpx; }

.px-50 {
  padding-left: 50rpx;
  padding-right: 50rpx; }

.py-50 {
  padding-top: 50rpx;
  padding-bottom: 50rpx; }

.pt-60 {
  padding-top: 60rpx; }

.p-60 {
  padding: 60rpx; }

.px-60 {
  padding-left: 60rpx;
  padding-right: 60rpx; }

.py-60 {
  padding-top: 60rpx;
  padding-bottom: 60rpx; }

.pt-70 {
  padding-top: 70rpx; }

.p-70 {
  padding: 70rpx; }

.px-70 {
  padding-left: 70rpx;
  padding-right: 70rpx; }

.py-70 {
  padding-top: 70rpx;
  padding-bottom: 70rpx; }

.pt-80 {
  padding-top: 80rpx; }

.p-80 {
  padding: 80rpx; }

.px-80 {
  padding-left: 80rpx;
  padding-right: 80rpx; }

.py-80 {
  padding-top: 80rpx;
  padding-bottom: 80rpx; }

.pt-90 {
  padding-top: 90rpx; }

.p-90 {
  padding: 90rpx; }

.px-90 {
  padding-left: 90rpx;
  padding-right: 90rpx; }

.py-90 {
  padding-top: 90rpx;
  padding-bottom: 90rpx; }

.pt-100 {
  padding-top: 100rpx; }

.p-100 {
  padding: 100rpx; }

.px-100 {
  padding-left: 100rpx;
  padding-right: 100rpx; }

.py-100 {
  padding-top: 100rpx;
  padding-bottom: 100rpx; }

.pt-120 {
  padding-top: 120rpx; }

.p-120 {
  padding: 120rpx; }

.px-120 {
  padding-left: 120rpx;
  padding-right: 120rpx; }

.py-120 {
  padding-top: 120rpx;
  padding-bottom: 120rpx; }

.pt-150 {
  padding-top: 150rpx; }

.p-150 {
  padding: 150rpx; }

.px-150 {
  padding-left: 150rpx;
  padding-right: 150rpx; }

.py-150 {
  padding-top: 150rpx;
  padding-bottom: 150rpx; }

.pt-200 {
  padding-top: 200rpx; }

.p-200 {
  padding: 200rpx; }

.px-200 {
  padding-left: 200rpx;
  padding-right: 200rpx; }

.py-200 {
  padding-top: 200rpx;
  padding-bottom: 200rpx; }

.pb-5 {
  padding-bottom: 5rpx; }

.p-5 {
  padding: 5rpx; }

.px-5 {
  padding-left: 5rpx;
  padding-right: 5rpx; }

.py-5 {
  padding-top: 5rpx;
  padding-bottom: 5rpx; }

.pb-10 {
  padding-bottom: 10rpx; }

.p-10 {
  padding: 10rpx; }

.px-10 {
  padding-left: 10rpx;
  padding-right: 10rpx; }

.py-10 {
  padding-top: 10rpx;
  padding-bottom: 10rpx; }

.pb-15 {
  padding-bottom: 15rpx; }

.p-15 {
  padding: 15rpx; }

.px-15 {
  padding-left: 15rpx;
  padding-right: 15rpx; }

.py-15 {
  padding-top: 15rpx;
  padding-bottom: 15rpx; }

.pb-20 {
  padding-bottom: 20rpx; }

.p-20 {
  padding: 20rpx; }

.px-20 {
  padding-left: 20rpx;
  padding-right: 20rpx; }

.py-20 {
  padding-top: 20rpx;
  padding-bottom: 20rpx; }

.pb-30 {
  padding-bottom: 30rpx; }

.p-30 {
  padding: 30rpx; }

.px-30 {
  padding-left: 30rpx;
  padding-right: 30rpx; }

.py-30 {
  padding-top: 30rpx;
  padding-bottom: 30rpx; }

.pb-40 {
  padding-bottom: 40rpx; }

.p-40 {
  padding: 40rpx; }

.px-40 {
  padding-left: 40rpx;
  padding-right: 40rpx; }

.py-40 {
  padding-top: 40rpx;
  padding-bottom: 40rpx; }

.pb-50 {
  padding-bottom: 50rpx; }

.p-50 {
  padding: 50rpx; }

.px-50 {
  padding-left: 50rpx;
  padding-right: 50rpx; }

.py-50 {
  padding-top: 50rpx;
  padding-bottom: 50rpx; }

.pb-60 {
  padding-bottom: 60rpx; }

.p-60 {
  padding: 60rpx; }

.px-60 {
  padding-left: 60rpx;
  padding-right: 60rpx; }

.py-60 {
  padding-top: 60rpx;
  padding-bottom: 60rpx; }

.pb-70 {
  padding-bottom: 70rpx; }

.p-70 {
  padding: 70rpx; }

.px-70 {
  padding-left: 70rpx;
  padding-right: 70rpx; }

.py-70 {
  padding-top: 70rpx;
  padding-bottom: 70rpx; }

.pb-80 {
  padding-bottom: 80rpx; }

.p-80 {
  padding: 80rpx; }

.px-80 {
  padding-left: 80rpx;
  padding-right: 80rpx; }

.py-80 {
  padding-top: 80rpx;
  padding-bottom: 80rpx; }

.pb-90 {
  padding-bottom: 90rpx; }

.p-90 {
  padding: 90rpx; }

.px-90 {
  padding-left: 90rpx;
  padding-right: 90rpx; }

.py-90 {
  padding-top: 90rpx;
  padding-bottom: 90rpx; }

.pb-100 {
  padding-bottom: 100rpx; }

.p-100 {
  padding: 100rpx; }

.px-100 {
  padding-left: 100rpx;
  padding-right: 100rpx; }

.py-100 {
  padding-top: 100rpx;
  padding-bottom: 100rpx; }

.pb-120 {
  padding-bottom: 120rpx; }

.p-120 {
  padding: 120rpx; }

.px-120 {
  padding-left: 120rpx;
  padding-right: 120rpx; }

.py-120 {
  padding-top: 120rpx;
  padding-bottom: 120rpx; }

.pb-150 {
  padding-bottom: 150rpx; }

.p-150 {
  padding: 150rpx; }

.px-150 {
  padding-left: 150rpx;
  padding-right: 150rpx; }

.py-150 {
  padding-top: 150rpx;
  padding-bottom: 150rpx; }

.pb-200 {
  padding-bottom: 200rpx; }

.p-200 {
  padding: 200rpx; }

.px-200 {
  padding-left: 200rpx;
  padding-right: 200rpx; }

.py-200 {
  padding-top: 200rpx;
  padding-bottom: 200rpx; }

.pr-5 {
  padding-right: 5rpx; }

.p-5 {
  padding: 5rpx; }

.px-5 {
  padding-left: 5rpx;
  padding-right: 5rpx; }

.py-5 {
  padding-top: 5rpx;
  padding-bottom: 5rpx; }

.pr-10 {
  padding-right: 10rpx; }

.p-10 {
  padding: 10rpx; }

.px-10 {
  padding-left: 10rpx;
  padding-right: 10rpx; }

.py-10 {
  padding-top: 10rpx;
  padding-bottom: 10rpx; }

.pr-15 {
  padding-right: 15rpx; }

.p-15 {
  padding: 15rpx; }

.px-15 {
  padding-left: 15rpx;
  padding-right: 15rpx; }

.py-15 {
  padding-top: 15rpx;
  padding-bottom: 15rpx; }

.pr-20 {
  padding-right: 20rpx; }

.p-20 {
  padding: 20rpx; }

.px-20 {
  padding-left: 20rpx;
  padding-right: 20rpx; }

.py-20 {
  padding-top: 20rpx;
  padding-bottom: 20rpx; }

.pr-30 {
  padding-right: 30rpx; }

.p-30 {
  padding: 30rpx; }

.px-30 {
  padding-left: 30rpx;
  padding-right: 30rpx; }

.py-30 {
  padding-top: 30rpx;
  padding-bottom: 30rpx; }

.pr-40 {
  padding-right: 40rpx; }

.p-40 {
  padding: 40rpx; }

.px-40 {
  padding-left: 40rpx;
  padding-right: 40rpx; }

.py-40 {
  padding-top: 40rpx;
  padding-bottom: 40rpx; }

.pr-50 {
  padding-right: 50rpx; }

.p-50 {
  padding: 50rpx; }

.px-50 {
  padding-left: 50rpx;
  padding-right: 50rpx; }

.py-50 {
  padding-top: 50rpx;
  padding-bottom: 50rpx; }

.pr-60 {
  padding-right: 60rpx; }

.p-60 {
  padding: 60rpx; }

.px-60 {
  padding-left: 60rpx;
  padding-right: 60rpx; }

.py-60 {
  padding-top: 60rpx;
  padding-bottom: 60rpx; }

.pr-70 {
  padding-right: 70rpx; }

.p-70 {
  padding: 70rpx; }

.px-70 {
  padding-left: 70rpx;
  padding-right: 70rpx; }

.py-70 {
  padding-top: 70rpx;
  padding-bottom: 70rpx; }

.pr-80 {
  padding-right: 80rpx; }

.p-80 {
  padding: 80rpx; }

.px-80 {
  padding-left: 80rpx;
  padding-right: 80rpx; }

.py-80 {
  padding-top: 80rpx;
  padding-bottom: 80rpx; }

.pr-90 {
  padding-right: 90rpx; }

.p-90 {
  padding: 90rpx; }

.px-90 {
  padding-left: 90rpx;
  padding-right: 90rpx; }

.py-90 {
  padding-top: 90rpx;
  padding-bottom: 90rpx; }

.pr-100 {
  padding-right: 100rpx; }

.p-100 {
  padding: 100rpx; }

.px-100 {
  padding-left: 100rpx;
  padding-right: 100rpx; }

.py-100 {
  padding-top: 100rpx;
  padding-bottom: 100rpx; }

.pr-120 {
  padding-right: 120rpx; }

.p-120 {
  padding: 120rpx; }

.px-120 {
  padding-left: 120rpx;
  padding-right: 120rpx; }

.py-120 {
  padding-top: 120rpx;
  padding-bottom: 120rpx; }

.pr-150 {
  padding-right: 150rpx; }

.p-150 {
  padding: 150rpx; }

.px-150 {
  padding-left: 150rpx;
  padding-right: 150rpx; }

.py-150 {
  padding-top: 150rpx;
  padding-bottom: 150rpx; }

.pr-200 {
  padding-right: 200rpx; }

.p-200 {
  padding: 200rpx; }

.px-200 {
  padding-left: 200rpx;
  padding-right: 200rpx; }

.py-200 {
  padding-top: 200rpx;
  padding-bottom: 200rpx; }

.pl-5 {
  padding-left: 5rpx; }

.p-5 {
  padding: 5rpx; }

.px-5 {
  padding-left: 5rpx;
  padding-right: 5rpx; }

.py-5 {
  padding-top: 5rpx;
  padding-bottom: 5rpx; }

.pl-10 {
  padding-left: 10rpx; }

.p-10 {
  padding: 10rpx; }

.px-10 {
  padding-left: 10rpx;
  padding-right: 10rpx; }

.py-10 {
  padding-top: 10rpx;
  padding-bottom: 10rpx; }

.pl-15 {
  padding-left: 15rpx; }

.p-15 {
  padding: 15rpx; }

.px-15 {
  padding-left: 15rpx;
  padding-right: 15rpx; }

.py-15 {
  padding-top: 15rpx;
  padding-bottom: 15rpx; }

.pl-20 {
  padding-left: 20rpx; }

.p-20 {
  padding: 20rpx; }

.px-20 {
  padding-left: 20rpx;
  padding-right: 20rpx; }

.py-20 {
  padding-top: 20rpx;
  padding-bottom: 20rpx; }

.pl-30 {
  padding-left: 30rpx; }

.p-30 {
  padding: 30rpx; }

.px-30 {
  padding-left: 30rpx;
  padding-right: 30rpx; }

.py-30 {
  padding-top: 30rpx;
  padding-bottom: 30rpx; }

.pl-40 {
  padding-left: 40rpx; }

.p-40 {
  padding: 40rpx; }

.px-40 {
  padding-left: 40rpx;
  padding-right: 40rpx; }

.py-40 {
  padding-top: 40rpx;
  padding-bottom: 40rpx; }

.pl-50 {
  padding-left: 50rpx; }

.p-50 {
  padding: 50rpx; }

.px-50 {
  padding-left: 50rpx;
  padding-right: 50rpx; }

.py-50 {
  padding-top: 50rpx;
  padding-bottom: 50rpx; }

.pl-60 {
  padding-left: 60rpx; }

.p-60 {
  padding: 60rpx; }

.px-60 {
  padding-left: 60rpx;
  padding-right: 60rpx; }

.py-60 {
  padding-top: 60rpx;
  padding-bottom: 60rpx; }

.pl-70 {
  padding-left: 70rpx; }

.p-70 {
  padding: 70rpx; }

.px-70 {
  padding-left: 70rpx;
  padding-right: 70rpx; }

.py-70 {
  padding-top: 70rpx;
  padding-bottom: 70rpx; }

.pl-80 {
  padding-left: 80rpx; }

.p-80 {
  padding: 80rpx; }

.px-80 {
  padding-left: 80rpx;
  padding-right: 80rpx; }

.py-80 {
  padding-top: 80rpx;
  padding-bottom: 80rpx; }

.pl-90 {
  padding-left: 90rpx; }

.p-90 {
  padding: 90rpx; }

.px-90 {
  padding-left: 90rpx;
  padding-right: 90rpx; }

.py-90 {
  padding-top: 90rpx;
  padding-bottom: 90rpx; }

.pl-100 {
  padding-left: 100rpx; }

.p-100 {
  padding: 100rpx; }

.px-100 {
  padding-left: 100rpx;
  padding-right: 100rpx; }

.py-100 {
  padding-top: 100rpx;
  padding-bottom: 100rpx; }

.pl-120 {
  padding-left: 120rpx; }

.p-120 {
  padding: 120rpx; }

.px-120 {
  padding-left: 120rpx;
  padding-right: 120rpx; }

.py-120 {
  padding-top: 120rpx;
  padding-bottom: 120rpx; }

.pl-150 {
  padding-left: 150rpx; }

.p-150 {
  padding: 150rpx; }

.px-150 {
  padding-left: 150rpx;
  padding-right: 150rpx; }

.py-150 {
  padding-top: 150rpx;
  padding-bottom: 150rpx; }

.pl-200 {
  padding-left: 200rpx; }

.p-200 {
  padding: 200rpx; }

.px-200 {
  padding-left: 200rpx;
  padding-right: 200rpx; }

.py-200 {
  padding-top: 200rpx;
  padding-bottom: 200rpx; }

.letter-space-2 {
  letter-spacing: 2rpx; }

.letter-space-4 {
  letter-spacing: 4rpx; }

.letter-space-6 {
  letter-spacing: 6rpx; }

.letter-space-8 {
  letter-spacing: 8rpx; }

.letter-space-10 {
  letter-spacing: 10rpx; }

.over-hidden {
  overflow: hidden; }

.over-scroll {
  overflow: scroll; }

.pos-absolute {
  position: absolute; }

.pos-relative {
  position: relative; }

.pos-fixed {
  position: fixed; }

.pos-sticky {
  position: sticky; }

.jc-start {
  justify-content: flex-start; }

.jc-end {
  justify-content: flex-end; }

.jc-center {
  justify-content: center; }

.jc-between {
  justify-content: space-between; }

.jc-around {
  justify-content: space-around; }

.ai-start {
  align-items: flex-start; }

.ai-end {
  align-items: flex-end; }

.ai-center {
  align-items: center; }

.ai-stretch {
  align-items: stretch; }

.box-size-border {
  box-sizing: border-box; }

.box-size-content {
  box-sizing: content-box; }

.ellipsis-1 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical; }

.ellipsis-2 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical; }

.ellipsis-3 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical; }

.ellipsis-4 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical; }

/* 圆形 */
.circle {
  border-radius: 50%; }

.border-b {
  border-bottom: 1rpx solid #ddd; 
}
.border-t {
  border-top: 1rpx solid #eee; 
}
.border-white {
  border: 1rpx solid #fff; }

/* 加粗 */
.font-bold {
  font-weight: bold; }

.card-shadow {
  box-shadow: 0rpx 5rpx 60rpx #eee; }

.d-iblock {
  display: inline-block; }

.d-flex {
  display: flex; }

.d-iflex {
  display: inline-flex; }

.flex-row {
  flex-direction: row; }

.flex-coloum {
  flex-direction: column; }

.flex-1 {
  flex: 1; }

.flex-wrap {
  flex-wrap: wrap; }
.add-btn{
	height:96rpx;
	font-size: 32rpx;
	line-height: 96rpx;
	border-radius: 100rpx;
}
button:after{
	border: 0 solid rgba(0,0,0,.2);
}
.bg-zt-orange{
	background-image: linear-gradient(90deg,#F58A58,#F9A078);
	color: #fff;
}
.bg-zt-orange-ds{
	background:#F58A58;
	color: #fff;
}
.text-zt-orange{
	color: #F58A58;
}

.box-shadows { box-shadow: 0rpx 5rpx 20rpx #e7e7e7; }
	