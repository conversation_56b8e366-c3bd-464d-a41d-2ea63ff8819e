<template>
	<!-- 页面主体 -->
	<view style="padding:20rpx 20rpx 10rpx 40rpx;">
		<form @submit="submit">
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">设备编码</text>
				<view class="gui-form-body">
					<input type="text" class="gui-form-input" style="width: 75%; float: left;" v-model="formData.iotSn"
						name="iotSn" placeholder="请输入设备编码" v-if="show" />
					<span v-else>{{ formData.iotSn || '' }}</span>
					<text class="gui-grids-icon gui-icons"
						style="display: inline; float: right;width: 15%; margin-right: 15rpx;" @click="startScan"
						v-if="show">&#xe61d;</text>
				</view>
			</view>
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">设备名称</text>
				<view class="gui-form-body">
					<input type="text" class="gui-form-input" v-model="formData.iotName" name="iotName"
						placeholder="请输入设备名称" v-if="show" />
					<span v-else>{{ formData.iotName || '' }}</span>
				</view>
			</view>
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">设备类型</text>
				<view class="gui-form-body">
					<view class="graceSelectMenuItem">
						<view class="graceSelectMenuItem">
							<gui-select-menu :items="selectMenu1" @select="select1" ref="selectMenu1" v-if="show">
							</gui-select-menu>
							<span v-else>{{ formData.iotType && selectMenu1[formData.iotType - 1] || '' }}</span>
						</view>
					</view>
				</view>
			</view>
			<view class="mt-200 gui-flex gui-rows gui-nowrap gui-justify-content-center" v-if="show">
				<button type="default" class="gui-button" style="width:400rpx;background-color: #C59F79;"
					formType="submit">
					<text class="gui-icons gui-color-white gui-button-text">连接设备</text>
				</button>
			</view>
			<view style="height:80rpx;"></view>
		</form>
	</view>
</template>
<script>
	var graceChecker = require("@/GraceUI5/js/checker.js");
	import {
		getDeviceDetailList,
	} from '@/api/index.js'
	import {
		bindingDevice
	} from '@/api/diabetes.js'
	export default {
		data() {
			return {
				iotSn: '',
				formData: {
					addUser: '',
					iotSn: '',
					iotName: '',
					iotType: undefined
				},
				selectMenu1: ['手环', '睡眠带', '血压计'],
				selectIndex1: 0,
				show: false,
				Addtype: '',
			}
		},
		onLoad(option) {
			this.Addtype = option.type
			if (option.type != 4) {
				if (uni.getStorageSync("cardObj")?.idCard) {
					getDeviceDetailList(uni.getStorageSync("cardObj")?.idCard || '').then(res => {
						if (JSON.stringify(res?.data || []) !== '[]') {
							this.show = false
							this.formData = res?.data.find(item => Object.is(String(item.iotType), String(option
								.type)))
							if (this.formData === undefined) {
								this.formData = {
									addUser: '',
									iotSn: '',
									iotName: '',
									iotType: undefined
								},
								this.show = true
							}
						} else {
							this.show = true
						}
					})
				}
			} else {
				this.show = true
			}
		},
		methods: {
			// 扫码接口
			startScan: function() {
				var _self = this;
				uni.scanCode({
					success: function(res) {
						_self.formData.iotSn = res.result;
						// this.$set(_self.formData,'iotSn',res.result)
					}
				});
			},
			select1: function(index, val) {
				this.selectIndex1 = index
			},
			reset: function() {
				this.$refs.selectMenu1.setCurrentIndex(0);
			},
			// 监听输入框输入事件
			inputting: function(e) {
				var name2Val = e.detail.value;
			},
			// 表单提交
			submit(e) {
				 if (this.$common.domainType == 2) {
					this.subClick(e)
				}else {
					this.formData = e.detail.value;
					this.formData.addUser = uni.getStorageSync("cardObj")?.idCard || ''
					this.formData.iotType = this.selectIndex1 + 1
					bindingDevice(this.formData).then(res => {
						if (res.data) {
							this.$common.msg("设备绑定成功", "success")
							this.formData = {}
							this.$common.navBack('1')
						} else {
							this.$common.msg(res.msg)
						}
					})
				} 
			},
			// 贵阳
			subClick(e) {
				var formData = this.formData;
				var rule = [{
					name: "iotName",
					checkType: "string",
					checkRule: "1,30",
					errorMsg: "请输入设备名"
				}]
				var checkRes = graceChecker.check(formData, rule);
				
				if (checkRes) {
					// 当验证 js 不适用某个检查时，使用自定义函数进行检查
					let type = this.Addtype
					this.formData = e.detail.value;
					this.formData.addUser = uni.getStorageSync("cardObj")?.idCard || ''
					this.formData.iotType = this.selectIndex1 + 1
					bindingDevice(this.formData).then(res => {
						if (res.code == 200) {
							uni.navigateBack({
								delta: 1
							})
							this.$common.msg("设备绑定成功", "success")
							this.formData = {}

						} else {
							this.$common.msg(res.msg)
						}
					})
					// return 会终止函数继续运行哦
					return false;
				} else {
					uni.showToast({
						title: graceChecker.error,
						icon: "none"
					});
				}
			}
		}
	}
</script>
