<template>
	<view>
		<view class="gui-padding">
			<view class="mb-40 fs-36">
				<view class="gui-bold">
					1.基本信息
				</view>
				<view class="ml-30 mt-20">
					<view class="mb-10"><text class="gui-color-gray">姓名： </text>{{fromData.name || '-'}}</view>
					<view class="mb-10"><text class="gui-color-gray">性别： </text>{{fromData.sex==1?'男':fromData.sex==2?'女':'未知'}}</view>
					<view class="mb-10"><text class="gui-color-gray">年龄： </text>{{fromData.age || '-'}}</view>
					<view class="mb-10"><text class="gui-color-gray">电话： </text>{{fromData.tel || '-'}}</view>
					<view class="mb-10"><text class="gui-color-gray">出生日期： </text>{{$common.parseTime(fromData.birthday,'{y}-{m}-{d}')}}</view>
					<view class="mb-10"><text class="gui-color-gray">证件号码： </text>{{fromData.idCard || '-'}}</view>
					<view class=""><text class="gui-color-gray">现住址： </text>{{fromData.county || '-'}}</view>
				</view>
			</view>
			<view class="mb-40">
				<view class=" gui-bold fs-36">
					2.家庭成员信息
				</view>
				<view class="mt-20" style="width: 700rpx;">
					<uni-table ref="table" :loading="loading" border stripe emptyText="暂无更多数据">
						<uni-tr>
						  <uni-th align="center">姓名</uni-th>
						  <uni-th align="center" width="90rpx">性别</uni-th>
						  <uni-th align="center">年龄</uni-th>
						  <uni-th align="center">关系</uni-th>
						  <uni-th align="center" width="220rpx">联系电话</uni-th>
						</uni-tr>
						<uni-tr v-for="(item, index) in fromData.familyList" :key="index">
						  <uni-td align="center">
							  <input class="gui-text" type="text" v-model="item.memberName" value="" />
						  </uni-td>
						  <uni-td align="center">
							<picker :value="item.sex=='1'?0:1" @change="val=>{sexChange(val,item)}" :range="sexType" range-key="value">
								<view class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center">
									<text class="gui-text">{{item.sex=='1'?'男':item.sex=='2'?'女':'-'}}</text>
									<text class=" gui-icons gui-text-center gui-color-gray">&#xe603;</text>
								</view>
							</picker>
						  </uni-td>
						  <uni-td align="center">
							  <input class="gui-text" type="text" v-model="item.age" value="" />
						  </uni-td>
						  <uni-td align="center">
							  <picker :value="relChangeIndex(item.relationship)" @change="val=>{relChange(val,item)}" :range="relationships" range-key="value">
							  	<view class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center">
							  		<text class="gui-text" style="width: 80rpx;">{{item.relationship}}</text>
							  		<text class=" gui-icons gui-text-center gui-color-gray">&#xe603;</text>
							  	</view>
							  </picker>
						  </uni-td>
						  <uni-td align="center">
						  	 <input type="number" maxlength="11" class="gui-text" v-model="item.phone" value="" />
						  </uni-td>
						</uni-tr>
					</uni-table>
					<view @click="addFamily" style="width: 100%;border:1px solid #eeeeee ; border-top: none;text-align: center; padding: 10rpx 0;">
						<view class="gui-flex gui-align-items-center gui-justify-content-center">
							继续添加<text class="gui-icons ml-15 fs-16">&#xe6c7;</text>
						</view>
					</view>
				</view>
			</view>
			<view class="fs-36">
				<view class="gui-bold ">
					3.病史信息
				</view>
				<view class="ml-30 mt-20">
					<view class="gui-flex mb-20">
						<view class="mr-20 gui-flex1">
							过敏史：
						</view>
						<view style="width: 100%;flex: 3;">
							<radio-group v-model="changedata.allergyr" @change="val=>{radiochange(val,1)}">
							<label class="radio mr-20"><radio color="#C59F79" style="transform:scale(0.6)" value="0" :checked="changedata.allergyr == '0'" /> 无</label>
							<label class="radio"><radio color="#C59F79" style="transform:scale(0.6)" value="1" :checked="changedata.allergyr == '1'"/> 有 </label>
							</radio-group>
							<view class="mt-10" v-show="changedata.allergyr == '1'">
								<ld-select ref="child" :multiple="true" :list="allergylist"
								label-key="name" value-key="value"
								placeholder="请选择"
								clearable
								v-model="changedata.allergyc"
								@change="val=>{selectchange(val,1)}"></ld-select>
							</view>
						</view>
					</view>
					<view class="mb-20 gui-flex">
						<view class="mr-20 gui-flex1">
							暴露史：
						</view>
						<view style="width: 100%;flex: 3;">
							<radio-group v-model="changedata.exposer" @change="val=>{radiochange(val,2)}">
							<label class="radio mr-20"><radio color="#C59F79" style="transform:scale(0.6)" value="0" :checked="changedata.exposer == '0'" /> 无</label>
							<label class="radio"><radio color="#C59F79" style="transform:scale(0.6)" value="1" :checked="changedata.exposer == '1'"/> 有</label>							
							</radio-group>
							<view class="mt-10" v-show="changedata.exposer == '1'">
								<ld-select ref="childexposer" :multiple="true" :list="exposelist"
								label-key="name" value-key="value"
								placeholder="请选择"
								clearable
								v-model="changedata.exposec"
								@change="val=>{selectchange(val,2)}"></ld-select>
							</view>
						</view>
					</view>
					<view class="mb-20 gui-flex">
						<view class="mr-20 gui-flex1">
							疾病史：
						</view>
						<view style="width: 100%;flex: 3;">
							<radio-group v-model="changedata.diseaser" @change="val=>{radiochange(val,3)}">
							<label class="radio mr-20"><radio color="#C59F79" style="transform:scale(0.6)" value="0" :checked="changedata.diseaser == '0'" /> 无</label>
							<label class="radio"><radio color="#C59F79" style="transform:scale(0.6)" value="1" :checked="changedata.diseaser == '1'"/> 有</label>
							</radio-group>
							<view class="mt-10" v-show="changedata.diseaser == '1'">
								<ld-select ref="childdiseaser" :multiple="true" :list="diseaselist"
								label-key="name" value-key="value"
								placeholder="请选择"
								clearable
								v-model="changedata.diseasec"
								@change="val=>{selectchange(val,3)}"></ld-select>
							</view>
						</view>
					</view>
					<view class="mb-20 gui-flex">
						<view class="mr-20 gui-flex1">
							残疾史：
						</view>
						<view style="width: 100%;flex: 3;">
							<radio-group v-model="changedata.physicalr" @change="val=>{radiochange(val,4)}">
							<label class="radio mr-20"><radio color="#C59F79" style="transform:scale(0.6)" value="0" :checked="changedata.physicalr == '0'" /> 无</label>
							<label class="radio"><radio color="#C59F79" style="transform:scale(0.6)" value="1" :checked="changedata.physicalr == '1'"/> 有</label>
							</radio-group>
							<view class="mt-10" v-show="changedata.physicalr == '1'">
								<ld-select ref="childphysicalr" :multiple="true" :list="physicallist"
								label-key="name" value-key="value"
								placeholder="请选择"
								clearable
								v-model="changedata.physicalc"
								@change="val=>{selectchange(val,4)}"></ld-select>
							</view>
						</view>
					</view>
					<view class="mb-20 gui-flex">
						<view class="mr-20 gui-flex1">
							手术史：
						</view>
						<view style="width: 100%;flex: 3;">
							<radio-group v-model="fromData.operationHistory" @change="val=>{radiochange(val,5)}">
							<label class="radio mr-20"><radio color="#C59F79" style="transform:scale(0.6)" value="0" :checked="fromData.operationHistory == '0'" /> 无</label>
							<label class="radio"><radio color="#C59F79" style="transform:scale(0.6)" value="1" :checked="fromData.operationHistory == '1'"/> 有</label>
							</radio-group>
							<view class="gui-border gui-border-radius mt-10 px-20 py-10 fs-30" v-if="fromData.operationHistory == '1'">
								<input type="text" placeholder="请输入手术史" v-model="fromData.operationOne" value="" />
							</view>
						</view>
					</view>
					<view class="mb-20 gui-flex">
						<view class="mr-20 gui-flex1">
							外伤史：
						</view>
						<view style="width: 100%;flex: 3;">
							<radio-group v-model="fromData.traumaHistory" @change="val=>{radiochange(val,6)}">
							<label class="radio mr-20"><radio color="#C59F79" style="transform:scale(0.6)" value="0" :checked="fromData.traumaHistory == '0'" /> 无</label>
							<label class="radio"><radio color="#C59F79" style="transform:scale(0.6)" value="1" :checked="fromData.traumaHistory == '1'"/> 有</label>
							</radio-group>
							<view class="gui-border gui-border-radius mt-10 px-20 py-10 fs-30" v-if="fromData.traumaHistory == '1'">
								<input type="text" placeholder="请输入外伤史" v-model="fromData.traumaOne" value="" />
							</view>
						</view>
					</view>
					<view class="mb-20 gui-flex">
						<view class="mr-20 gui-flex1">
							输血史：
						</view>
						<view style="width: 100%;flex: 3;">
							<radio-group v-model="fromData.transfusionHistory" @change="val=>{radiochange(val,7)}">
							<label class="radio mr-20"><radio color="#C59F79" style="transform:scale(0.6)" value="0" :checked="fromData.transfusionHistory == '0'" /> 无</label>
							<label class="radio"><radio color="#C59F79" style="transform:scale(0.6)" value="1" :checked="fromData.transfusionHistory == '1'"/> 有</label>
							</radio-group>
							<view class="gui-border gui-border-radius mt-10 px-20 py-10 fs-30" v-if="fromData.transfusionHistory == '1'">
								<input type="text" placeholder="请输入输血史" v-model="fromData.transfusionOne" value="" />
							</view>
						</view>
					</view>
					<view class=" gui-flex">
						<view class="mr-20 gui-flex1">
							遗传病史：
						</view>
						<view style="width: 100%;flex: 3;">
							<radio-group v-model="fromData.geneticDisease" @change="val=>{radiochange(val,8)}">
							<label class="radio mr-20"><radio color="#C59F79" style="transform:scale(0.6)" value="0" :checked="fromData.geneticDisease == '0'" /> 无</label>
							<label class="radio"><radio color="#C59F79" style="transform:scale(0.6)" value="1" :checked="fromData.geneticDisease == '1'"/> 有</label>
							</radio-group>
							<view class="gui-border gui-border-radius mt-10 px-20 py-10 fs-30" v-if="fromData.geneticDisease == '1'">
								<input type="text" placeholder="请输入遗传病史" v-model="fromData.geneticName" value="" />
							</view>
						</view>
					</view>
					
				</view>
			</view>
			<view style="height: 160rpx;"></view>
			<view 
				class="bg-white w-100 pos-fixed box-size-border box-shadows py-20 px-40 gui-border-radius-small"
				style="height: 130rpx; bottom: 0;left: 0;z-index: 9;">
				<view @click="reSubmit" class="bg-zhuti text-white w-100 h-100 d-flex ai-center jc-center">保存</view>
			</view>
		</view>
		<view class="">
					<gui-page-loading ref="guipageloading">
						<!-- 可以通过插槽添加自定义内容，如 加载提示文本 -->
						<text 
						class="gui-block-text gui-text-small gui-text-center gui-color-gray gui-italic" 
						style="padding-top:10rpx;">loading</text>
					</gui-page-loading>
				</view>
	</view>
</template>
<script>
	import ldSelect from '@/components/ld-select/ld-select.vue'
	import {getPatientInfo,updatePatientInfo} from '@/api/my.js'
	export default {
		components:{ ldSelect},
		data() {
			return {
				noClick:true,
				sexType: [
					{id:'1',value:'男'},
					{id:'2',value:'女'},
				],
				relationships:[
					{value: '户主',label: '户主'},
					{value: '父',label: '父'},
					{value: '母',label: '母'},
					{value: '夫',label: '夫'},
					{value: '妻',label: '妻'},
					{value: '子',label: '子'},
					{value: '女',label: '女'}
				],
				fromData:{
					familyList:[],//家庭信息
					// diseasecName:'',//疾病史多选
					operationHistory:'0',//手术史信息
					traumaHistory:'0',//外伤史信息
					transfusionHistory:'0',//输血史信息
					geneticDisease:'0',//遗传病史信息
					
				},
				allergylist: [],//过敏史
				exposelist:[//暴露史
					{value: 1, name: '化学品'},
					{value: 2, name: '毒物'},
					{value: 3, name: '射线'},
				],
				diseaselist:[//疾病史
					{value: 1, name: '结核病'},
					{value: 2, name: '重性精神疾病'},
					{value: 3, name: '脑卒中'},
					{value: 4, name: '慢性阻塞性肺疾病'},
					{value: 5, name: '冠心病'},
					{value: 6, name: '糖尿病'},
					{value: 7, name: '高血压'},
					{value: 8, name: '恶性肿瘤'},
					{value: 9, name: '其他法定传染病'},
					{value: 10, name: '职业病'},
					{value: 11, name: '肝炎'},
				],
				physicallist:[//残疾
					{value: 1, name: '视力残疾'},
					{value: 2, name: '听力残疾'},
					{value: 3, name: '语言残疾'},
					{value: 4, name: '肢体残疾'},
					{value: 5, name: '智力残疾'},
					{value: 6, name: '精神残疾'},
					{value: 7, name: '其他残疾'},
				],
				arrData:[],//疾病
				changedata:{//选择的数据
					allergyr:'0',
					allergyc:[],
					exposer:'0',
					exposec:[],
					diseaser:'0',
					diseasec:[],
					physicalr:'0',
					physicalc:[],
				}
			}
		},
		onLoad() {
			this.$refs.guipageloading.open();
		},
		onShow() {
			this.getList()
		},
		methods: {
			showModal(e){
				// console.log('你好呀',e)
			},
			sexChange(e,item){//picker 性别切换
				this.$set(item,'sex',this.sexType[e.detail.value].id)
			},
			relChange(e,item){
				this.$set(item,'relationship',this.relationships[e.detail.value].label)
			},
			relChangeIndex(e){
				const index = this.relationships.findIndex(i => (i.label == e))
				if (index== -1 ) {
					return 0
				} else{
					return index
				}
			},
			radiochange(e,index){//病史信息单选
				switch (index) {
					case 1://过敏史
						this.changedata.allergyr = e.detail.value
						if(e.detail.value == '0'){
							this.changedata.allergyc = []
						}else{
							this.$refs.child.showModal()
						}
						break;
					case 2://暴露史
						this.changedata.exposer = e.detail.value
						if(e.detail.value == '0'){
							this.changedata.exposec = []
						}else{
							this.$refs.childexposer.showModal()
						}
						break;
					case 3://疾病史
						this.changedata.diseaser = e.detail.value
						if(e.detail.value == '0'){
							this.changedata.diseasec = []
						}else{
							this.$refs.childdiseaser.showModal()
						}
						break;
					case 4://残疾史
						this.changedata.physicalr = e.detail.value
						if(e.detail.value == '0'){
							this.changedata.physicalc = []
						}else{
							this.$refs.childphysicalr.showModal()
						}
						break;
					case 5://手术史
						this.$set(this.fromData,'operationHistory',e.detail.value)
						if(e.detail.value == '0'){
							this.fromData.operationOne = ''
						}
						break;
					case 6://外伤史
					this.$set(this.fromData,'traumaHistory',e.detail.value)
						if(e.detail.value == '0'){
							this.fromData.traumaOne = ''
						}
						break;
					case 7://输血史
					this.$set(this.fromData,'transfusionHistory',e.detail.value)
						if(e.detail.value == '0'){
							this.fromData.transfusionOne = ''
						}
						break;
					case 8://遗传病史
					this.$set(this.fromData,'geneticDisease',e.detail.value)
						if(e.detail.value == '0'){
							this.fromData.geneticName = ''
						}
						break;
				}
			},
			selectchange(e,index){//病史信息多选
				switch (index) {
					case 1://过敏史
						this.changedata.allergyc = e
						break;
					case 2://暴露史
						this.changedata.exposec = e
						break;
					case 3://疾病史
						this.changedata.diseasec = e
						break;
					case 4://残疾史
						this.changedata.physicalc = e
						break;
				}
				
			},
			reSubmit(){
				var allergyArr = [];
				var exposeArr = [];
				var diseaseArr = [];
				var physicalArr = [];
				this.changedata.allergyc?.map(a=>{
					allergyArr.push(this.allergylist[a].name)
				})
				this.changedata.exposec?.map(a=>{
					exposeArr.push(this.exposelist[a-1].name)
				})
				this.changedata.diseasec?.map(a=>{
					diseaseArr.push(this.diseaselist[a-1].name)
				})
				this.arrData.map(i=>{
					if(diseaseArr?.indexOf(i.name) >= 0) {
						i.checked = true
					}else{
						i.checked = false
					}
				})
				this.changedata.physicalc?.map(a=>{
					physicalArr.push(this.physicallist[a-1].name)
				})
				setTimeout(() => {
					if (this.noClick) {
						this.noClick = false;
						updatePatientInfo({
							...this.fromData,
							familyList:JSON.stringify(this.fromData.familyList),
							drugAllergy:allergyArr.join(';') || '',
							exposeHistory:exposeArr.join(';') || '',
							disease:JSON.stringify(this.arrData),
							disability:physicalArr.join(';') || '',
						}).then(res =>{
							this.$common.msg("保存成功")
							setTimeout(() => {
								this.getList()
							}, 500);
						})
					}
				}, 600);
				
			},
			addFamily(){//新增家庭成员
				this.fromData.familyList.push({
					memberName:'',
					sex:'',
					age:'',
					relationship:'',
					phone:''
				})
			},
			getList(){
				this.$refs.guipageloading.open();
				this.changedata.allergyc = []
				this.changedata.exposec = []
				this.changedata.diseasec = []
				this.changedata.physicalc = []
				getPatientInfo({
					patientId:uni.getStorageSync('cardObj').patientId
				}).then(res =>{
					this.fromData = {...this.fromData,...res.data}
					//过敏史信息
					res.data.drugAllergyOptions?.forEach((item,index) =>{
						let obj = {
							value:index,
							name:item.dictLabel
						}
						this.allergylist.push(obj)
						if (res.data.drugAllergy && res.data.drugAllergy.indexOf(item.dictLabel) >= 0) {
							this.changedata.allergyr ='1'
							this.changedata.allergyc.push(index)
						}
						
					})
					//疾病史信息
					if (res.data.disease) {
						this.diseaselist = [];
						this.arrData = JSON.parse(res.data.disease)
						this.arrData.forEach((item,index) =>{
							if (item.name != '无') {
								let obj = {
									...item,
									value:index
								}
								this.diseaselist.push(obj)
								if (item.checked) {
									this.changedata.diseaser = '1'
									this.changedata.diseasec.push(index)
								}
							};
						})
					}
					//暴露史信息
					if (res.data.exposeHistory != '无') {
						this.exposelist.forEach(item =>{
							if (res.data.exposeHistory && res.data.exposeHistory.indexOf(item.name) >= 0) {
								this.changedata.exposer ='1'
								this.changedata.exposec.push(item.value)
							}
						})
						
					}
					//残疾史信息
					if (res.data.disability != '无') {
						this.physicallist.forEach(item =>{
							if (res.data.disability && res.data.disability.indexOf(item.name) >= 0) {
								this.changedata.physicalr ='1'
								this.changedata.physicalc.push(item.value)
							}
						})
						
					}
					this.$refs.guipageloading.close();
					this.analyzeIDCard(res.data.idCard)//通过身份证计算年龄
					setTimeout(() => {
						this.noClick = true;
					}, 8000);
				})
			},
			analyzeIDCard(IDCard) {
				//获取用户身份证号码
			   	//获取性别
			    // if (parseInt(IDCard.substr(16, 1)) % 2 == 1) {
			    //    getDataByIdCard.sexCode = '1' //男
			    // } else {
			    //    getDataByIdCard.sexCode = '2' //女
			    // }
			   	//获取出生年月日
			  	var yearBirth = IDCard.substring(6, 10);
			   	var monthBirth = IDCard.substring(10, 12);
				var dayBirth = IDCard.substring(12, 14);
				// var birthDate = yearBirth + "-" + monthBirth + "-" + dayBirth;
				//获取当前年月日并计算年龄
				var myDate = new Date();
			  	var monthNow = myDate.getMonth() + 1;
				var dayNow = myDate.getDay();
				var age = myDate.getFullYear() - yearBirth;
				if (monthNow < monthBirth || (monthNow == monthBirth && dayNow < dayBirth)) {
			       age--;
				}
			    //得到年龄
			    // getDataByIdCard.birthDate = birthDate;
			    this.fromData.age = age;
			 },
		}
	}
</script>
<style scoped>
	.demo{width:210rpx; margin:10rpx;}
	.button{width:90rpx; height:90rpx; line-height:90rpx; border-radius:8rpx; margin:10rpx;}
	.button-text{font-size:38rpx; text-align:center;}
</style>
