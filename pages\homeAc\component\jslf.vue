<template>
  <!-- 中医饮食指导（儿科一般指导） -->
  <view class="fs-32">
    <view class="my-20 fs-32">
      <view class="d-flex mb-20">
        <view>姓名：{{cardObj.userName || '-'}}</view>
        <view class="ml-120">性别：{{cardObj.sex == 1 ? "男" : "女"}}</view>
      </view>
      <view>时间：{{list[0].createTime}}</view>
    </view>
    <!-- 每个时间段一个独立的表格 -->
    <view v-if="loading" class="loading-container">
      <uni-load-more status="loading" :content-text="{ contentdown: '加载中...', contentrefresh: '加载中...', contentnomore: '加载中...' }"></uni-load-more>
    </view>
    <view v-else-if="list.length === 0" class="empty-container">
      <text class="empty-text">暂无更多数据</text>
    </view>
    <view v-else v-for="(item, index) in list" :key="index">
      <!-- 时间标题 -->
      <view class="time-title">
        <text class="time-title-text">{{item.arrange || '未安排时间'}}</text>
      </view>

      <!-- 该时间段的方案表格 -->
      <uni-table border stripe class="plan-table">
        <!-- 表头 -->
        <uni-tr>
          <uni-th align="center" class="type-header-new">类型</uni-th>
          <uni-th align="center" class="content-header-new">执行方案</uni-th>
        </uni-tr>
        <!-- 治疗 -->
        <uni-tr v-if="item.therapy">
          <uni-td align="center" class="type-cell-new">治疗</uni-td>
          <uni-td align="left" class="content-cell-new">{{item.therapy}}</uni-td>
        </uni-tr>
        <!-- 运动 -->
        <uni-tr v-if="item.exercise">
          <uni-td align="center" class="type-cell-new">运动</uni-td>
          <uni-td align="left" class="content-cell-new">{{item.exercise}}</uni-td>
        </uni-tr>
        <!-- 饮食 -->
        <uni-tr v-if="item.diet">
          <uni-td align="center" class="type-cell-new">饮食</uni-td>
          <uni-td align="left" class="content-cell-new">{{item.diet}}</uni-td>
        </uni-tr>
        <!-- 文化活动 -->
        <uni-tr v-if="item.culture">
          <uni-td align="center" class="type-cell-new">文化活动</uni-td>
          <uni-td align="left" class="content-cell-new">{{item.culture}}</uni-td>
        </uni-tr>
      </uni-table>
    </view>
  </view>
</template>

<script>
import {
  listFastingTherapy
} from '@/api/home.js'
export default {
  name: 'jslf',
  props: ['templateId', 'visitRecordId', 'templateDictKey'],
  data() {
    return {
      loading: false,
      cardObj: uni.getStorageSync('cardObj'),
      list: []
    }
  },
  computed: {
    // 患者信息
    info() {
      return this.cardObj || {};
    }
  },
  watch: {
    templateDictKey: {
      handler(val) {
      	if (val == 35) {
      		this.getDetail();
      	}
      },
      immediate: true
    }
  },
  methods: {
    // 获取建议详情
    getDetail() {
      this.loading = true
      listFastingTherapy({
        visitId: this.visitRecordId,
        patientId: this.cardObj.patientId
      }).then(res => {
        this.list = res.rows
        console.log(this.list)
        this.loading = false
      })
    },
    // 格式化时间文本为垂直显示
    formatTimeText(text) {
      if (!text) return '';
      // 将文本按字符分割并用换行符连接
      return text.split('').join('\n');
    }
  },
}
</script>

<style>
/* 加载和空状态样式 */
.loading-container, .empty-container {
  padding: 60rpx 0;
  text-align: center;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

/* 时间标题样式 */
.time-title {
  margin: 30rpx 0 20rpx 0;
  padding: 15rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.time-title-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  display: block;
  letter-spacing: 2rpx;
}

/* 方案表格样式 */
.plan-table {
  width: 100%;
  margin-bottom: 30rpx;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 新表头样式 */
.type-header-new {
  width: 140rpx !important;
  min-width: 140rpx !important;
  max-width: 140rpx !important;
  padding: 15rpx 10rpx !important;
  font-size: 28rpx !important;
  font-weight: bold !important;
  background-color: #f8f9fa !important;
  color: #495057 !important;
}

.content-header-new {
  padding: 15rpx 15rpx !important;
  font-size: 28rpx !important;
  font-weight: bold !important;
  background-color: #f8f9fa !important;
  color: #495057 !important;
}

/* 新数据单元格样式 */
.type-cell-new {
  width: 140rpx !important;
  min-width: 140rpx !important;
  max-width: 140rpx !important;
  padding: 15rpx 10rpx !important;
  font-size: 26rpx !important;
  text-align: center !important;
  vertical-align: middle !important;
  background-color: #fff !important;
  color: #495057 !important;
  font-weight: 500 !important;
}

.content-cell-new {
  padding: 15rpx 20rpx !important;
  line-height: 1.5 !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  min-height: 80rpx !important;
  font-size: 26rpx !important;
  vertical-align: middle !important;
  background-color: #fff !important;
  color: #333 !important;
}

/* 通用表格样式优化 */
.uni-table-td {
  border: 1px solid #e9ecef !important;
}

.uni-table-th {
  border: 1px solid #e9ecef !important;
}

/* 小程序特殊适配 */
/* #ifdef MP-WEIXIN */
.plan-table {
  overflow-x: auto;
}

.content-cell-new {
  max-width: 500rpx;
}

/* 时间标题在小程序中的优化 */
.time-title {
  margin: 25rpx 0 15rpx 0;
}
/* #endif */

/* 响应式优化 */
@media screen and (max-width: 750rpx) {
  .time-title-text {
    font-size: 30rpx;
  }

  .type-cell-new {
    font-size: 24rpx !important;
  }

  .content-cell-new {
    font-size: 24rpx !important;
    padding: 12rpx 15rpx !important;
  }
}

/* 废弃的旧样式保留以防需要 */
.tle {
  width: 10%;
  border: 1px solid #f2f3f4;
  text-align: center;
  writing-mode: vertical-lr;
  letter-spacing: 10rpx;
  line-height: 70rpx;
}

.gui-td {
  width: 100rpx;
  flex: 1;
  overflow: hidden;
  padding: 20rpx 10rpx;
  display: flexbox;
}

.gui-td-text {
  line-height: 40rpx !important;
}

#lineTd {
  padding: 0;
  background: #fff url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxsaW5lIHgxPSIwIiB5MT0iMCIgeDI9IjEwMCUiIHkyPSIxMDAlIiBzdHJva2U9IiNFQkVFRjUiIHN0cm9rZS13aWR0aD0iMSIvPjwvc3ZnPg==) no-repeat 100% center;
}
</style>
