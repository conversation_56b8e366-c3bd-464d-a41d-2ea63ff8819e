<template>
	<view class="gui-padding">
		<view class="gui-margin-top-large"></view>
		<form @submit="submit">
			<!-- 姓名 -->
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">姓名</text>
				<view class="gui-form-body">
					<input type="text" class="gui-form-input" maxlength="10" v-model="formData.userName" name="userName"
						placeholder="请输入姓名" />
				</view>
			</view>

			<!-- 身份证号 -->
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">身份证号</text>
				<view class="gui-form-body">
					<input type="text" class="gui-form-input" maxlength="18" v-model="formData.idCard" name="idCard"
						placeholder="请输入证件号码" />
				</view>
			</view>

			<!-- 就诊卡号 -->
			<view class="gui-form-item gui-border-b" v-if="$common.domainType == 1">
				<text class="gui-form-label">就诊卡号</text>
				<view class="gui-form-body">
					<input type="text" class="gui-form-input" maxlength="30" v-model="formData.visitCard"
						name="visitCard" placeholder="请输入就诊卡号" />
				</view>
			</view>
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label"> 关 系 </text>
				<view class="gui-form-body">
					<picker v-model="formData.relationship" :range="gender" @change="pickerChange">
						<view class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center">
							<text class="gui-text">{{gender[genderIndex]}}</text>
							<text class="gui-form-icon gui-icons gui-text-center gui-color-gray">&#xe603;</text>
						</view>
					</picker>
				</view>
			</view>

			<!-- 设置默认就诊卡 -->
			<view class="gui-form-item gui-border-b gui-flex gui-rows gui-space-between">
				<text class="gui-form-label" style="width: 300rpx;">设为默认就诊人</text>
				<switch :checked="formData.defaultFlag==1?true:false" name="defaultFlag" @change="switchChange"
					color="#C59F79" />
			</view>
			<view class="gui-margin-top">
				<text class="gui-h6 gui-color-gray">注：您填写的信息请与医院登记的信息保持一致，否则无法完成绑定</text>
			</view>
			<view v-if="ysxy" class="gui-margin-top">
				<gui-radio :checked="isCheck" @change="radioChange">
					<text class="gui-text gui-primary-color">我已阅读并同意 </text><text class="gui-color-blue"
						@click="open">《用户授权与隐私保护协议》</text>
				</gui-radio>
			</view>

			<!-- 立即绑定 -->
			<view class="gui-flex gui-columns gui-justify-content-center" style="margin-top: 120px;">
				<button type="primary" formType="submit" class="bnt"
					style="height: 40px;line-height: 40px;font-size: 34rpx;background-color: #C59F79;">确定绑定</button>
			</view>
			<!-- <view  @tap="addAddress"  class="gui-flex gui-columns gui-justify-content-center" style="margin-top: 15px;">
				<button type="primary" class="bnt" style="border: 1px dashed #C59F79;background:#fbf7f5;color: #C59F79;height: 40px;line-height: 40px;font-size: 34rpx;">没有卡，在线申领</button>
			</view> -->
			<!-- 弹窗 -->
			<gui-modal ref="guimodal" title="服务协议和隐私政策">
				<view slot="content" class="gui-padding gui-bg-white">
					<text class="gui-text" style="padding:10rpx;">{{textdata}}</text>
					<!--					<text class="gui-text gui-color-blue">用户协议、隐私政策、免责协议</text>-->
					<text class="gui-text">如您同意，请点击“同意”开始接受我们的服务。</text>
				</view>
				<!-- 利用 flex 布局 可以放置多个自定义按钮哦  -->
				<view slot="btns" class="gui-flex gui-rows gui-space-between">
					<view class="modal-btns gui-flex1" style="margin-right:80rpx;">
						<text class="modal-btns gui-color-gray" @tap="close">不同意</text>
					</view>
					<view class="modal-btns gui-flex1" style="margin-left:80rpx;">
						<text class="modal-btns gui-color-blue" @tap="confirm">同意</text>
					</view>
				</view>
			</gui-modal>
			<!-- 引导测评（授权订阅消息） -->
			<gui-popup ref="guipopup1">
				<view class="gui-relative gui-box-shadow gui-bg-white demo-lr">
					<view class="demo-lr-title">温馨提醒</view>
					<view class="demo-lr-content">
						<span class="demo-lr-content-span">就诊前您需要进行一次体质测评，是否立即测评？</span>
						<!-- <span class="demo-lr-content-span"></span> -->
					</view>
					<view class="demo-lr-btn">
						<view class="cancelText" @click="confirmText(0)">稍后再说</view>
						<view class="confirmText" @click="confirmText(1)">立即测评</view>
					</view>
				</view>
			</gui-popup>
		</form>
	</view>
</template>
<script>
	import {
		cardAdd,
		selectVisitCardInfo,
		appSetOff,
		selectByVisitCardNum
	} from '@/api/my.js'
	import {
		register,
		getCard,
		constitutionFillContent
	} from '@/api/home.js'
	export default {
		data() {
			return {
				$common:this.$common,
				ysxy: false,
				textdata: '请您充分理解“服务协议”和“隐私政策”各条款，为了向您提供健康管理和宣教等服务，我们需要收集您的身份证等个人信息。您可以在“我的”个人信息中查看、变更个人信息。',
				// 表单数据存储
				formData: {
					userName: '',
					idCard: '',
					visitCard: '',
					relationship: '',
					defaultFlag: 1
				},
				isCheck: false, //是否勾选协议
				gender: ['请选择', '本人', '父母', '配偶', '子女'],
				genderIndex: 1,
				// 密码可视 类型切换
				isPwd: true,
				code: false, //true扫码进来绑定
				resultCode: '' //二维码内容
			}
		},
		onLoad(e) {
			// this.$common.openDyMsg();
			this.code = e.code ? JSON.parse(e.code) : false
			this.resultCode = e.result ? e.result : {},
			this.appSetOff();
			this.selectByVisitCardNum(e)
		},
		methods: {
			confirmText(e){
				this.$common.openDyMsg(0,2);
				// this.$common.openDyMsgs();
				if(e){
					this.$common.navCloseTo('/pages/homeAc/evaluation/index')
				}else{
					this.$common.navTab('/pages/home/<USER>')
					// this.$common.navBack(1)
				}
			},
			selectByVisitCardNum(e){
				if(!e.visitCardNum){
					return
				}
				selectByVisitCardNum({
					visitCardNum:e.visitCardNum
				}).then(res=>{
					if(res.data){
						var formData = this.formData
						formData.userName = res.data.userName
						formData.idCard = res.data.idCard
						formData.visitCard = res.data.visitCardNum
					}
				})
			},
			//隐私政策 显示开关配置
			appSetOff() {
				appSetOff().then(res=>{
					this.ysxy = res.data;
				})
			},
			//隐私政策
			radioChange: function(e) {
				this.isCheck = e[0];
			},
			open: function() {
				this.$refs.guimodal.open();
			},
			close: function() {
				this.isCheck = false;
				this.$refs.guimodal.close();
			},
			confirm: function() {
				// 客户点击确认按钮后的逻辑请在此处实现
				this.isCheck = true;
				this.$refs.guimodal.close();
			},
			addAddress: function() {
				uni.navigateTo({
					url: './card-new'
				})
			},
			// 表单提交
			submit: function(e) {
				let that = this;
				if (!e.detail.value.userName) {
					return this.$common.msg('姓名不能为空')
				}
				if (!e.detail.value.idCard && !e.detail.value.visitCard) {
					return this.$common.msg('就诊卡号或者身份证不能为空')
				}
				// 身份证18位校验
				// let idreg =/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;	
				let idreg = /(^\d{15}$)|(^\d{17}([0-9]|X)$)/;
				if (!idreg.test(e.detail.value.idCard) && e.detail.value.idCard) {
					return this.$common.msg('请输入正确的身份证号')
				}
				// if(!e.detail.value.visitCard){return this.$common.msg('就诊卡不能为空')}
				// if(this.formData){return this.$common.msg('就诊卡不能为空')}
				this.formData.relationship = this.gender[this.genderIndex];
				console.log(this.formData.relationship,'555555555555')
				if (!this.formData.relationship || this.formData.relationship == '请选择') {
					return this.$common.msg('请选择正确的关系')
				}
				this.formData.userName = e.detail.value.userName;
				this.formData.idCard = e.detail.value.idCard? e.detail.value.idCard.replace(/\s+/g, ''):'';
				this.formData.visitCardNum =e.detail.value.visitCard? e.detail.value.visitCard.replace(/\s+/g, '') :'';
				this.formData.isDefaultPatient = e.detail.value.defaultFlag ? 1 : 0;
				if (this.ysxy && this.isCheck != true) {
					return this.$common.msg('请勾选授权与隐私保护协议')
				}
				cardAdd(this.formData).then(res => {
					if (this.code) {
						register({
							doctorId: this.resultCode,
							visitCardNum: this.formData.visitCardNum,
							idCard: this.formData.idCard,
						}).then(resi => {
							var msg = "已成功签到" + resi.data.doctorName + "医生进行健康管理！"
							setTimeout(() => {
								this.$common.msg(msg, "none", 5000)
							}, 500);
							setTimeout(function() {
								that.getCard();
							}, 5000);
						}, fail => {
							this.$common.msg(res.msg, "success");
							setTimeout(() => {
								that.getCard();
							}, 3000);
						})
					} else {
						this.$common.msg(res.msg, "success");
						setTimeout(function() {
							that.getCard();
						}, 1000);
					}
				},fail=>{
					console.log('500500500')
				})
			},
			getCard() {
				var self = this;
				getCard().then(res => {
					console.log(res,'就诊卡列表')
					const cardList = res.data.cardList;
					if (cardList.length > 0) {
						var patientId = cardList[0].patientId || ''
						var memberId = patientId ? '' : uni.getStorageSync("user")?.id
						constitutionFillContent({
							patientId,
							memberId
						}).then(res => {
							console.log('我进来了呵呵呵',res.data)
							self.$common.navBack(1)
							// if (res.data) {
							// 	self.$common.navBack(1)
							// 	// 已测评
							// } else {
							// 	// 未测评
							// 	// self.$refs.guipopup1.open()
							// }
						})
						selectVisitCardInfo({
							patientId: cardList[0].patientId || '',
							memberId: uni.getStorageSync('user')?.id || ''
						}).then(res => {})
						uni.setStorageSync("cardObj", cardList[0]); //缓存res是默认的就诊卡
					}
				})
			},
			// picker 切换
			pickerChange: function(e) {
				this.genderIndex = e.detail.value;
				// this.formData.relationship = this.gender[this.genderIndex];
			},
			// switch 开关
			switchChange: function(e) {
				this.formData.defaultFlag = this.formData.defaultFlag == 1 ? 0 : 1;
			},
		}
	}
</script>
<style scoped>
	.demo-lr-content-span{
		text-align: center;
		word-wrap: break-word;
	}
	.demo-lr{
		border-radius: 20rpx;
	}
	.demo-lr-content{
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		color: #999;
		text-align: center;
		font-size: 38rpx;
	}
	.demo-lr-title{
		padding: 60rpx 30rpx 0rpx 30rpx;
		text-align: center;
		font-weight: bold;
		font-size: 38rpx;
	}
	.confirmText{
		color: #55aaff;
		padding: 30rpx 0;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 38rpx;
		flex:1;
		font-weight: bold;
	}
	.cancelText{
		font-size: 38rpx;
		justify-content: center;
		align-items: center;
		font-weight: bold;
		text-align: center;
		padding: 30rpx 0;
		flex:1;
		border-right: 2rpx solid #eee;
	}
	.demo-lr-btn{
		display: flex;
		border-top: 2rpx solid #eee;
		flex-direction: row;
		justify-content: space-between;
	}
	.gui-text-small {
		line-height: 50rpx;
	}

	.modal-btns {
		line-height: 88rpx;
		font-size: 26rpx;
		text-align: center;
		width: 200rpx;
	}

	.gui-text-card {
		border: 1px solid #008AFF;
		border-radius: 5px;
		color: #008AFF;
		line-height: 30px;
		text-align: center;
	}

	>>>.gui-form-input {
		margin: 0;
		height: 80rpx;
		line-height: 80rpx;
	}

	.bnt {
		width: 60%;
		height: 30px;
		font-size: 12px;
		margin: 5rpx auto;
	}

	.gui-form-label {
		font-size: 36rpx;
	}
</style>
