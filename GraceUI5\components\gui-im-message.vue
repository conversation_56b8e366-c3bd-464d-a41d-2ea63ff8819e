<template name="gui-im-messages">
	<view class="gui-im"
	:style="{'background-color':background}" @touchmove="move">
		<block v-for="(item, index) in msgs" :key="index">
			<block v-if="item.groupIndex == group">
				<!-- 系统消息 -->
				<view class="gui-im-msg gui-flex gui-rows gui-justify-content-center"
				v-if="item.contentType == 'system'">
					<view class="gui-im-system-msg gui-bg-black">{{item.content}}</view>
				</view>
				<!-- 用户消息 -->
				<view v-else >
					<view class="gui-im-msg"
					:class="[userid != item.senderUid ? 'gui-im-msg-right' : 'gui-im-msg-left']">
						<view class="gui-im-face">
							<!-- <image src="../../static/yisheng.png" mode=""></image> -->
							<image class="gui-im-face-image" :src="item.senderFace?item.senderFace:url" mode="widthFix"></image>
						</view>
						<view class="gui-im-content-spacing"></view>
						<view class="gui-im-content-in">
						<!-- <view class="gui-im-name" v-if="userid == item.senderUid">
							<text :style="unameStyle">{{item.senderName}}</text>
						</view> -->
						<!-- 文本消息 -->
						<view class="gui-flex" :class="[userid != item.senderUid ? 'gui-im-flex-end' : 'gui-im-flex-start']" @longpress="val=>{revocation(index,item)}">
							<!-- <view v-if="userid != item.senderUid" class="read">{{item.isRead == 1?'已读':'未读'}}</view> -->
							<view v-if="userid != item.senderUid && index == revocationKey" style="display: flex;flex-direction: column;justify-content: flex-end;">
								<view @click="revocationadd(item,index)"  class="gui-bg-black2 gui-color-white px-30 gui-border-radius mr-10 fs-26" style="height: 50rpx;line-height: 50rpx;">撤回</view>
							</view>
							<!-- 文本消息 -->
							<view v-if="item.contentType == 'txt'"
							:class="[userid != item.senderUid ? 'gui-im-flex-end' : 'gui-im-flex-start']">
								<view class="gui-im-content-txt"
								:class="[userid != item.senderUid ? 'gui-bg-green' : '']">
									<text :style="txtContentStyle"
									:class="[userid != item.senderUid ? 'gui-color-white' : '']">{{item.content}}</text>
								</view>
							</view>
							<!-- 5)宣教，问卷 -->
							<view v-if="item.contentType == 'task' && userid == item.senderUid"
							:class="[userid != item.senderUid ? 'gui-im-flex-end' : 'gui-im-flex-start']">
								<view class="gui-im-content-intervene"
								:class="[userid != item.senderUid ? 'gui-bg-green' : '']" @click="openTask(JSON.parse(item.content))">
									<view class="gui-flex gui-columns py-15">
										<view class="mb-20 pb-20 px-20 gui-flex gui-justify-content-end" style="border-bottom: 1px solid #d1d1d1;">
											<view class="text-center gui-color-white gui-bg-blue" style="border-radius: 50rpx;height: 40rpx; width: 40rpx;margin:auto;line-height: 40rpx;">
												<text v-if="item.content && JSON.parse(item.content).task_type == 1" class="gui-icons fs-28" >&#xe69e;</text>
												<text v-else-if="item.content && JSON.parse(item.content).task_type == 2" class="gui-icons fs-28" >&#xe609;</text>
											</view>
											<view v-if="item.content && JSON.parse(item.content).task_type == 2" class="ml-20 gui-color-blue fs-32 mr-10">宣 教 指 导</view>
											<view v-if="item.content && JSON.parse(item.content).is_read == 0 && JSON.parse(item.content).task_type == 2" class="bg-zhuti fs-20 px-10 gui-border-radius text-center gui-color-white" style="margin: auto;">待阅读</view>
											<view v-if="item.content && JSON.parse(item.content).is_read == 1 && JSON.parse(item.content).task_type == 2" class="gui-bg-black3 fs-20 px-10 gui-border-radius text-center gui-color-white" style="margin: auto;">已阅读</view>
											<view v-if="item.content && JSON.parse(item.content).task_type == 1" class="ml-20 gui-color-blue fs-32 mr-10">填 写 问 卷</view>
											<view v-if="item.content && JSON.parse(item.content).status == 0 && JSON.parse(item.content).task_type == 1" class="bg-zhuti fs-20 px-10 gui-border-radius text-center gui-color-white" style="margin: auto;">待填写</view>
											<view v-if="item.content && JSON.parse(item.content).status == 1 && JSON.parse(item.content).task_type == 1" class="gui-bg-black3 fs-20 px-10 gui-border-radius text-center gui-color-white" style="margin: auto;">已填写</view>
										</view>
										<text class="px-20" :style="txtContentStyle"
										:class="[userid != item.senderUid ? 'gui-color-white' : '']">{{JSON.parse(item.content).questionnaire_name}}</text>
									</view>
								</view>
							</view>
							<!-- 图片消息 -->
							<view v-if="item.contentType == 'img'"
							:class="[userid != item.senderUid ? 'gui-im-flex-end' : '']">
								<view class="gui-img-in gui-im-img">
									<image class="gui-im-img"
									:src="item.content" :data-img="item.content"
									@tap="showImgs" mode="widthFix"></image>
								</view>
							</view>
							<!-- 语言消息 -->
							<view v-if="item.contentType == 'voice'" class="voice">
								<view class="gui-flex gui-rows gui-nowrap gui-align-items-center"
								:class="['gui-im-voice-msg', userid != item.senderUid ? 'gui-bg-green,gui-im-flex-end,reverse' : '']"
								:data-voice="JSON.parse(item.content).url"
								:data-index='index' @tap='playVoice'
								>
									<view class="saying">
										<text :class="['shelter', index == playIndex ? 'play' : '']"></text>
										<text class="gui-icons"
										:class="[userid != item.senderUid ? 'gui-color-white' : '',]">&#xe800;</text>
									</view>
									<text class="gui-im-voice-msg-text"
									:class="[userid != item.senderUid ? 'gui-color-white' : '']">
									{{JSON.parse(item.content).second.toFixed(0)}} ”
									</text>
									<view :style="{'width':(JSON.parse(item.content).second*6)+'rpx'}"></view>
								</view>
							</view>
							<!-- <view v-if="userid == item.senderUid" class="read">{{item.isRead == 1?'已读':'未读'}}</view> -->
						</view>
						<view :class="[userid != item.senderUid ? 'gui-text-right' : '']">
							<text class="gui-im-timer gui-block-text">{{item.date}}</text>
						</view>
						</view>
					</view>
				</view>
			</block>
		</block>

	</view>
</template>
<script>
	import {revokeMessage} from '@/api/im.js'
export default {
	name   : "gui-im-messages",
	props  : {
		msgs             : { type : Array,  default : function(){ return []; }},
		userid           : { type : String, default:''},
		group            : { type : String, default : "" },
		background       : { type : String, default : "#F7FBFE" },
		unameStyle       : { type : String, default : 'line-height:28rpx; height:28rpx; font-size:26rpx; color:#666666;'},
		txtContentStyle  : { type : String, default : 'line-height:38rpx; font-size:34rpx; color:#2B2E3D;'}
	},
	data() {
		return {
			url:"../../static/yisheng.png",
			player    : null,
			playIndex : -1,
			revocationKey:-1,
		}
	},
	created: function(){
		this.player = uni.createInnerAudioContext();
		this.player.onPlay(() => {console.log('play');});
		this.player.onEnded(() => {
			var cIndex     = Number(this.playIndex);
			this.playIndex = -1;
			// for(let i = cIndex + 1; i < this.msgs.length; i++) {
			// 	if (this.msgs[i].contentType == 'voice') {
			// 		this.playNow(this.msgs[i].content, i);
			// 		break;
			// 		return;
			// 	}
			// }
		});
		this.player.onError((E)=>{console.log(E);});
  },
  methods: {
	  move(){
	  		  this.revocationKey = -1
	  		console.log('手指移动')
	  },
	  openTask(item){
		  uni.setStorageSync("openIMconsult", 3)
		  if (item.task_type == 2) {
		  	// 宣教
		  	this.$common.navTo('/pages/task/other/missionary?id='+item.id+'&taskType='+item.task_type)
		  } else if(item.task_type == 1) {
		  	// 问卷
		  	this.$common.navTo('/pages/task/other/questionnaire?id='+item.id+'&taskType='+item.task_type+'&openType=3')
		  }
	  },
	  revocation(e,item){
		  // console.log('33333==',item.date)
		  let odlData = new Date().getFullYear()+'-'+item.date;
		  let magDtae = new Date().getTime() - new Date(odlData).getTime()
		  // console.log('888==',magDtae)
		  if (magDtae<= 120000) {
			  if(this.revocationKey == e){
				  this.revocationKey = -1
			  }else{
				  this.revocationKey = e;
			  }
		  	this.$forceUpdate()
		  }
	  },
	  revocationadd(item,index){
		  this.revocationKey = -1;
		  this.$forceUpdate()
		  let odlData = new Date().getFullYear()+'-'+item.date;
		  let magDtae = new Date().getTime() - new Date(odlData.replace(/-/g,'/')).getTime()
		  if (magDtae> 120000) {return}
		  revokeMessage(item).then(res=>{
			  if (res.code == 200 ) {
				this.$emit('revokeText',item,index);
			  }
		  })
	  },
		  goCommentAdd(){
			uni.navigateTo({
				url: '../../pages/my/personal/comment-add'
			});
		  },
		// 播放语音
		playVoice: function (e) {
			var voicelUrl = e.currentTarget.dataset.voice;
			var index     = e.currentTarget.dataset.index;
			if (this.playIndex == -1){
				return this.playNow(voicelUrl, index);
			}
			if (this.playIndex == index) {
				this.player.stop();
				this.playIndex = -1;
			} else {
				this.player.stop();
				this.playIndex = -1;
				this.playNow(voicelUrl, index);
			}
		},
		playStop(){
			this.player.stop();
		},
		// 语音播放基础函数
		playNow: function (voicelUrl, index){
			this.playIndex  = index;
			this.player.src = voicelUrl;
			this.player.play();
			return true;
		},
		// 图片预览
		showImgs : function(e){
			var imgs        = [];
			var imgsCurrent = e.currentTarget.dataset.img;
			for (var i = 0; i < this.msgs.length; i++) {
				if (this.msgs[i].contentType == 'img') {
					imgs.push(this.msgs[i].content);
				}
			}
			uni.previewImage({urls : imgs, current : imgsCurrent});
		},
	}
}
</script>
<style scoped lang="scss">
	.read{
		font-size: 24rpx;
		color: #999;
		flex-direction: column;
		padding: 0 10rpx;
		display: flex;
		justify-content: flex-end;
	}
.pendant{background-color: #F1F1F1;border-radius: 50rpx;width:100rpx; height:100rpx; border-radius:100rpx; text-align:center; line-height:100rpx; font-size:24rpx;position: fixed;right: 20rpx;bottom: 200rpx;}
.gui-im{padding:30rpx;}
.gui-im-system-msg{color:#FFFFFF; font-size:32rpx; line-height:38rpx; padding:5px 10px; display:block; border-radius:6rpx;}
.gui-im-msg{margin-bottom:28px; display:flex; flex-direction:row; flex-wrap:nowrap;}
.gui-im-face{width:88rpx; height:88rpx; overflow:hidden; flex-shrink:0; border-radius:6rpx; overflow:hidden; font-size:0;}
.gui-im-face-image{width:88rpx;}
.gui-im-name{padding-bottom:10rpx; margin-top:-5rpx; }
.gui-im-content-spacing{width:25rpx; height:50rpx; flex-shrink:0;}
.gui-im-content-in{width:600rpx; overflow:hidden;}
.gui-im-content-txt{background-color:#E7F0F3; padding:15rpx 20rpx; border-radius:6rpx; min-width:60rpx;max-width:400rpx; word-break:break-all;}
.gui-im-content-intervene{background-color:#E7F0F3; border-radius:6rpx; min-width:60rpx;max-width:400rpx; word-break:break-all;}
.gui-im-msg-right{flex-direction:row-reverse;}
.gui-im-timer{margin-top:5px; line-height:30rpx; font-size:22rpx; color:#8788A3;}
.gui-im-img{width:358rpx; border-radius:6rpx;}
.gui-im-flex-end{display:flex; flex-direction:row; flex-wrap:nowrap; justify-content:flex-end;}
.gui-im-flex-start{display:flex; flex-direction:row; flex-wrap:nowrap; justify-content:flex-start;}
.gui-im-voice-msg{height:72rpx; padding:0 20rpx; background-color:#E7F0F3; border-radius:6rpx; color:#2B2E3D; min-width:140rpx; max-width:466rpx;}
.gui-im-voice-msg-text{font-size:28rpx; margin:0 5rpx; flex: none;}
.voice .gui-icons{
	font-size: 36rpx;
}

@font-face {
font-family: "guiimfont";
src:url('data:font/ttf;charset=utf-8;base64,d09GRgABAAAAAARIAAsAAAAABpAOfljMqxyszBwQAGoSFheJxjYGRgYADih0telMfz23xl4GZhAIEblhYmCPr/ZxYGZl4gl4OBCSQKAC7VCZ8AeJxjYGRgYG7438AQw8IAAkCSkQEVMAEARGInC9nJQTHaVXQA5CB44C9F5F876O4UPCXntWPc3ndFsU1P9zhQEC9M9cU7qy0nk6T4E9XxtSdXQrbsuelDSRXs1JErJCXta2VELqATZlV44RelzRiT8oZ0j/AAlabsgAAAB4nGNgYoAALgbsgImRiZGZgTWxNCUzn4EBAAzjAi0AAA==');
}
.graceIMFont{font-family:"graceIMFont"; font-size:30rpx; color:#2B2E3D;}

.saying {
    position: relative;
}
.shelter {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 20;
    width: 0;
    height: 100%;
    background: #E7F0F3;
}
.play {
    animation: scalein 1s ease-out infinite;
}
@keyframes scalein {
    0% {
      width: 65%;
    }
    10% {
      width: 65%;
    }
    39% {
      width: 50%;
    }
    40% {
      width: 49%;
    }
    69% {
      width: 40%;
    }
    70% {
      width: 39%;
    }
    100% {
      width: 0;
    }
}
.reverse {
	justify-content: end;
	flex-direction: row-reverse;
	.saying {
		transform: rotate(180deg);
	}
	.shelter {
		background: #07C160 ;
	}
}
</style>

