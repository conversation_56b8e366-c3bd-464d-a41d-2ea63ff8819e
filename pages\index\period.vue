<template>
	<view class="gui-padding">
		<form >
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">最近一次经期：</text>
				<view class="gui-form-body">
					<gui-datetime @confirm="confirm2" :value="from.menstruationStartDate" :isTime="false">
						<text class="demo gui-border-radius-large gui-icons ">{{from.menstruationStartDate}} &#xe6d1; </text>
					</gui-datetime>
				</view>
			</view>
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">月经持续天数：</text>
				<view class="gui-form-body">
					<picker mode="selector" :range="gender" @change="pickerChange">
						<view class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center">
							<text class="gui-text">{{gender[genderIndex]}}</text>
							<text class="gui-form-icon gui-icons gui-text-center gui-color-gray">&#xe603;</text>
						</view>
					</picker>
				</view>
			</view>
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">月经周期天数：</text>
				<view class="gui-form-body">
					<picker mode="selector" :range="cycle" @change="cycleChange">
						<view class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center">
							<text class="gui-text">{{cycle[cycleIndex]}}</text>
							<text class="gui-form-icon gui-icons gui-text-center gui-color-gray">&#xe603;</text>
						</view>
					</picker>
				</view>
			</view>
		</form>
		<view class="subtn bg-zhuti">
			<view @click="addList">提交保存</view>
		</view>
	</view>
</template>
<script>
	import {pregnancy,getPregnancyByPatientId} from '@/api/index.js'
export default {
	data() {
		return {
			from:{
				patientId:uni.getStorageSync("cardObj").patientId,//患者ID
				menstruationStartDate:"请选择日期",//上一次日期
				durationDay:"",//天数
				cycle:"",//周期
				createBy: uni.getStorageSync("user").nickName
			}, 
			gender : ['请选择'],
			genderIndex : 0,
			cycle : ['请选择'],
			cycleIndex : 0,
		}
	},
	onLoad() {
		//选择项内容 1-15  15-90  .unshift('请选择')
		var days = Array(15).fill(1).map((v,k)=>k+1)
		let arr = days.unshift('请选择')
		this.gender = days
		
		var cyales = Array(76).fill(1).map((v,k)=>k+15)
		let obj = cyales.unshift('请选择')
		this.cycle = cyales
		
		let times = uni.getStorageSync('timedata')
		this.getList(times)
		uni.removeStorageSync('timedata')
	},
	methods: {
		//获取经期预测数据
		getList(daytiam) {
			var querydata = {
				patientId: uni.getStorageSync("cardObj").patientId, //患者ID
				month: daytiam
			}
			getPregnancyByPatientId(querydata).then(res=>{
				//天数 durationDay
				if (res.data.pregnancy.durationDay != '') {
					let daykey = this.gender.findIndex(item => item === res.data.pregnancy.durationDay)
					this.genderIndex = daykey
					this.from.durationDay = this.gender[this.genderIndex];
					// 周期cycle
					let cyclekey = this.cycle.findIndex(item => item === res.data.pregnancy.cycle)
					this.cycleIndex = cyclekey
					this.from.cycle = this.cycle[this.cycleIndex];
					// 最近一次
					let timedata = this.$common.parseTime(new Date(res.data.pregnancy.menstruationStartDate), '{y}-{m}-{d}')
					this.from.menstruationStartDate = timedata
				}
				// 周期cycle
				if (res.data.pregnancy.cycle != null) {
					let cyclekey = this.cycle.findIndex(item => item === res.data.pregnancy.cycle)
					this.cycleIndex = cyclekey
					this.from.cycle = this.cycle[this.cycleIndex];
				}
			})
		},
		confirm2(res) {
			this.from.menstruationStartDate = res[0]+'-'+res[1]+'-'+res[2];
		},
		//经期天数
		pickerChange(e) {
			this.genderIndex = e.detail.value;
			this.from.durationDay = this.gender[this.genderIndex];
		},
		//经期周期
		cycleChange(e) {
			this.cycleIndex = e.detail.value;
			this.from.cycle = this.cycle[this.cycleIndex];
		},
		// 表单提交
		addList() {
			if(!this.from.menstruationStartDate){
				uni.showToast({title:"请选择最近一次经期", icon:"none"}); 
				return ;
			}
			if(!this.from.durationDay){
				uni.showToast({title:"请选择月经持续天数", icon:"none"}); 
				return ;
			}
			if(!this.from.cycle){
				uni.showToast({title:"请选择月经周期天数", icon:"none"}); 
				return ;
			}
			pregnancy(this.from).then(res=>{
				this.$common.msg("提交成功", "success")
				setTimeout(()=>{
					this.$common.navTab('/pages/index/index')
				}, 1000);
			})
		}
	}
}
</script>
<style scoped>
	>>>.gui-form-label{width: 200rpx;}
.gui-text-small{line-height:50rpx;}
.subtn{
	left: 0rpx;
	width: 100%;
	height: 120rpx;
	position: absolute;
	bottom: 0rpx;
	text-align: center;
	line-height: 120rpx;
	font-size: 34rpx;
}
</style>