<script>
	import {
		getCard
	} from '@/api/home.js'
	import {
		getList
	} from '@/api/system.js'
	export default {
		onLaunch: function() {
			console.log('只用进来一次')
			// #ifdef MP-WEIXIN
			this.autoUpdate()
			// #endif
			// #ifdef APP-PLUS
			plus.screen.lockOrientation('portrait-primary'); //锁定屏幕
			const dom = weex.requireModule('dom');
			dom.addRule('fontFace', {
				'fontFamily': "graceIconfont",
				'src': "url('/static/grace.ttf')"
			});
			// #endif
		},
		onShow: function() {
			// 获取系统配置
			this.getList()
			if (uni.getStorageSync("token") && uni.getStorageSync('cardObj')) {
				if(uni.getStorageSync("cardObj").idCard){
					this.$common.getNoReads()
				}
				console.log('刷新就诊卡数据')
				this.getCard()
			}
		},
		onHide: function() {
			this.$store.commit('closeSocket')
		},
		methods: {
			getList(){
				getList().then(res=>{
					this.$common.systemlist = res.data.map(function(item,index){
						return item.menuIdent
					})
				})
			},
			//获取当前登录者绑定的就诊卡
			getCard() {
				getCard().then(res => {
					let cardList = res.data.cardList;
					if (cardList.length > 0) {
						if (uni.getStorageSync('cardObj')) {
							const patientId = uni.getStorageSync('cardObj')?.patientId;
							var obj = cardList.find(card => String(card.patientId) === String(patientId));
							uni.setStorageSync("cardObj", obj);
						} else {
							uni.setStorageSync("cardObj", cardList[0])
						}
					} else {
						uni.removeStorageSync('cardObj')
					}
					this.$common.role();
				})
			},
			// 监测小程序更新
			autoUpdate() {
				if (uni.canIUse('getUpdateManager')) {
					const updateManager = uni.getUpdateManager()
					console.log(updateManager)
					// 检查是否有新版本发布
					if(!updateManager){
						return
					}
					updateManager.onCheckForUpdate(function(res) {
						if (res.hasUpdate) {
							//小程序有新版本，则静默下载新版本，做好更新准备
							updateManager.onUpdateReady(function() {
								uni.showModal({
									title: '更新提示',
									content: '新版本已经准备好，是否重启应用？',
									success: function(res) {
										if (res.confirm) {
											//新的版本已经下载好，调用 applyUpdate 应用新版本并重启
											updateManager.applyUpdate()
										} else if (res.cancel) {
											//如果需要强制更新，则给出二次弹窗，如果不需要，则这里的代码都可以删掉了
											uni.showModal({
												title: '温馨提示',
												content: '我们已经做了新的优化，请及时更新哦~',
												showCancel: false, //隐藏取消按钮，也可显示，取消会走res.cancel，然后从新开始提示
												success: function(res) {
													//第二次提示后，强制更新           
													if (res.confirm) {
														// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
														updateManager.applyUpdate()
													} else if (res.cancel) {
														//重新回到版本更新提示
														autoUpdate()
													}
												}
											})
										}
									}
								})
							})
							// 新的版本下载失败
							updateManager.onUpdateFailed(function() {
								uni.showModal({
									title: '温馨提示',
									content: '新版本已经上线，请您删除当前小程序，重新搜索打开',
								})
							})
						}
					})
				} else {
					// 提示用户在最新版本的客户端上体验
			  uni.showModal({
						title: '温馨提示',
						content: '当前微信版本过低，可能无法使用该功能，请升级到最新版本后重试。'
					})
				}
			}
		}
	}
</script>
<style>
	@import "appui/style.css";
	@import "appui/main.css";
	@import "appui/icon.css";
	/* 加载框架核心样式 */
	@import "./GraceUI5/css/graceUI.css";
	/* 加载主题样式 */
	@import "./GraceUI5/skin/black.css";
	@import "./tools/styles/style.css";

	uni-toast .uni-toast {
		font-size: 50rpx !important;
	}


	/* 加载图标字体 - 条件编译模式 */
	/* #ifdef APP-PLUS-NVUE */
	.gui-icons {
		font-family: graceIconfont;
	}

	/* #endif */
	page {
		background: #fff;
	}

	.nav-list {
		display: flex;
		flex-wrap: wrap;
		padding: 0px 40upx 0px;
		justify-content: space-between;
	}

	.nav-li {
		padding: 30upx;
		border-radius: 12upx;
		width: 45%;
		margin: 0 2.5% 40upx;
		background-image: url(https://cdn.nlark.com/yuque/0/2019/png/280374/1552996358352-assets/web-upload/cc3b1807-c684-4b83-8f80-80e5b8a6b975.png);
		background-size: cover;
		background-position: center;
		position: relative;
		z-index: 1;
	}

	.nav-li::after {
		content: "";
		position: absolute;
		z-index: -1;
		background-color: inherit;
		width: 100%;
		height: 100%;
		left: 0;
		bottom: -10%;
		border-radius: 10upx;
		opacity: 0.2;
		transform: scale(0.9, 0.9);
	}

	.nav-li.cur {
		color: #fff;
		background: rgb(94, 185, 94);
		box-shadow: 4upx 4upx 6upx rgba(94, 185, 94, 0.4);
	}

	.nav-title {
		font-size: 32upx;
		font-weight: 300;
	}

	.nav-title::first-letter {
		font-size: 40upx;
		margin-right: 4upx;
	}

	.nav-name {
		font-size: 28upx;
		text-transform: Capitalize;
		margin-top: 20upx;
		position: relative;
	}

	.nav-name::before {
		content: "";
		position: absolute;
		display: block;
		width: 40upx;
		height: 6upx;
		background: #fff;
		bottom: 0;
		right: 0;
		opacity: 0.5;
	}

	.nav-name::after {
		content: "";
		position: absolute;
		display: block;
		width: 100upx;
		height: 1px;
		background: #fff;
		bottom: 0;
		right: 40upx;
		opacity: 0.3;
	}

	.nav-name::first-letter {
		font-weight: bold;
		font-size: 36upx;
		margin-right: 1px;
	}

	.nav-li text {
		position: absolute;
		right: 30upx;
		top: 30upx;
		font-size: 52upx;
		width: 60upx;
		height: 60upx;
		text-align: center;
		line-height: 60upx;
	}

	.text-light {
		font-weight: 300;
	}

	@keyframes show {
		0% {
			transform: translateY(-50px);
		}

		60% {
			transform: translateY(40upx);
		}

		100% {
			transform: translateY(0px);
		}
	}

	@-webkit-keyframes show {
		0% {
			transform: translateY(-50px);
		}

		60% {
			transform: translateY(40upx);
		}

		100% {
			transform: translateY(0px);
		}
	}
</style>
