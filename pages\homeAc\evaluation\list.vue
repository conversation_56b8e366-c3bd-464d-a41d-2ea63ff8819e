<template>
	<!-- <gui-page :loadmore="true" @loadmorefun="getList" :isLoading="pageLoading"
			:apiLoadingStatus="apiLoadingStatus" ref="guipage" :refresh="true" @reload="reload"> -->
  <gui-page :isLoading="pageLoading">
	  <view slot="gBody" style="height: 100vh;">
		<view class="mt-20" v-if="showBox">
			<view class="mx-60 gui-relative" @click="navTo('综合')">
				<view class="fs-36 gui-bold" style="position: absolute;z-index: 9;top: 40rpx;width: 100%;">
					<view class="w-100 pl-60">中医体质综合</view>
					<view class="w-100 pl-80">问卷测评</view>
				</view>
				<image src="https://img.starup.net.cn/xmkj/zwb-gy-test/2024/07/05/015e64b8bb2945afa00c491aab29ef79.png" style="width: 100%; height: 200rpx; border-radius: 10rpx;" mode=""></image>
			</view>
			<view class="gui-flex gui-rows gui-wrap mx-40 mt-20 gui-space-around">
				<view @click="toList(item.value)" v-for="(item,index) in imgList">
					<view class="h300 w300 mb-40 gui-border-radius gui-relative">
						<view class="gui-color-white fs-36 gui-bold w-100 h-100 gui-flex gui-justify-content-center gui-align-items-end pb-60 gui-absolute-rb">{{item.name}}</view>
						<image :src="item.image" style="width: 300rpx; height: 300rpx; border-radius: 10rpx;" mode=""></image>
					</view>
				</view>
			</view>
		</view>
	  	<view style="height: 100vh;" class="gui-relative" v-if="!showBox">
			<view @click="toBox" class="gui-absolute-rb pb-100 w-100 gui-flex gui-justify-content-center">
				<view class="bg-zhuti gui-color-white fs-36 w-20 gui-border-radius py-10 gui-flex gui-justify-content-center">返 回</view>
			</view>
	  		<view v-if="list.length >= 0" class="gui-padding gui-flex gui-align-items-center gui-justify-content-center gui-columns pt-40">
	  			  <view class="mb-30" v-for=" (item,index) in list" :key="index">
	  			  	<view @click="navTo(item)" class="gui-flex gui-justify-content-center fs-36 gui-bg-white py-30 lsit-title-lyb" >
	  					<text class="ellipsis-1">{{item.questionName}}</text>
	  			  	</view>
	  			  </view>
	  		</view>
	  		<view v-if="list.length <= 0">
	  			<gui-empty>
	  				<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
	  					<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
	  				</view>
	  				<text slot="text"
	  				class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
	  			</gui-empty>
	  		</view>
	  	</view>
	  </view>
  </gui-page>
</template>

<script>
	import {questionnaireList,questionnairegroup} from '@/api/home.js'
export default {
  data() {
    return {
		loadState: 0 ,  // 0 : 默认0，有下一页   1 ：请求中  2： 加载完毕
		pageLoading: true,//加载动画
		apiLoadingStatus: false,// 用于记录是否有 api 请求正在执行
		page:1,
		size:20,
		zongSize:0,
		zongPage: 0,
		list:[],
		showBox:true,
		imgList:[
			// {questionGroup:1,name:'脑卒中评估',url:'https://img.starup.net.cn/xmkj/zwb-test/2025/03/12/2b233ec31f0b480db1ecd9e4d1aa5626.png'},
			// {questionGroup:2,name:'冠心病评估',url:'https://img.starup.net.cn/xmkj/zwb-test/2025/03/12/0c78b87c8cdc4d74ba83035428d16b7b.png'},
			// {questionGroup:3,name:'高血压评估',url:'https://img.starup.net.cn/xmkj/zwb-test/2025/03/12/6161f7b9fef149efa5e1481d4eb3c18a.png'},
			// {questionGroup:4,name:'糖尿病评估',url:'https://img.starup.net.cn/xmkj/zwb-test/2025/03/12/********************************.png'},
			// {questionGroup:5,name:'高脂血评估',url:'https://img.starup.net.cn/xmkj/zwb-test/2025/03/12/5763551f537d4d26a1e6d28214abdd32.png'},
			// {questionGroup:6,name:'痛风评估',url:'https://img.starup.net.cn/xmkj/zwb-test/2025/03/12/c1289286dcbd4addb9b914c4e9623add.png'}
		],
		}
  },
  onShow() {
	this.getImgList()
  },
  methods: {
	  getImgList(){
		  questionnairegroup().then(res =>{
			  this.imgList = res.data;
			  this.pageLoading = false;
		  })
	  },
	  toBox(){
		  this.showBox = true;
		  this.list = []
		  this.$forceUpdate()
	  },
	  toList(questionGroup){
		  this.pageLoading = true;
		  this.getList(questionGroup);
	  },
	  reload() {
	  	this.page = 1;
	  	// this.getList(true);
	  },
    navTo(item) {
		if (item == '综合') {
			// {questionName:"中医体质综合问卷测评",typeName:"体质",id:'5tizhi'}
			this.$common.navTo('/pages/homeAc/evaluation/index?typeId=5tizhi&questionName=中医体质综合问卷测评&questionType=体质')
		} else{
			this.$common.navTo('/pages/homeAc/evaluation/index?typeId='+ item.id + '&questionName='+item.questionName+'&questionType='+item.questionType)
		}
		setTimeout(() => {
			this.toBox();
		}, 600)

    },
    getList(questionGroup) {
		this.apiLoadingStatus = true;
		questionnaireList({
			pageNum:this.page,
			pageSize:this.size,
			questionGroup:questionGroup
		}).then(res=>{
			// this.list = this.page == 1 ? [{questionName:"中医体质综合问卷测评",typeName:"体质",id:'5tizhi'}].concat(res.data.rows) : this.list.concat(res.data.rows);
			this.list = res.data.rows;
			this.zongSize = res.data.total;
			this.pageLoading = false;
			this.showBox = false;
			this.$forceUpdate()
			// if (isReload) {
			// 	this.$refs.guipage.endReload();
			// }
			// if (this.page >= this.zongPage) {
			// 	this.$refs.guipage.nomore();
			// 	this.loadState = 2;
			// 	this.apiLoadingStatus  = false;
			// 	return;
			// }else{
			// 	this.$refs.guipage.stoploadmore();
			// }
			// this.page++;
			// this.loadState = 0;
			// this.apiLoadingStatus = false;
		})
    }
  }
}
</script>

<style scoped>
.title {
  font-size: 38rpx;
}
.lsit-title-lyb{
	border: 2px dashed #d8d8d8;
	width: 700rpx;
	}
</style>
