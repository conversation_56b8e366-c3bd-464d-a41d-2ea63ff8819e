<template>
	<view class="weixin-official-account">
		<view class="logosize mr-15" @click="openimg">
			<image :src="logo||'https://img.starup.net.cn/xmkj/zwb/img/zyLogo.jpg'"
				style="width: 100%;height: 100%; border-radius: 80rpx; display: inline-block"></image>
		</view>
		<view class="txt" @click="openimg">
			<view class="">{{gzhName}}</view>
			<view style="font-size: 26rpx;">关注公众号，更多健康文章不容错过！</view>
		</view>
		<button type="default" class="btn btn-official" @click="openimg"> + 关注
		<!-- <official-account  class="official-account" @load="bindload" id="official_account"></official-account> -->
		</button>
		<!-- <text class="gui-icons gui-color-white" @click="close">&#xe610;</text> -->
		<!-- <icon class="iconfont icon-close" @click="close"></icon> -->
		<gui-popup ref="guipopup1" >
			<view class="gui-flex gui-columns gui-align-items-center">
				<view class="gui-relative gui-box-shadow gui-bg-white gui-flex gui-columns gui-align-items-center mb-20" style="border-radius: 20rpx;">
					<image :show-menu-by-longpress="true" :src="gzhImg || 'https://img.starup.net.cn/xmkj/zwb/2023/09/01/24ef4669ec1245588decbc54af9acc28.jpg'"
						style="width: 450rpx;height: 450rpx; display: inline-block;margin: 30rpx;"></image>
						<view class="mb-20 fs-24" style="color: #4d4d4d">
							长按图片关注公众号
						</view>
				</view>
				<view @click="colseimg" style="width: 50rpx;height: 50rpx; line-height: 50rpx; text-align: center; border-radius: 80rpx; border: 4rpx solid #ffffff;">
					<text class="gui-icons gui-color-white fs-24">&#xe7a5;</text>
				</view>
			</view>
		</gui-popup>
	</view>
</template>

<script>
	export default {
		props: {
			bottom: {
				type: [Number, String],
				default: 100
			},
			gzhName: {
				type: [ String],
				default: '广西星铭科技'
			},
			gzhImg: {
				type: [ String],
				default: 'https://img.starup.net.cn/xmkj/zwb/2023/09/01/24ef4669ec1245588decbc54af9acc28.jpg'
			},
			logo: {
				type: [ String],
				default: 'https://img.starup.net.cn/xmkj/zwb/img/zyLogo.jpg'
			},
			isFollow: {// 是否已经关注过公众号
				type: [Boolean],
				default: false
			}
		},
		data() {
			return {
				show: true,
				opacity: 0,
				max: 3
			}
		},
		mounted() {
			setTimeout(() => {
				this.init();
			}, 800)
		},
		methods: {
			openimg(){
				this.$refs.guipopup1.open()
			},
			colseimg(){
				this.$refs.guipopup1.close()
			},
			// bindload(detail){
			// 	console.log('子组件公众号==',detail.detail)
			// },
			init() {
				let that = this;
				const query = uni.createSelectorQuery().in(this);
				query.select('#official_account').boundingClientRect(data => {
					if (data.width && data.height) {
						that.check();
					} else {
						that.show = false;
					}
				}).exec();
			},
			async check() {
				try {
					const num = uni.getStorageSync('CLOSE_OFFICIAL_ACCOUNT');
					if (+num >= this.max) {
						this.show = false;
						return false;
					}
					this.show = !this.isFollow;
					if (this.show) this.opacity = 1;
				} catch (e) {
					this.show = false;
				}
			},
			close() {
				this.show = false;
				const num = uni.getStorageSync('CLOSE_OFFICIAL_ACCOUNT') ? +uni.getStorageSync('CLOSE_OFFICIAL_ACCOUNT') + 1 : 1;
				uni.setStorageSync('CLOSE_OFFICIAL_ACCOUNT', num);
			}
		}
	}
</script>

<style scoped lang="scss">
	.logosize {
		display: flex;
		width: 80rpx;
		height: 80rpx;
	}
	.weixin-official-account {
		// position: fixed;
		// z-index: 88888;
		left: 50%;
		// transform: translateX(-50%);
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		height: 110rpx;
		padding: 0 10rpx 0 30rpx;
		background: rgba(0, 0, 0, .6);
		border-radius: 6rpx;
	
		.txt {
			font-size: 28rpx;
			color: #fff;
		}
	
		.btn {
			position: relative;
			width: 150rpx;
			height: 60rpx;
			line-height: 60rpx;
			padding: 0;
			background: #c59f79;
			border-radius: 30px;
			overflow: hidden;
			text-align: center;
			font-size: 28rpx;
			color: #fff;
	
			.official-account {
				position: absolute;
				z-index: ********;
				right: -14px;
				top: -40px;
				opacity: 0;
			}
		}
	
		.icon-close {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 50rpx;
			height: 68rpx;
			font-size: 22rpx;
			color: #fff;
		}
	}
</style>