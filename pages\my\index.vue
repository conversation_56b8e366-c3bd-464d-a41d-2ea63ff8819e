<template>
	<view>
		<image src="https://img.starup.net.cn/xmkj/zwb/img/zy_pre_topbg.png" style="width: 800rpx; height: 420rpx;">
		</image>
		<view class="" style="margin-top: -150rpx;">
			<image src="https://img.starup.net.cn/xmkj/zwb/img/zy_pre_cardbg.png"
				style="width: 720rpx;margin-left: 15rpx;height: 550rpx;"></image>
		</view>
		<view class="gui-relative" style="margin: -640rpx 80rpx 0;">
			<view class="gui-text-center" style="margin-top: -600rpx !important;" @click="editImg">
				<image class="ucenter-face-image" :src="user.avatar ? user.avatar : '/static/empty_avatar.png'"
					mode="widthFix"></image>
			</view>
			<view class="gui-text-center" @tap="upMember">
				<text class="font-bold gui-h4 gui-primary-color">{{user.nickName || "请先登录"}}</text>
			</view>
			<button v-if="cardList.length>0" @tap="qhCard(1)" style="background: rgba(255,255,255,0.5);color:#88735E"
				class="card-id-n" type="default" size="mini">切换就诊人</button>
			<view @tap="toCardDetail()">
				<view class="addcar gui-flex ai-center gui-justify-content-center"
					v-if="isEmpty(res) || isEmpty(res.id)">
					<text class="gui-grids-icon gui-icons">&#xe6c7;</text>
					添加就诊卡
				</view>
				<view class="" v-else>
					<view class="gui-text-center mt-30" style="color: #88735E;">
						当前就诊人：{{res.userName}}
						<text v-if="res.relationship"
							style="margin-left: 10rpx;font-size: 28rpx;">【{{res.relationship}}】</text>
					</view>
					<view class="gui-flex p-10 b-radius-10 my-20"
						style="background: rgba(255,255,255,0.5);display: flex;flex-direction: row;align-items: center;">
						<view class="fs-26 car-num">卡号：</view>
						<view class="py-15">
							<text class="fs-50 " style="color: #F66829;">{{res.visitCardNum}}</text>
						</view>
					</view>
				</view>
			</view>
			<view @tap="qhCard(2)" class="gui-text-right fs-24" style="color: #A18E7A;"> 就诊卡 {{cardList.length || 0}}
				张，点击管理<image src="https://img.starup.net.cn/xmkj/zwb/img/zy_ent_brow.png" class="w30 h20 ml-15 mt-10">
				</image>
			</view>
		</view>
		<!-- 设备绑定 -->
		<view class="device">
			<view class="pb-20"  @click="deviceList()" style="display: flex; align-items: center;justify-content: space-between;flex-direction: row;">
				<view class="device_titl py-20 px-20 fs-32 d-flex">
					<image src="../../static/log/device.png" mode=""></image>
					<text style="line-height: 50rpx;">设备列表</text>
				</view>
				<text class="gui-list-arrow-right gui-icons gui-color-gray-light fs-40 d-flex">&#xe601;</text>
			</view>
			<view class="gui-border-bw mx-20"></view>
			<view class="pt-20" @click="filesList()" style="display: flex; align-items: center;justify-content: space-between;flex-direction: row;">
				<view class="device_titl py-20 px-20 fs-32 d-flex">
					<image src="../../static/img/zy_health_data_hl.png" mode=""></image>
					<text style="line-height: 50rpx;">完善档案</text>
				</view>
				<text class="gui-list-arrow-right gui-icons gui-color-gray-light fs-40 d-flex">&#xe601;</text>
			</view>
			<view class="gui-border-bw mx-20"></view>
			<view class="pt-20" @click="$common.navTo('/pages/care/index')" style="display: flex; align-items: center;justify-content: space-between;flex-direction: row;">
				<view class="device_titl py-20 px-20 fs-32 d-flex">
					<image src="../../static/img/cpwj.png" mode=""></image>
					<text style="line-height: 50rpx;">系统消息</text>
				</view>
				<text class="gui-list-arrow-right gui-icons gui-color-gray-light fs-40 d-flex">&#xe601;</text>
			</view>
			<view v-if="cardList.length>0">
				<view class="gui-border-bw mx-20"></view>
				<view class="pt-20" @click="tomsgList()" style="display: flex; align-items: center;justify-content: space-between;flex-direction: row;">
					<view class="device_titl py-20 px-20 fs-32 d-flex">
						<image src="../../static/img/tljy.png" mode=""></image>
						<text style="line-height: 50rpx;">消息订阅</text>
					</view>
					<text class="gui-list-arrow-right gui-icons gui-color-gray-light fs-40 d-flex">&#xe601;</text>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	import {getCard} from '@/api/home.js'
	import {getUserInfo} from '@/api/my.js'
	export default {
		data() {
			return {
				// $iconJson: this.$iconJson,
				res: {},
				user: {},
				cardList: []
			}
		},
		onShow() {
			this.$common.role();
			if (!uni.getStorageSync("token") || !uni.getStorageSync("user")) {
				uni.showModal({
					title: '提示',
					content: '您没有授权登录！',
					confirmText: '去登录',
					confirmColor: '#576B95',
					success: function(res) {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/login/index'
							})
						} else if (res.cancel) {
							uni.switchTab({
								url: '/pages/home/<USER>'
							})
						}
					}
				});
			} else {
				this.res = uni.getStorageSync('cardObj');
				this.user = uni.getStorageSync('user');
				this.getUserInfo();
				this.getCard();
			}
		},
		methods: {
			//获取用户信息刷新
			getUserInfo(){
				getUserInfo({
					openId: uni.getStorageSync('openId')
				}).then(res=>{
					if(res.data && res.data.openId){
					    this.user = res.data
					    uni.setStorageSync('user', res.data)
					}
				})
			},
			// 绑定设备
			deviceList(type) {
				let name = this.user.nickName
				uni.navigateTo({
					url: `/pages/myAc/deviceList?name=` +  name
				})
			},
			//完善档案
			filesList(){
				if (uni.getStorageSync('cardObj') && uni.getStorageSync('cardObj').patientId) {
					this.$common.navTo('/pages/myAc/filesList')
				} else {
					this.$common.msg("请先绑定就诊卡")
				}
			},
			tomsgList(){
				// this.$common.navTo('/pages/care/index')
				// return
				if (uni.getStorageSync('cardObj') && uni.getStorageSync('cardObj').patientId) {
					this.$common.navTo('/pages/myAc/magList')
				} else {
					this.$common.msg("请先绑定就诊卡")
				}
			},
			editImg(){
			  this.$common.navTo('/pages/myAc/headPortrait')
			},
			toCardDetail() {
				this.$common.openDyMsg();
				if (this.cardList.length <= 0) {
					this.$common.navTo('/pages/myAc/other/addPatient')
				} else {
					var item = encodeURIComponent(JSON.stringify(this.res));
					this.$common.navTo('./other/card-id?item=' + item)
				}
			},
			// 获取卡列表
			getCard() {
				getCard().then(res=>{
					this.cardList = res.data.cardList
				})
				this.$common.getNoReads();
			},
			// 跳转就诊卡
			qhCard(type) {
				this.$common.openDyMsg();
				if (this.cardList.length <= 0) {
					this.$common.navTo('/pages/myAc/other/addPatient')
				} else {
					uni.removeStorageSync('role')
					this.$common.navTo('/pages/myAc/other/patient?type=' + type)
				}
			},
			toMess() {
				this.$common.navTo('/pages/myAc/message')
			},
			upMember(){
			  this.$common.navTo('/pages/homeAc/evpi?my=1')
			},
		}
	}
</script>
<style scoped>
	.gui-border-bw{border-bottom-style:solid; border-bottom-width:1rpx; border-bottom-color:#ffffff;}
	/* 设备绑定 */
	.device {
		position: relative;
		clear: both;
		width: 700rpx;
		top: 120rpx;
		margin: 0 auto;
		border-radius: 15rpx;
		background-color: #f3ebda;
		color: #A18E7A;
	}
	
	.device_titl image {
		display: block;
		width: 50rpx;
		height: 50rpx;
		float: left;
		margin-right: 15rpx;
	}
	
	.gui-grids-icon {
		width: 100rpx;
		font-size: 45rpx;
	}

	.addcar {
		background: rgba(255, 255, 255, 0.5);
		height: 160rpx;
		margin-top: 60rpx;
		margin-bottom: 20rpx;
		border: 1px dashed #C59F79;
		border-radius: 10rpx;
		color: #C59F79;
		text-align: center;
	}

	.auto {
		margin: auto;
	}

	.icons-calor {
		color: #64CBA1;
	}

	.grace-body {
		margin: 160rpx 40rpx;
	}

	.card {
		width: 700rpx;
		height: 320rpx;
		position: relative;
		margin: auto;
		color: #FFFFFF;
		display: flex;
		flex-direction: column;
	}

	.car-num {
		color: #C59F79;
		white-space: nowrap;
	}

	.card-id-n {
		position: absolute;
		top: 150rpx;
		right: 0;
		border-radius: 50px;
		background: none;
		color: #F66829;
	}

	.card-id-btn {
		width: 55px;
		height: 55px;
		border: 2px solid #FFFFFF;
		border-radius: 85px;
		background: url(https://img.starup.net.cn/xmkj/zwb/img/hz_btn_cant.png) round;
	}

	.card-id {
		margin: 10px;
	}

	.card-name {
		margin: auto;
		font-size: 16px;
	}

	.ucenter-face {
		width: 100rpx;
		height: 100rpx;
	}

	.ucenter-face-image {
		width: 160rpx;
		height: 160rpx;
		border-radius: 160rpx;
	}

	.ucenter-line {
		height: 20rpx;
		background-color: #F6F7F8;
		margin: 16rpx 0;
	}

	.logoff {
		line-height: 88rpx;
		font-size: 28rpx;
	}

	.gui-list-title-text {
		line-height: 60rpx;
	}
</style>
