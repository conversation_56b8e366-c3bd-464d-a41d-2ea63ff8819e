<template>
	<gui-page>
		<template v-slot:gBody>
			<!-- 轮播图  -->
			<view>
				<gui-swiper 
				:swiperItems="swiperItems" 
				:spacing="0" :padding="0" 
				borderRadius="0rpx" 
				:width="750" 
				:height="450"></gui-swiper>
			</view>
			<!-- 商品标题 分享按钮 -->
			<!-- 价格 -->
			<view  class="gui-padding gui-bg-white gui-dark-bg-level-3 mt-10" >
				<view class="detail-list">
					<viwe class="gui-flex mb-10 gui-space-between" >
						<view class="fs-36 ellipsis-1" v-if="product.activityName"> {{product.activityName}} </view>
					</viwe>
					<view class="gui-flex gui-space-between">
						<viwe class="gui-text-small small-text">活动截止时间：{{product.activityDateStatus ==1?product.activityDate : '不限'}} </viwe>
					</view>
				</view>
				
			</view>
			<view class="gui-bg-gray" style="width: 100%; height: 20rpx;"></view>
			<!-- 切换导航 -->
			<view class="">
				<gui-switch-navigation 
				:items="navItems" 
				:isCenter="true" 
				:size="200"
				lineHeight="80rpx" 
				textAlign="center"
				activeLineWidth="200rpx" 
				activeLineHeight="2rpx"
				activeFontSize = "32rpx"
				activeLineBg = "linear-gradient(to right, #d6ad84,#C59F79)"
				:margin="10" ></gui-switch-navigation>
			</view>
			<!-- 详情 请根据项目情况自行改进 可以使用 富文本-->
			<view class="mt-20 px-40 fs-32" style="word-wrap:break-word" v-html="$common.adjustRichTextImageSize(product.activityContent)"></view>
			
			<!-- 底部 -->
			<view class="product-footer gui-dark-bg-level-3" >
				<!-- 底部按钮栏 -->
				<view class=" gui-flex gui-columns gui-align-items-center">
					<!-- 2个底部按钮 -->
					<!-- <view class="gui-footer-large-buttons gui-flex1 gui-flex gui-row gui-nowrap"> -->
						<view class="bg-zhuti" style="text-align:center;width: 60%;border-radius: 80rpx;" hover-class="gui-tap" @tap="buynow(product)">
							<text v-if="product.issueStatus == 1 && product.applyStatus == 0" class="gui-text gui-text-center gui-block gui-color-white gui-footer-large-button-text" style="font-size: 32rpx;">报 名</text>
							<text v-else class="gui-text gui-text-center gui-block gui-color-white gui-footer-large-button-text" style="font-size: 32rpx;">已报名</text>
						</view>
						<view style="height:40rpx;"></view>
					<!-- </view> -->
				</view>
				<gui-iphone-bottom></gui-iphone-bottom>
			</view>
			<!-- 底部占位 -->
			<view style="height:120rpx;"></view>
		</template>
	</gui-page>
</template>
<script>
	import { activityDetail,addActivityByPatient,activityDetailNo,activityshare } from '@/api/activity.js'
	import guiSwiper from '@/GraceUI5/components/gui-swiper.vue'
	import guiSwitchNavigation  from '@/GraceUI5/components/gui-switch-navigation.vue'
export default {
	components:{guiSwiper,guiSwitchNavigation},
	data() {
		return {
			// 轮播图 
			swiperItems : [],
			// 商品信息
			product : {},
			// 切换导航
			navItems : [{id:0, dictLabel:'活动详情'}],
			// 切换索引
			active   : 0,
			// 属性记录
			attrRes : {color:null, size:null, number:1},
			shopid:''
		}
	},
	onLoad(e) {
		console.log(uni.getLaunchOptionsSync().scene,'场景值1154')
		wx.showShareMenu({
		    withShareTicket:true,
		    menus:["shareAppMessage","shareTimeline"]
		})
		if (e.id) {
			this.shopid = e.id
			if(uni.getLaunchOptionsSync().scene == 1154){
				this.getDetail1(Number(e.id))
			}else{
				this.getDetail(Number(e.id))
			}
		}
	},
	onShareAppMessage: function(options){
		if(uni.getStorageSync("token")){
			activityshare(this.shopid)
		}
		var shareObj = {
		title: this.product.activityName,    // 默认是小程序的名称(可以写slogan等)
		path: '/pages/homeAc/activity/detail?id='+this.shopid,    // 默认是当前页面，必须是以‘/'开头的完整路径
		imageUrl: this.swiperItems[0].img   //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
		}
		// 返回shareObj
		return shareObj;
	},
	//分享到朋友圈
	onShareTimeline(res) {
		if(uni.getStorageSync("token")){
			activityshare(this.shopid)
		}
		var shareObj1 = {
			title: this.product.activityName,
			query: 'id='+this.shopid,
			// path: '/pages/science/detail?articleId='+this.model.articleId,
			imageUrl: this.swiperItems[0].img
		}
		return shareObj1
	},
	methods: {
		// 分享到朋友圈单页面模式展示，无token接口
		getDetail1(e){
			activityDetailNo(e).then(res =>{
				this.product = res.data
				//头部轮播图
				var dataArr = res.data.activityImgUrl?.split(',')
				for( let i of dataArr){
					var obj ={
						img : i
					}
					this.swiperItems.push(obj)
				}
			})
		},
		getDetail(e){
			activityDetail(e,{userId:uni.getStorageSync('user').id}).then(res =>{
				this.product = res.data
				
				//头部轮播图
				var dataArr = res.data.activityImgUrl?.split(',')
				for( let i of dataArr){
					var obj ={
						img : i
					}
					this.swiperItems.push(obj)
				}
			})
		},
		buynow(e){
			if (e) {
				let self = this
				uni.showModal({
					title: '温馨提醒', //提示标题
					content: '您是否确定要报名此活动？', //提示内容
					cancelText: "取消", // 取消按钮的文字  
					confirmText: "确认", // 确认按钮的文字  
					confirmColor: '#C59F79',
					cancelColor: '#000',
					success: function(res) {
						if (res.confirm) { //confirm为ture，代表点击了确定
							// console.log('用户点击确定');
							addActivityByPatient({
								activityId:e.id,//活动id
								patientId:uni.getStorageSync('user').id,//用户id
							}).then(res =>{
								if (res.code == 200) {
									self.$common.msg("活动报名成功")
									self.product.applyStatus = 1;
									setTimeout(() => {
										self.$common.navTo('/pages/homeAc/activity/index?type=1')
									}, 600)
								}
							}) 
						} else if (res.cancel) { //cancel为ture，代表点击了取消
							// console.log('用户点击取消');
							// self.$common.navBack(1)
						}
					}
				});
			}
		}
	}
}
</script>
<style scoped>
	.detail-list{
		display: flex;
		flex-direction: column;
	}
	.small-text{
		color: #C59F79;
		line-height: 50rpx;
		font-size: 30rpx;
	}
.product-name{width:560rpx; line-height:50rpx;}
.product-share{width:80rpx; height:80rpx; text-align:center; font-size:50rpx; color:#FF7900; line-height:80rpx;}
.product-price{color:#FF7900; font-size:36rpx; }
.gui-common-line{height:18rpx;}
.product-footer{position:fixed; z-index:99; left:0; bottom:0; width:100%; }
.product-attr{width:700rpx; margin:25rpx; height:580rpx;}
.gui-padding{padding:20rpx 25rpx;}
.gui-footer-icon-buttons{width:100rpx;}
</style>
