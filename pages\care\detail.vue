<template>
	<gracePage :customHeader="false">
		<view slot="gBody" class="p-30">
			<!-- <view v-if="model.content && model.articleType && model.articleType == 3"></view> -->
			<view >
				<view class="font-bold text-black fs-36 mb-20">
						{{model.title || ''}}
					</view>
					<view>
						<text class="text-grey-b2 fs-24">{{model.createTime}}</text>
					</view>
					<!-- <view v-if="model.content && model.articleType == 2" class="text-justify mt-30" style="word-wrap:break-word">
						<u-parse :content="$common.adjustRichTextImageSize(model.content)"></u-parse>
					</view> -->
					<view style="width: 100%;" class="text-justify mt-30">
						<mp-html :content="model.content" />
					</view>
					<!-- <view  class="text-justify mt-30" style="word-wrap:break-word" v-html="$common.adjustRichTextImageSize(model.content)"></view> -->
			</view>
		</view>
	</gracePage>
</template>

<script>
	import {noticeList} from '@/api/home.js'
	// import uParse from '@/components/u-parse/u-parse.vue'
	export default {
		// components:{uParse},
		data() {
			return {
				useropenId:uni.getStorageSync('cardObj').patientId,
				id:null,
				type:null,
				model:{
					articleName:'',
					content:'',
					createTime:"",
					articleType:""
				}
			}
		},
		onLoad(option) {
			if (option?.id) {
				this.getdata(option.id);
			}
			this.model = JSON.parse(decodeURIComponent(option.item))
		},
		methods:{
			getdata(id){
				noticeList({
					id:id,
					// memberId:this.useropenId
				}).then(res=>{
					this.model = res.rows[0]
				})
			},
			//调整富文本图片大小
			adjustRichTextImageSize(text){
				return text;
			}
		}
	}
</script>

<style>
	page{
		background: #fff;
	}
</style>
