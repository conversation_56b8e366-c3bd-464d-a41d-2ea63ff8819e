{
	"pages": [{
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "中医健康管理平台",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/my/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "white"
				// "navigationBarTitleText": "我的"
			}
		},
		{
			"path": "pages/index/index",
			"style": {
				// "navigationBarTitleText": "好孕",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/period",
			"style": {
				"navigationBarTitleText": "经期设置"
			}
		},
		{
			"path": "pages/task/other/aboutus",
			"style": {
				"navigationBarTitleText": "链接详情"
			}
		}, {
			"path": "pages/task/index",
			"style": {
				"navigationBarTitleText": "打卡任务"
			}
		}, {
			"path": "pages/task/tongue",
			"style": {
				"navigationBarTitleText": "上传舌面象"
			}
		}, {
			"path": "pages/science/index",
			"style": {
				"navigationBarTitleText": "科普",
				"enablePullDownRefresh": true
			}
		}, {
			"path": "pages/science/detail",
			"style": {
				"navigationBarTitleText": "科普详情"
			}
		}, {
			"path": "pages/task/other/missionary",
			"style": {
				"navigationBarTitleText": "宣教任务"
			}
		}, {
			"path": "pages/task/other/questionnaire",
			"style": {
				"navigationBarTitleText": "问卷任务"
			}
		},
		{
			"path": "pages/task/other/treatment",
			"style": {
				"navigationBarTitleText": "康养任务"
			}
		},
		{
			"path": "pages/task/other/medication",
			"style": {
				"navigationBarTitleText": "用药提醒"
			}
		},{ //2023-3-22前暂时没有启用，后期升级
			"path": "pages/task/other/monitor",
			"style": {
				"navigationBarTitleText": "监测任务 "
			}
		},
		{
			"path": "pages/task/other/aXueya",
			"style": {
				"navigationBarTitleText": "血压记录 "
			}
		}, {
			"path": "pages/task/other/bMaibo",
			"style": {
				"navigationBarTitleText": "脉搏记录 "
			}
		}, {
			"path": "pages/task/other/cXuetang",
			"style": {
				"navigationBarTitleText": "血糖记录 "
			}
		}, {
			"path": "pages/task/other/dXueyang",
			"style": {
				"navigationBarTitleText": "血氧记录 "
			}
		}, {
			"path": "pages/task/other/eTiwen",
			"style": {
				"navigationBarTitleText": "体温记录 "
			}
		}, {
			"path": "pages/task/other/fHuxi",
			"style": {
				"navigationBarTitleText": "呼吸记录 "
			}
		}, {
			"path": "pages/login/index",
			"style": {
				"navigationBarTitleText": "登录"
			}
		}, {
			"path": "pages/login/gzhweb",
			"style": {
				"navigationBarTitleText": "授权"
			}
		},
		{
			"path": "pages/home/<USER>/chat",
			"style": {
				"navigationBarTitleText": "咨询"
			}
		},
		{
			"path": "pages/home/<USER>/consult",
			"style": {
				"navigationBarTitleText": "聊天"
			}
		}

	],
	"subPackages": [
		{
			"root": "pages/homeAc",
			"pages": [
				{
					"path": "other/furtherVisit",
					"style": {
						"navigationBarTitleText": "复诊通知"
					}
				},
				{
					"path": "saoDetail",
					"style": {
						"navigationBarTitleText": "药品详情"
					}
				},
				{
					"path": "healthy/healthy",
					"style": {
						"navigationBarTitleText": "健康日记"
					}
				},
				{
					"path": "healthy/release",
					"style": {
						"navigationBarTitleText": "发布日记"
					}
				},{
					"path": "other/integral",
					"style": {
						"navigationBarTitleText": "积分明细"
					}
				}, {
					"path": "other/proposal",
					"style": {
						"navigationBarTitleText": "健康建议"
					}
				},
				{
					"path": "other/salDetail",
					"style": {
						"navigationBarTitleText": "健康建议"
					}
				},{
					"path": "other/pdfPreview",
					"style": {
						"navigationBarTitleText": "营养干预方案报告"
					}
				},{
					"path": "other/articleDetail",
					"style": {
						"navigationBarTitleText": "医案分享"
					}
				},{
					"path": "other/department",
					"style": {
						"navigationBarTitleText": "科室介绍"
					}
				},
				{
				    "path" : "evpi",
				    "style" :
				    {
				        "navigationBarTitleText": "完善个人信息",
				        "enablePullDownRefresh": false
				    }
				},{
					"path": "record/index",
					"style": {
						"navigationBarTitleText": "健康记录"
					}
				}, {
					"path": "record/commonlyDetail",
					"style": {
						"navigationBarTitleText": "记录详情"
					}
				},
				{
					"path": "record/info",
					"style": {
						"navigationBarTitleText": "结果详情",
						"enablePullDownRefresh": false
					}
				},
				 {
					"path": "record/notSickDetail",
					"style": {
						"navigationBarTitleText": "记录详情"
					}
				},{
					"path": "record/jcReport",
					"style": {
						"navigationBarTitleText": "检查报告"

					}
				}, {
					"path": "record/jyReport",
					"style": {
						"navigationBarTitleText": "检验报告"
					}
				},
				{
					"path": "evaluation/list",
					"style": {
						"navigationBarTitleText": "测评问卷"
					}
				},
				{
					"path": "evaluation/details",
					"style": {
						"navigationBarTitleText": "问卷结果"
					}
				},
				{
					"path": "evaluation/index",
					"style": {
						"navigationBarTitleText": "体质测评"
					}
				}, {
					"path": "evaluation/content",
					"style": {
						"navigationBarTitleText": ""
					}
				}, {
					"path": "code",
					"style": {
						"navigationBarTitleText": "扫码签到"
					}
				},{
					"path": "treatment/index",
					"style": {
						"navigationBarTitleText": "项目预约",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "treatment/record",
					"style": {
						"navigationBarTitleText": "预约记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "treatment/project",
					"style": {
						"navigationBarTitleText": "治疗预约",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "activity/index",
					"style":{
					    "navigationBarTitleText": "活动列表",
					    "enablePullDownRefresh": false
					}
				},
				{
					"path": "activity/detail",
					"style":{
					    "navigationBarTitleText": "活动详情",
					    "enablePullDownRefresh": false
					}
				},
				{
					"path": "component/openH5",
					"style": {
						"navigationBarTitleText": "营养干预方案",
					    "enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/myAc",
			"pages": [
				{
					"path": "other/patient",
					"style": {
						"navigationBarTitleText": "就诊人管理"
					}
				}, {
					"path": "other/addPatient",
					"style": {
						"navigationBarTitleText": "添加就诊人"
					}
				}, {
					"path": "other/card-id",
					"style": {
						"navigationBarTitleText": "就诊人详情"
					}
				}, {
					"path": "other/card-new",
					"style": {
						"navigationBarTitleText": "创建就诊人"
					}
				},
				{
				    "path" : "filesList",
				    "style" :
				    {
				        "navigationBarTitleText": "完善档案",
				        "enablePullDownRefresh": false
				    }

				},
				{
				    "path" : "magList",
				    "style" :
				    {
				        "navigationBarTitleText": "消息通知",
				        "enablePullDownRefresh": false
				    }

				},
				{
					"path": "message",
					"style": {
						"navigationBarTitleText": "订阅消息"
					}
				},
				{
					"path": "headPortrait",
					"style": {
						"navigationBarTitleText": "上传头像"
					}
				},
				{
				    "path" : "deviceList",
				    "style" :
				    {
				        "navigationBarTitleText": "设备列表",
				        "enablePullDownRefresh": false
				    }
				}
			]
		},
		{
			"root": "pages/children",
			"pages": [
				{
					"path": "addBloodSugar",
					"style": {
						"navigationBarTitleText": "血糖添加",
						"enablePullDownRefresh": false
					}

				},
				{
					"path": "report",
					"style": {
						"navigationBarTitleText": "身高评估报告",
						"enablePullDownRefresh": false,
						// "navigationStyle": "custom"
						"backgroundColor": "#C59F79"
					}

				},
				{
					"path": "index",
					"style": {
						"navigationBarTitleText": "信息填写",
						"enablePullDownRefresh": false
					}

				},
				{
					"path": "add",
					"style": {
						"navigationBarTitleText": "添加身高",
						"enablePullDownRefresh": false
					}
				}, {
					"path": "list",
					"style": {
						"navigationBarTitleText": "身高记录",
						"enablePullDownRefresh": false
					}

				}, {
					"path": "info",
					"style": {
						"navigationBarTitleText": "身高记录详情",
						"enablePullDownRefresh": false
					}

				}, {
					"path": "update",
					"style": {
						"navigationBarTitleText": "修改历史记录",
						"enablePullDownRefresh": false
					}

				}
			]
		},
		{
			"root": "pages/integral",
			"pages": [
				{
					"path": "index",
					"style": {
						"navigationBarTitleText": "好物甄选",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "record",
					"style": {
						"navigationBarTitleText": "兑换记录",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "record-details",
					"style": {
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "details",
					"style": {
						"navigationBarTitleText": "商品详情",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "exchange",
					"style": {
						"navigationBarTitleText": "兑换成功",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/diabetes",
			"pages": [
				{
					"path": "info",
					"style": {
						"navigationBarTitleText": "食物详情",
						"enablePullDownRefresh": false
					}

				},
				{
					"path": "classification",
					"style": {
						/*"navigationBarTitleText": "列表",*/
						"enablePullDownRefresh": false
					}

				},
				{
					"path": "index",
					"style": {
						"navigationBarTitleText": "绑定设备"
					}

				}
			]
		},
		{
			"root": "pages/care",
			"pages": [
				{
					"path": "index",
					"style": {
						"navigationBarTitleText": "消息列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "detail",
					"style": {
						"navigationBarTitleText": "详情"
					}
				},
				{
					"path": "visitBack",
					"style": {
						"navigationBarTitleText": "复诊详情"
					}
				}
			]
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "中医健康管理平台",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#FFFFFF",
		"enablePullDownRefresh": false
	},
	"tabBar": {
		"color": "#9AA7B9",
		"selectedColor": "#2A3647",
		"borderStyle": "black",
		"backgroundColor": "#FFFFFF",
		"list": [{
				"pagePath": "pages/home/<USER>",
				"iconPath": "static/img/zy_bar_hom.png",
				"selectedIconPath": "static/img/zy_bar_hom_hl.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/task/index",
				"iconPath": "static/img/zy_bar_rew.png",
				"selectedIconPath": "static/img/zy_bar_rew_hl.png",
				"text": "打卡"
			},
			{
				"pagePath": "pages/index/index",
				"iconPath": "static/img/zy_bar_woek.png",
				"selectedIconPath": "static/img/zy_bar_woekhl.png",
				"text": "控糖"
			},
			{
				"pagePath": "pages/science/index",
				"iconPath": "static/img/zy_bar_anl.png",
				"selectedIconPath": "static/img/zy_bar_anl_hl.png",
				"text": "科普"
			},
			{
				"pagePath": "pages/my/index",
				"iconPath": "static/img/zy_bar_pre.png",
				"selectedIconPath": "static/img/zy_bar_pre_hl.png",
				"text": "我的"
			}
		]
	},
	"easycom": {
		"custom": {
			"autoscan": true,
			"gui-(.*)": "@/GraceUI5/components/gui-$1.vue"
		}
	}
}
