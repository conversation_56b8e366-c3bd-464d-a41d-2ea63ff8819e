<template>
	<view>
		<view class="my-list" v-if="listData.length > 0 ">
			<view class="gui-border-b pb-20" style="display: flex;justify-content:unset;"
				v-for="(item,index) in listData" :key="index"> <!-- @click="measure(item.id)" -->
				<view class="gui-list-body" >
					<view class=" fs-30 gui-flex gui-align-items-center" style="width: 100%;line-height: 50rpx;">
						<text class=" gui-color-gray"
							style="width: 100px;">{{$common.parseTime(item.measureDate,'{y}-{m}-{d}')}}</text>
						<text class=" gui-color-gray">{{item.age}}</text>
					</view>
					<view class=" mt-15 gui-flex gui-space-between gui-align-items-center" >
						<view class="fs-32">
							<text class=" gui-primary-color "
								style="width: 100px;float: left;">身高：{{item.height}}</text>
							<text class=" gui-primary-color ">体重：{{item.weight}}</text>
						</view>
						<view class="">
							<view class="gui-color-blue nodis" style="border-bottom: 1rpx solid #008AFF;"
								@click="update(item)">编辑</view>
							<view class="ml-20 gui-color-blue nodis" style="border-bottom: 1rpx solid #008AFF;"
								@click="measureDel(item.id)">删除
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="gui-margin-top" v-if=" listData.length <=0">
			<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
				<image class="gui-empty-img" src="../../static/kong.png"></image>
			</view>
			<text slot="text" class="gui-text-small gui-block-text gui-text-center gui-margin-top"
				style="color:#9DABFF;">
				暂无数据 ......
			</text>

		</view>
	</view>
</template>
<script>
	import {measureDel,measureinfo,measurelist} from '@/api/children.js'
	export default {
		data() {
			return {
				listData: [],
				updateType: null

			}
		},
		onShow() {
			this.list()
		},
		// onLoad() {
		// 	this.list()
		// },
		methods: {
			list() {
				let that = this
				measurelist({
					patientId: uni.getStorageSync("cardObj").patientId
				}).then(res=>{
					this.listData = res.rows
					this.$forceUpdate()
				})
			},
			measure(e) {
				measureinfo(e).then(res=>{
					if (res.code == 200) {
						if (this.updateType == null) {
							this.$common.navTo('/pages/children/info?info=' + JSON.stringify(res.data))
						}
						this.updateType = null
					
					}
				})
			},
			//编辑			
			update(e) {
				// console.log('编辑', e)
				this.updateType = e
				this.$common.navTo('/pages/children/update?item=' + JSON.stringify(e))
			},
			//删除
			measureDel(e) {
				let that = this
				this.updateType = e				
				uni.showModal({
					title: "提示",
					content: "是否确定删除该条数据",
					cancelText: '取消',
					confimrText: '确定',
					success: async (res) => {
						if (res.confirm) {	
							measureDel(e).then(res=>{
								that.list()
							})
						} else if (res.cancel) {}
					}
				});

			},
		}
	}
</script>
<style>
	.nodis {
		display: unset !important;
	}

	.my-list {
		margin: 30rpx 0;
		padding: 0 30rpx;
	}
</style>
