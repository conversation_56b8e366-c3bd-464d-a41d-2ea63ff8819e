<template>
	<gui-page pageBg="#f6f6f6" :fullPage="true" :isLoading="pageLoading"
		:apiLoadingStatus="apiLoadingStatus" :loadmore="true" @loadmorefun="getdata" ref="guipage">
		<view slot="gBody" class="bg-headlthy">
			<view style="background-color: #fff;padding: 10rpx 0;" v-if="!shareType">
				<view class='healthy-head'>
					<view class='healthy-head-title'>快来记录您的健康变化吧~</view>
					<view class="healthy-head-img">
						<image class="img-camera" @click="chooseClick" src="@/static/camera.svg"></image>
						<image class="img-camera" @click="shareClick" src="@/static/share.svg"></image>
					</view>
				</view>
			</view>
			<checkbox-group style="margin-top: 40rpx;" @change="onchange">
				<view v-for="(item, idx) in demoData" :key="item.id" class="demo">
					<checkbox class="checkBox" v-if="shareShow" :value="item.id" :checked="checkbox.indexOf(item.id)>-1" />
					<view class="line_box">
						<view class="line_radio"></view>
						<view class="line_for"></view>
					</view>
					<view class="list-row">
						<view class="list-row-date">
							<view style="display: flex;align-items: center;">
								<view>{{item.recordTime}}</view>
								<view class="tags-feedback" v-if="item.feedbackContent">已反馈</view>
							</view>
							<view class="list-row-date-btn" v-if="!shareType">
								<text class="date-btn" @click="editClick(item)">编辑</text>
								<text class="date-btn" @click="delClick(item,idx)">删除</text>
							</view>
						</view>
						<view class="list-text">
							<view class="list-item-img gui-flex gui-rows gui-wrap" v-if="item.imgUrl">
								<image @click="clickImg(e,item.imgUrl)" mode="aspectFill" v-for="(i,e) in item.imgUrl.split(',')" :class="item.imgUrl.split(',').length==1?'list-item':'list-item-i'"
									:src="i" />
							</view>
							<view class="list-text-item">{{item.content}}</view>
							<view class="list-text-item" style="border-top: 1px solid #eee;" v-if="item.feedbackContent">医生反馈：{{item.feedbackContent}}</view>
						</view>
					</view>
				</view>
			</checkbox-group>
			<gui-empty v-if="demoData.length == 0">
				<view slot="img" class="gui-flex empty">
				<!-- 请根据您的项目要求制作并更换为空图片 -->
					<image class="gui-empty-img" 
					src="https://upload-images.jianshu.io/upload_images/15372054-1f849183cebb80b1.png?imageMogr2/auto-orient/strip|imageView2/2/w/388/format/webp"></image>
					<text slot="text" 
					class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#9DABFF;">暂无数据 ......</text>
				</view>
			</gui-empty>
			<view class="gui-img-in" v-if="shareShow">
				<view class="btn-row">
					<view @click="shareNo" v-if="checkbox.length==0" class="btn-row-item" style="border-right: 2rpx solid #eee;padding: 0 14px;">
						<text class="gui-icons btn-row-icon" style="color: #999;">&#xe622;</text>
						<text style="color: #999;">一键分享</text>
					</view>
					<button v-if="checkbox.length>0" class="btn-row-item" open-type="share" style="border-right: 2rpx solid #eee;">
						<text class="gui-icons btn-row-icon">&#xe622;</text>
						<!-- <image class="btn-row-item-icon" src="@/static/share.svg" /> -->
						<text>一键分享</text>
					</button>
					<view class="btn-row-item" style="padding: 0 14px;" @click="shareShow = false">
						<image class="btn-row-item-icon" src="@/static/quxiao.png" />
						<text>取消</text>
					</view>
				</view>
				<!-- iphone 底部操作按钮躲避 -->
				<gui-iphone-bottom></gui-iphone-bottom>
			</view>
		</view>
	</gui-page>
</template>
<script>
	import {diaryList,diaryDel} from '@/api/home.js'
	export default {
		data() {
			return {
				page:1,
				shareShow: false,
				checkbox: [],
				img: [1],
				demoData: [],
				pageLoading: true,
				// 用于记录是否有 api 请求正在执行
				apiLoadingStatus: false,
				shareType:false //true是分享进来的
			}
		},
		onLoad: function(e) {
			this.demoData = [];
			this.checkbox = e.id?e.id.split(','):[]
			this.shareType = e.shareType?JSON.parse(e.shareType):false
		},
		onShow() {
			this.page = 1;
			// 页码加载时第一次加载数据
			this.getdata();
		},
		onShareAppMessage(res) {
			let id = this.checkbox.join(',')
			var imgUrl = this.demoData.find(item=>{return item.id==this.checkbox[0]}).imgUrl
			// this.shareShow = false
		    var shareObj = {
				title: '健康日记分享',    // 默认是小程序的名称(可以写slogan等)
				path: '/pages/homeAc/healthy/healthy?id='+id+'&shareType='+true,    // 默认是当前页面，必须是以‘/'开头的完整路径
				imageUrl: imgUrl.split(',')[0]
			}
		    // 返回shareObj
		    return shareObj;
		},
		methods: {
			// 分享
			shareNo(){
				this.$common.msg('请选择要分享的内容', "none");
			},
			// 删除
			delClick(e,index){
				this.$common.model('温馨提示','确定删除'+e.recordTime+'健康记录吗',success=>{
					if(success.confirm){
						diaryDel(e.id).then(res=>{
							this.demoData.splice(index,1)
							this.$common.msg('删除成功', "success");
						})
					}
				})
			},
			// 编辑
			editClick(e){
				this.$common.navTo('/pages/homeAc/healthy/release?id='+e.id)
			},
			// 分享
			shareClick() {
				if(this.demoData.length == 0){
					return
				}
				this.checkbox.push(this.demoData[0].id)
				this.shareShow = true
			},
			// 多选
			onchange(e) {
				this.checkbox = e.detail.value
			},
			// 图片预览
			clickImg(e,list) {
				var arr = list.split(',')
				wx.previewImage({
					current:arr[e],
					urls: arr,
				})
			},
			// 上传
			chooseClick() {
				var that = this
				uni.chooseImage({
					count: 6,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: function(res) {
						// uni.showLoading({
						// 	title: '图片上传中'
						// });
						that.$common.navTo('/pages/homeAc/healthy/release?itemlist=' + JSON.stringify(res.tempFilePaths))
						// var i = 0
						// var list = []
						// res.tempFilePaths.map(item => {
						// 	that.$common.uploadFile(item, resx => {
						// 		i++
						// 		list.push(resx.data.url)
						// 		console.log(resx, 'pppp')
						// 		if (i == res.tempFilePaths.length) {
						// 			that.$common.navTo(
						// 				'/pages/homeAc/healthy/release?itemlist=' + JSON
						// 				.stringify(list))
						// 		}
						// 	}, fail => {
						// 		i++
						// 		if (i == res.tempFilePaths.length) {
						// 			that.$common.navTo(
						// 				'/pages/homeAc/healthy/release?itemlist=' + JSON
						// 				.stringify(list))
						// 		}
						// 	})
						// })
					}
				})
			},
			// 刷新函数
			getdata: function() {
				this.apiLoadingStatus = true;
				diaryList({
					patientId:this.shareType?'':uni.getStorageSync('cardObj').patientId,
					pageNum:this.page,
					pageSize:10,
					ids:this.shareType?this.checkbox.join(','):''
				}).then(res=>{
					var demoArr = res.rows
					// .map(item=>{
					// 	let i = item.imgUrl?item.imgUrl.split(','):[]
					// 	item.imgUrlarr = i
					// })
					if (this.page >= 2) {
						this.demoData = this.demoData.concat(demoArr)
						// 加载完成后停止加载动画
						this.$refs.guipage.stoploadmore();
						// 假定第3页加载了全部数据，通知组件不再加载更多
						// 实际开发由接口返回值来决定
						if (this.page >= Math.ceil(Number(res.rows.total / 10))) {
							this.$refs.guipage.nomore();
						}
					}
					// 第一页数据
					else {
						this.demoData = demoArr
						this.pageLoading = false;
					}
					this.page++;
					this.apiLoadingStatus = false;
				})
			}
		}
	}
</script>
<style scoped>
	.tags-feedback{
		font-size: 22rpx;
		color: #fff;
		background-color:#c59f79;
		border-radius: 34rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 80rpx;
		height: 34rpx;
		margin-left: 10rpx;
	}
	.empty{
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	.btn-row {
		display: flex;
		flex-direction: row;
	}
	.gui-img-in{
		background-color: #fff;
		position: fixed;
		bottom: 0;
		width: 100%;
		font-size:35rpx;
	}
	.btn-row-item {
		flex: 1;
		display: flex;
		line-height: 100rpx !important;
		background-color: #fff;
		height: 100rpx;
		font-size: 35rpx;
		flex-direction: row;
		color: #353535;
		align-items: center;
		justify-content: center;
		/* padding: 20rpx 0; */
	}
	.btn-row-icon{
		font-size: 40rpx;
		margin-right: 20rpx;
	}
	.btn-row-item-icon {
		width: 50rpx;
		height: 50rpx;
		margin-right: 20rpx;
	}

	.date-btn {
		text-decoration: underline;
		color: #353535;
		margin-left: 24rpx;
	}

	.list-item-img {
		/* display: flex;
		flex-direction: row; */

	}

	.list-item-i {
		width: 30% !important;
		height: 200rpx;
		margin: 10rpx 10rpx;
	}

	.list-text-item {
		padding: 20rpx 10rpx;
		font-size: 30rpx;
		line-height: 50rpx;
	}

	.list-row {
		display: flex;
		flex-direction: column;
		flex: 1;
		margin-left: 20rpx;
		padding-bottom: 40rpx;
	}

	.bg-headlthy {
		/* min-height: 100%;
		background-color: rgba(246, 245, 245, 1); */
	}

	.list-text {
		display: flex;
		flex-direction: column;
		border-radius: 4rpx;
		margin-top: 12rpx;
		padding: 10rpx;
		width: 100%;
		background-color: #fff;
	}

	.list-item {
		/* flex:1 !important; */
		width: 100%;
		height: 300rpx;
		margin: 10rpx 10rpx;
	}

	.healthy-head {
		display: flex;
		padding: 10rpx 0;
		margin: 20rpx;
		font-size: 30rpx;
		flex-direction: row;
		border: 2rpx dashed rgba(255, 141, 26, 1);
	}

	.list-row-date {
		margin-left: 16rpx;
		font-size: 32rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.healthy-head-img {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-left: 40rpx;
	}

	.healthy-head-title {
		flex: 1;
		text-align: right;
		font-size: 32rpx;
	}

	.img-camera {
		width: 50rpx;
		height: 50rpx;
		margin: 0 20rpx;
	}

	.demo {
		/* padding: 30rpx; */
		display: flex;
		flex-direction: row;
		/* width: 100%; */
		padding: 0 30rpx;
	}

	.line_box {
		display: flex;
		margin-top: 10rpx;
		flex-direction: column;
		/* justify-content: center; */
		align-items: center;
	}

	.line_radio {
		width: 18rpx;
		height: 18rpx;
		border-radius: 100%;
		background-color: rgba(153, 153, 153, 1);
	}

	.line_for {
		height: 100%;
		width: 2rpx;
		background-color: rgba(229, 229, 229, 1);

	}

	.checkBox {
		width: 70rpx;
		height: 60rpx;
		transform: scale(0.9);
	}
</style>
<style>
	checkbox.checkBox[checked] .wx-checkbox-input,
	checkbox.checkBox.checked .uni-checkbox-input {
		background-color: rgba(255, 141, 26, 1) !important;
		border-color: rgba(255, 141, 26, 1) !important;
	}

	checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
		font-size: 40rpx !important;
		color: #ffffff !important;
	}
</style>