<template>
	<gracePage :customHeader="false">
		<view slot="gBody" class="p-30">
			<view>
				<view class="font-bold text-black fs-36 mb-20">复诊提醒</view>
				<view>
					<text class="text-grey-b2 fs-24">{{model.visitTime}}</text>
				</view>
				<view class="text-justify mt-30" style="word-wrap:break-word"
					v-html="$common.adjustRichTextImageSize(model.content)"></view>
			</view>
		</view>
	</gracePage>
</template>

<script>
	import {
		backUpdate,
		zwbBack
	} from '@/api/visitBack.js'
	export default {
		data() {
			return {
				useropenId: uni.getStorageSync('cardObj').patientId,
				id: null,
				type: null,
				model: {
					articleName: '',
					content: '',
					createTime: "",
					articleType: ""
				}
			}
		},
		onLoad(option) {
			if (option?.id) {
				this.getdata(option.id);
			}
		},
		methods: {
			getdata(id) {
				zwbBack(id).then(res => {
					this.model = res.data
					backUpdate({
						id:id,
						readFlag: '1',
					}).then(ress =>{
						
					})
				})
			},
			//调整富文本图片大小
			adjustRichTextImageSize(text) {
				return text;
			}
		}
	}
</script>

<style>
	page {
		background: #fff;
	}
</style>