<template>
	<gui-page :fullPage="true" :isLoading="pageLoading" ref="guiPage">
		<view slot="gBody" class="gui-flex1">
			<!-- 搜索框 -->
			<!-- <view class="header-search gui-bg-gray gui-border-box">
				<gui-search @clear="search" @confirm="search"></gui-search>
			</view> -->
			<!-- 主体区域 -->
			<view class="gui-flex gui-rows gui-space-between" v-if="mainCate.length > 0">
				<!-- 左侧分类列表 -->
				<scroll-view :scroll-y="true" :show-scrollbar="false" 
				:scroll-with-animation="true" :scroll-into-view="leftTo" 
				class="gui-cate-left" :style="{height:mainHeight+'px'}">
					<view class="gui-cate-left-item gui-border-box" 
					v-for="(item, index) in mainCate" :key="index" 
					:class="[currentCateIndex == item.id ? 'gui-cate-left-current' : '']" 
					>
						<text class="gui-border-l" 
						:class="['gui-cate-left-item-title','gui-block-text', currentCateIndex == item.id ? 'gui-cate-left-current' : '']" 
						@tap="changCate(item.id)">{{item.tgiftType}}</text>
					</view>
				</scroll-view>
				<!-- 右侧列表 -->
				<scroll-view 
				:scroll-into-view="productListTo" :show-scrollbar="false" 
				:scroll-with-animation="true" :scroll-y="true" 
				class="gui-cate-right" 
				:style="{height:mainHeight+'px'}">
					<!-- 兑换记录 -->
					<view class="record" @click="record">
						<view>兑换</view>
						<view>记录</view>
					</view>
					<!-- 循环展示分类及分类对应的产品列表 -->
					<view class="gui-margin-top"  v-if="products.length > 0"
					v-for="(item, index) in products" :key="index" @click="productsClick(item.id)">
						<!-- <text class="gui-block-text gui-h6 gui-bold gui-color-gray gui-cate-right-title">{{cate.name}}</text> -->
						<!-- 循环展示产品 -->
						<view class="gui-flex gui-rows gui-nowrap gui-cate-product-list gui-border-b" >
							<view class="gui-cate-pimg">
								<gui-image :src="item.cover" :width="180" borderRadius="10rpx"></gui-image>
							</view>
							<view class="gui-cate-pbody gui-flex gui-columns gui-space-between">
								<view class="gui-text gui-primary-color gui-block-text ellipsis-1">{{item.name}}</view>
								<view class="gui-flex gui-rows gui-nowrap gui-justify-content-end">
									<text v-if="item.giftStatus == 1" class="gui-cate-price gui-color-red">{{item.integral}} 积分</text>
									<!-- <text v-else class="gui-cate-price gui-color-red " style="font-size: 28rpx;">不支持兑换</text> -->
								</view>
								<!-- <view class="gui-flex gui-rows gui-nowrap gui-space-between">
									<text></text>
									<text class="gui-icons gui-text-small gui-color-gray">Button</text>
								</view> -->
							</view>
						</view>
					</view>
					<view v-if="products.length <= 0">
						<gui-empty>
							<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
								<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
							</view>
							<text slot="text" 
							class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
						</gui-empty>
					</view>
				</scroll-view>
			</view>
			<view v-if="mainCate.length <= 0">
				<gui-empty>
					<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
						<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
					</view>
					<text slot="text" 
					class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
				</gui-empty>
			</view>
		</view>
	</gui-page>
</template>
<script>
var graceJS = require('@/GraceUI5/js/grace.js');
var cateChangeData = require('@/GraceUI5/demoData/cateChange.js');
	import { giftTypeList,giftList } from '@/api/integral.js'
export default {
	data() {
		return {
			img : "https://images.unsplash.com/photo-1660505465468-c898ea7ff674?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHx0b3BpYy1mZWVkfDQ2fHhqUFI0aGxrQkdBfHxlbnwwfHx8fA%3D%3D&auto=format&fit=crop&w=200&q=90",
			// 全屏加载，当数据请求完毕时关闭
			pageLoading : true,
			// 核心区域高度
			mainHeight  : 300,
			// 左侧分类定位
			leftTo      : 'cate',
			// 标识切换时左侧点击触发的
			isLeftTap   : false,
			// 左侧分类数据
			mainCate    : [],
			// 当前分类
			currentCateIndex : 0,
			// 延迟执行防止卡顿
			scrollTimer : null,
			// 产品列表滚动定位
			productListTo: '',
			products:[]
		}
	},
	onLoad:function(){
		// 获取页面主体高度从而得出主体高度
		graceJS.getRefs('guiPage', this, 0, (ref)=>{
			ref.getDomSize('guiPageBody', (e)=>{
				// 02. 导航的高度
				// 可以 使用 graceJS.select() 函数动态获取， 也可以使用 uni.upx2px 转换已知高度
				this.mainHeight  = e.height;
				setTimeout(()=>{
					this.pageLoading = false;
				},1000)
			});
		});
	},
	onShow() {
		this.getListType()
	},
	methods: {
		getListType(){
			giftTypeList().then(res => {
				if (res.total > 0) {
					this.mainCate = [{tgiftType:"全部",id:0}].concat(res.rows);
					this.getList()
				}
			})
		},
		getList(e){
			giftList({giftTypeId:e?e:this.currentCateIndex}).then(res =>{
				this.products = res.rows
			})
		},
		changCate : function (e) {
			var cateid = e
			this.currentCateIndex = cateid;
			this.getList(this.currentCateIndex);
		},
		record(e){
			if (uni.getStorageSync('cardObj') && uni.getStorageSync('cardObj').patientId) {
				this.$common.navTo("/pages/integral/record")
			} else {
				this.$common.msg("请先绑定就诊卡")
			}
			// uni.navigateTo({
			//      url: './record'
			// })
		},
		productsClick(e){
			this.$common.navTo("/pages/integral/details?id=" +e)
			// uni.navigateTo({
			//      url: './details'
			// })
			// console.log(e)
		}
	}
}
</script>
<style scoped>
	.record{
		position: fixed;
		bottom: 90rpx;
		background-color: #C59F79;
		border-radius: 50%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 100rpx;
		height: 100rpx;
		color: #fff;
		right:10rpx
	}
.header-search{padding:20rpx 80rpx; width:750rpx;}
.gui-cate-left{width:200rpx; background-color:#F6F7F8;}
.gui-cate-right{width:520rpx; overflow:hidden;}
.gui-cate-left-item{height:100rpx; padding:35rpx 0; font-size:26rpx;}
.gui-cate-left-item-title{line-height:32rpx; font-size:28rpx; border-color:#F6F7F8; border-left-width:8rpx; text-align:center;}
.gui-cate-left-current{border-color:#C59F79 !important; background-color:#FFFFFF; color:#C59F79; font-weight:bold;}
.gui-cate-right-title{line-height:80rpx;}
.gui-cate-product-list{padding-bottom:30rpx;padding-right:25rpx;}
.gui-cate-pimg{width:180rpx;}
.gui-cate-pbody{
	margin-left:30rpx; width:100rpx; flex:1; padding-top:-20rpx;
}
.gui-block-text{
	font-size: 34rpx;
}
.gui-cate-price{font-size:32rpx; line-height:60rpx;}
</style>
