export default {
// 菜单
	menu(){
		var list = [//通用版
			{menuName:'科室介绍',icon:'/static/img/ksjs.png',key:'departmentIntroduction'},
			{menuName:'健康记录',icon:'/static/img/jkjl.png',key:'healthRecord'},
			{menuName:'体质测评',icon:'/static/img/cpwj.png',key:'constitutionEvaluation'},
			{menuName:'在线咨询',icon:'/static/img/jkzx.png',key:'onlineConsultation'},
			{menuName:'健康建议',icon:'/static/img/tljy.png',key:'conditioningSuggestion'},
			{menuName:'健康日志',icon:'/static/img/jkrj.png',key:'healthDiary'},
			{menuName:'好物甄选',icon:'/static/img/hwzx.png',key:'goodSelection'},
			{menuName:'治疗预约',icon:'/static/img/zy_bar_servhl.png',key:'treatmentAppointment'},
			// {menuName:'智能舌象',icon:'/static/homeImg/weijue.png',key:'intelligenttongue'},
			{menuName:'体检报告',icon:'/static/log/jianyans.png',key:'physicalExaminationReport'},
			// {menuName:'我要答题',icon:'/static/homeImg/yuedu.png',key:'answerChallenge'},
			{menuName:'主动健康',icon:'/static/log/device.png',key:'activeHealthService'},
			// {menuName:'疾病筛查',icon:'/static/homeImg/kangjian.png',key:'kangchaAI'},
		];
		
		return {
			list,
		}
	},
	// 时间格式化
	formatDate(d, format = 'yyyy-MM-dd') {
		if (!d) return '';
		let date = d;
		switch (typeof date) {
			case 'string':
				date = new Date(date.replace(/-/g, '/'));
				break;
			case 'number':
			default:
				date = new Date(date);
		}
		if (!(date instanceof Date)) return '';

		const dict = {
			yyyy: date.getFullYear(),
			M: date.getMonth() + 1,
			d: date.getDate(),
			H: date.getHours(),
			m: date.getMinutes(),
			s: date.getSeconds(),
			MM: (`${date.getMonth() + 101}`).substr(1),
			dd: (`${date.getDate() + 100}`).substr(1),
			HH: (`${date.getHours() + 100}`).substr(1),
			mm: (`${date.getMinutes() + 100}`).substr(1),
			ss: (`${date.getSeconds() + 100}`).substr(1),
		};
		try {
			return format.replace(/(yyyy|MM?|dd?|HH?|ss?|mm?)/g, f => dict[f]);
		} catch (e) {
			return '';
		}
	},
	//调整富文本图片大小
	adjustRichTextImageSize(text){
		text=text.replace(/\<img/gi, '<img style=width:100%;margin:auto;margin-left:-2em;height:auto;');
		return text
	},
	// 转义非法字符
	escapeCharacter(str = '') {
		return str
			.replace(/&ldquo;/g, '“').replace(/&rdquo;/g, '”')
			.replace(/&lsquo;/g, '‘').replace(/&rsquo;/g, '’')
			.replace(/&quot;/g, '"')
			.replace(/&#039;/g, "'")
			.replace(/&lt;/g, '<')
			.replace(/&gt;/g, '>')
			.replace(/&hellip;&hellip;/g, '……')
			.replace(/&mdash;&mdash;/g, '——')
			.replace(/&amp;/g, '&');
	},

	// 字符串转html
	unescapeHTML(value) {
		const val = value.toString();
		return val.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&')
			.replace(/&quot;/g, '"').replace(/&apos;/g, "'"); // eslint-disable-line
	},

	// 判断数据类型
	getObjType(obj) {
		var toString = Object.prototype.toString
		var map = {
			'[object Boolean]': 'boolean',
			'[object Number]': 'number',
			'[object String]': 'string',
			'[object Function]': 'function',
			'[object Array]': 'array',
			'[object Date]': 'date',
			'[object RegExp]': 'regExp',
			'[object Undefined]': 'undefined',
			'[object Null]': 'null',
			'[object Object]': 'object'
		}
		if (obj instanceof Element) {
			return 'element'
		}
		return map[toString.call(obj)]
	},
	/**
	 * 对象深拷贝
	 */
	deepClone(data) {
		var type = getObjType(data)
		var obj
		if (type === 'array') {
			obj = []
		} else if (type === 'object') {
			obj = {}
		} else {
			// 不再具有下一层次
			return data
		}
		if (type === 'array') {
			for (var i = 0, len = data.length; i < len; i++) {
				obj.push(deepClone(data[i]))
			}
		} else if (type === 'object') {
			for (var key in data) {
				obj[key] = deepClone(data[key])
			}
		}
		return obj
	},
	/**
	 * 根据字典的value查找对应的index
	 */
	findArray(dic, value) {
		for (let i = 0; i < dic.length; i++) {
			if (dic[i].value === value) {
				return i
			}
		}
		return -1
	},
	/* 验证pad还是pc */
	vaildatePc() {
		const userAgentInfo = navigator.userAgent
		const Agents = ['Android', 'iPhone',
			'SymbianOS', 'Windows Phone',
			'iPad', 'iPod'
		]
		let flag = true
		for (var v = 0; v < Agents.length; v++) {
			if (userAgentInfo.indexOf(Agents[v]) > 0) {
				flag = false
				break
			}
		}
		return flag
	},

	// 数组最大值
	arrayMax(arr) {
		return Math.max.apply(null, arr)
	},
	// 数组最小值
	arrayMin(arr) {
		return Math.min.apply(null, arr)
	},
	// 数组并集
	arrayUnion(arr1, arr2) {
		return [...new Set([...arr1, ...arr2])]
	},
	// 数组交集
	arrayIntersect(arr1, arr2) {
		// let arr3 = [...arr1].filter(value => arr2.includes(value))
		// return [...new Set([...arr3])]
		return [...new Set([...arr1].filter(value => arr2.includes(value)))]
	},
	// 数组差集
	arrayDiff(arr1, arr2) {
		return [...new Set([...arr1].filter(value => !arr2.includes(value)))]
	},
	// 数组去重
	arrayUnique(arr) {
		return [...new Set([...arr])]
	}
}
