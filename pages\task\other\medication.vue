<template>
	<view>
		<view v-if="obj.whetherPerform == -1">
			<view style="padding:50rpx 0;">
				<text slot="text"class="gui-block-text gui-text-center gui-margin-top text-zhuti" >此内容已经被撤回...</text>
			</view>
		</view>
		<view v-else-if="obj.guidMonitorInfo.monitorName">
			<view class="ml-20 fs-36 font-bold">药品名称：</view>
			<view class="gui-h3 p-20 text-zhutis ml-30">{{obj.guidMonitorInfo.monitorName || '-'}}</view>
			<!-- <view class="d-flex jc-between px-20 fs-30 text-grey-74 my-15">
				<view>下发人：{{obj.taskInfo.createBy || '-'}}</view>
				<view>下发时间：{{obj.taskInfo.createTime || '-'}}</view>
			</view> -->
			<view v-if="obj.guidMonitorInfo.monitorContent && obj.guidMonitorInfo.articleType == 2" class="p-20" style="line-height: 50rpx;text-align: justify;word-wrap:break-word">
				<u-parse :monitorContent="$common.adjustRichTextImageSize(obj.guidMonitorInfo.monitorContent)"></u-parse>
			</view>
			<view v-if="obj.guidMonitorInfo.monitorContent && obj.guidMonitorInfo.articleType != 2" class="p-20" style="line-height: 50rpx;text-align: justify;word-wrap:break-word" v-html="$common.adjustRichTextImageSize(obj.guidMonitorInfo.monitorContent)"></view>
			
			<view class="mb-40 mx-20 p-20 fs-36" style="background-color: #F9F9FB;">
				<view class="mt-10 gui-flex">
					用法：
					<view class="text-zhutis gui-flex1">
						{{obj.plasterMonitorSet.monitorUsedes}}&nbsp;
						<!-- 每次{{obj.plasterMonitorSet.singleQuantity && obj.plasterMonitorSet.singleQuantity || ''}}{{obj.plasterMonitorSet.singleUnitdes || ''}}
						每{{(obj.plasterMonitorSet.monitorUnit === 1 && '天') || (obj.plasterMonitorSet.monitorUnit === 2 && '周') || (obj.plasterMonitorSet.monitorUnit === 3 && '月') || ''}}
						 {{(obj.plasterMonitorSet.monitorDays && `${obj.plasterMonitorSet.monitorDays}次`) || (obj.plasterMonitorSet.monitorDays === 1 && '1次' || '')}}
						 {{(obj.plasterMonitorSet.dates && `(第${obj.plasterMonitorSet.dates}天执行)`) || ''}} -->
					</view>
				</view>
				<view class="mt-20 mb-20 gui-flex">
					用药频率：
					<view class="text-zhutis gui-flex gui-wrap gui-flex1">
						每次{{obj.plasterMonitorSet.singleQuantity && obj.plasterMonitorSet.singleQuantity || ''}}{{obj.plasterMonitorSet.singleUnitdes || ''}}
						每{{(obj.plasterMonitorSet.monitorUnit === 1 && '天') || (obj.plasterMonitorSet.monitorUnit === 2 && '周') || (obj.plasterMonitorSet.monitorUnit === 3 && '月') || ''}}
						 {{(obj.plasterMonitorSet.monitorDays && `${obj.plasterMonitorSet.monitorDays}次`) || (obj.plasterMonitorSet.monitorDays === 1 && '1次' || '')}}
						 {{(obj.plasterMonitorSet.dates && `(第${obj.plasterMonitorSet.dates}天执行)`) || ''}}
					</view>
				</view>
				<view class="mt-20 mb-20">
					执行时间：<text class="text-zhutis">{{obj.plasterMonitorSet.times}}</text>
				</view>
					医生医嘱：<view class="text-zhutis gui-flex1">{{obj.plasterMonitorSet.remark || '-'}}</view>
				<!-- 医生回复 -->
				<view v-if="obj.replyDoubt">
					<view class="text-zhuti mt-40 px-20" style="font-weight: bold;">{{obj.replyDoubt.createBy || ''}}回复：{{obj.replyDoubt.content || ''}}</view>
					<view class="text-zhuti px-20 mt-20" style="font-weight: bold;">回复时间：{{obj.replyDoubt.createTime || ''}}</view>
				</view>
				<!-- <view class="fs-30 mt-15">反馈情况：</view> -->
				<!-- <view class="d-flex ai-center jc-around">
					<view @click="change(item.value)" :class="type == item.value ? 'blj-box2' : 'blj-box'" v-for="(item,index) in fedList" :key="index">
						<image :src="type == item.value ? item.img2 : item.img" mode="widthFix" class="w70 h70 mb-15"></image>
						<view :class="type == item.value ? 'text-white' : 'text-black'">{{item.options}}</view>
					</view>
				</view> -->
				<!-- <view  class="w-100 box-size-border pos-relative bg-white">
					<textarea v-model="reson" :disabled="obj.taskInfo.status != 0" placeholder="请输入" maxlength="200" class="box-size-border w-100 p-20 fs-30" placeholder-class="fs-30" />
					<view class="pos-absolute text-grey-b2" style="bottom: 15rpx;right: 30rpx;">{{reson.length}}/200</view>
				</view>
				<text class="text-zhutis mt-40 d-iblock fs-22">提示：{{obj.guidMonitorInfo.remark || '-'}}</text> -->
				<!-- 医生回复 -->
				<!-- <view v-if="obj.replyDoubt">
					<view class="text-zhuti mt-40 px-20" style="font-weight: bold;">{{obj.replyDoubt.createBy}}回复：{{obj.replyDoubt.content}}</view>
					<view class="text-zhuti fs-28 px-20 mt-20" style="font-weight: bold;">回复时间：{{obj.replyDoubt.createTime}}</view>
				</view> -->
			</view>
			<view style="height: 180rpx;"></view>
			<!-- <view v-if="obj.guidMonitorInfo.whetherPerform == 0" class="bg-white w-100 pos-fixed box-size-border box-shadows py-20 px-40 gui-border-radius-small" style="height: 130rpx; bottom: 0;left: 0;z-index: 9;">
				<view @click="save" class="bg-zhuti text-white w-100 h-100 d-flex ai-center jc-center">提交反馈</view>
			</view> -->
		</view>
		
	</view>
</template>

<script>
	import {getFollowTaskInfo,submitGuidMonitor,submitPlasterMonitor } from '@/api/task.js'
	import uParse from '@/components/u-parse/u-parse.vue'
	export default {
		components:{uParse},
		data() {
			return {
				id:"",
				taskType:'',
				type:"",
				reson:"",
				obj:{
					guidMonitorInfo:{},
					replyDoubt:{},
					taskInfo:{},
				},
				textareaVal: '', // 文本内容
				maxWords: 50, // 最多字符数
				textareatimer: null, // 延迟记录
				fedList: [{
						options: '完全理解',
						img: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_ok.png',
						img2: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_okw.png',
						value:"1"
					},
					{
						options: '基本理解',
						img: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_lit.png',
						img2: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_litw.png',
						value:"2"
					},
					{
						options: '不理解',
						img: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_qu.png',
						img2: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_quw.png',
						value:"3"
					},
				],
				fedIndex: '',
			}
		},
		onLoad(options) {
			this.id = options.id;
			this.taskType = options.taskType
			this.getDetail();
		},
		methods: {
			getDetail(){
				getFollowTaskInfo({
					id:this.id,
					taskType:this.taskType
				}).then(res=>{
					this.obj = res.data
					this.type = res.data.taskInfo.isUnderstand ? res.data.taskInfo.isUnderstand : ""
					// 康养
					if (res.data?.guidMonitorInfo) {
					  this.reson = res.data?.guidMonitorInfo?.feedback || ""
					}
					// 膏方
					if (res.data?.plasterMonitorInfo) {
					  this.obj.guidMonitorInfo = res.data.plasterMonitorInfo
					  this.reson = res.data?.plasterMonitorInfo?.feedback || ""
					  this.save()
					}
				})
			},
			change(value){
				if(this.obj.taskInfo.status != 0 ) {return}
				this.type = value;
			},
			save() {
				var obj = {
					patientId:uni.getStorageSync('user').id, // 系统用户id
					id:this.id,
					taskType:this.taskType,
					isUnderstand:this.type,
					feedback:this.reson
				}
				// 膏方
				if (this.obj?.plasterMonitorInfo){
					submitPlasterMonitor(obj).then(res=>{
						// this.$common.msg("反馈成功","success")
						// this.getDetail();
						// setTimeout(() => {
						// 	this.$common.navTab('/pages/task/index')
						// }, 1000)
					})
				}else{
					submitGuidMonitor(obj).then(res=>{
						// this.$common.msg("反馈成功","success")
						// this.getDetail();
						// setTimeout(()=>{
						// 	this.$common.navTab('/pages/task/index')
						// },1000)
					})
				}
			}
		}
	}
</script>

<style>
	.modal-btns {
		line-height: 88rpx;
		font-size: 26rpx;
		text-align: center;
		width: 200rpx;
	}

	.submit {
		width: 50%;
		line-height: 70rpx;
		position: fixed;
		bottom: 10rpx;
		left: 25%;
	}
	
	.blj-box{
		flex: 1;
		text-align: center;
		background-color: #fff;
		border-radius: 10rpx;
		padding: 30rpx 0;
		margin:30rpx 10rpx;
	}
	
	.blj-box2{
		flex: 1;
		text-align: center;
		background-color: #8FC17B;
		border-radius: 10rpx;
		padding: 30rpx 0;
		margin:30rpx 10rpx;
	}
</style>
