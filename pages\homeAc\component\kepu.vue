<template>
	<view class="">
		<view style="width: 100%;" class="shadow py-30" v-show="tabs.length > 0">
			<gui-switch-navigation activeLineWidth="30%" activeLineBg="linear-gradient(to right, #5D808F, #5D808F)"
				activeColor="#5D808F" lineHeight="40rpx" activeDirection="center" :items="tabs" :size="150"
				:currentIndex="currentIndex" @change="navchange">
			</gui-switch-navigation>
		</view>
		<view class="">
			<view class="gui-list-items" v-for="(item,index) in list" :key="index" @click="toDetail(item)">
				<view class="d-flex jc-between gui-border-b w-100 box-size-border ml-20 py-20">
					<view class="gui-flex gui-columns gui-space-between">
						<view class="gui-list-title-text gui-primary-color ellipsis-2 font-bold">{{item.articleName}}</view>
						<view >
							<view class="fs-26 gui-color-gray gui-flex mt-10">
								<view class=" gui-color-gray">{{item.createTime}}</view>
								<view class="gui-flex ml-30">
									<text class="gui-icons">&#xe609;</text>
									<text class="ml-10">{{item.readNum || 0}}</text>
								</view>
							</view>
						</view>
					</view>
					<image v-if="item.filePath" class="w200 h150 gui-border-radius-small" style="min-width: 200rpx;" :src="item.filePath" mode="aspectFill"></image>
				</view>
			</view>
			<view v-if="list.length<= 0">
				<gui-empty>
					<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
						<image class="gui-empty-img" style="margin-top: 20rpx;" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
					</view>
					<text slot="text" 
					class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
				</gui-empty>
			</view>
			<gui-loadmore ref="loadmorecom" :status="loadState" class="w-100" style="box-sizing: border-box;"></gui-loadmore>
		</view>
	</view>
</template>

<script>
	import {articleSpecialList,channelType} from '@/api/science.js'
	export default {
		name:'kepu',
		props:{
			departmentId: {
				type: String,
				default: '心血管',
			},
		},
		data(){
			return{
				loadingStatus:false, //  ， true： 请求中   false :请求结束
				loadState: 0 ,  // 0 : 默认0，有下一页   1 ：请求中  2： 加载完毕
				page:1,
				size:30,
				zongSize:0,
				list:[],
				// 选项卡标签
				tabs: [],
				// 选中选项的 索引
				currentIndex: 0
			}
		},
		watch:{
			departmentId:{
				deep:true,
				handler(newVal,oldVal){
					console.log("已改变的值=>",newVal);
					console.log("改变前的值=>",oldVal);
				}
			}
		},
		mounted() {
			this.$on('navchange', function() {
				this.getFl(this.departmentId);
			} )
		},
		methods: {
			toDetail(item){
				this.$common.openDyMsg(0,2);
				if(item.articleType==3){
					setTimeout(()=>{
						this.$common.navTo('/pages/task/other/aboutus?articleId='+ item.articleId)
					},300)
				}else{
					this.$common.navTo("/pages/science/detail?articleId="+item.articleId)
				}
			},
			getFl(e){
				channelType({appId:e?e:this.departmentId}).then(res =>{
					if (res.data.length > 0) {
						this.tabs = [{dictLabel:"全部",dictCode:""}].concat(res.data);//2023-5-17韦总觉得查询全部检索太慢了，隐藏掉全部的选项
						this.init();
					}
				});
			},
			confirm(e){
				this.keyword = e;
				this.init();
			},
			clear(){
				this.keyword = "";
				this.init();
			},
			// 初始化
			init(){
				this.page = 1;
				this.loadingStatus = false;
				this.loadState = 0;
				this.getList();
			},
			getList() {
				this.loadingStatus  = true;
				articleSpecialList({
					pageNum:this.page,
					pageSize:this.size,
					// articleName:this.keyword
					appId:this.departmentId,
					specialId:this.tabs[this.currentIndex].dictCode
				}).then(res=>{
					this.list = this.page == 1 ? res.rows : this.list.concat(res.rows);
					this.zongSize = res.total;
					if(this.list.length >= this.zongSize){
						this.loadState = 2; 
						this.loadingStatus  = false;
						return;
					}
					this.page++;
					this.loadState = 0;
					this.loadingStatus  = false;
				})
				this.$common.getNoReads();
			},
			change1: function(data) {
			},
			navchange: function(index) {
				this.currentIndex = index;
				this.init();
			},
		}
	}
</script>

<style scoped>
	.time{
		position: relative;
		bottom: -22rpx;
	}
	.gui-bg-orange{
		background: #41bcb3;
	}
	.shadow {
		box-shadow: 0px 8rpx 32rpx 0rpx #F1F1F1;
	}

	.tab-card-body {
		width: 690rpx;
		height: 388rpx;
		margin-top: 25rpx;
	}

	.tab-card-item {
		width: 690rpx;
		height: 388rpx;
	}

	.tab-card-demo-text {
		line-height: 388rpx;
		font-size: 26rpx;
	}
</style>