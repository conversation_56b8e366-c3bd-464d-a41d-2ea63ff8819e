<template>
	<view>
		<view v-for="(item,index) in list" :key="index" class="shadow" :class="index%2 != 0 ? 'bor2' : 'bor'" @tap="detail(item)">
			<image :src="index%2 != 0 ? 'https://img.starup.net.cn/xmkj/zwb/img/zy_car_ydjy.png' : 'https://img.starup.net.cn/xmkj/zwb/img/zy_car_jkjy.png'" mode="widthFix" class="w80 h80 mr-30"></image>
			<text class="flex-1 ellipsis-1 text-left fs-36 ">{{item.templateDictKey=='32'?'体质评估及健康指导手册':item.title}}</text>
		</view>
		<view v-if="list.length <= 0">
			<gui-empty>
				<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
					<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
				</view>
				<text slot="text"
				class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
			</gui-empty>
		</view>
		<view class="h50"></view>
	</view>
</template>

<script>
	import {heathList,getPhysicalReportList,previewPDF,postPartumPreviewPDF} from '@/api/home.js'
	export default {
		data(){
			return{
				list:[]
			}
		},
		onShow() {
			this.getList();
		},
		methods: {
			getList(){
				heathList({
					patientId : uni.getStorageSync('cardObj').patientId
				}).then(res=>{
					this.list = res.data;
				})
			},
			// 膳食建议
			detail(item) {
				if (item.templateDictKey == '32' || item.templateDictKey == '31') {
					if (item.fileData) {
						console.log('11111111')
						this.openPDF(item.wpId,item.templateDictKey)
					} else{
						console.log('222222222')
						this.getFileData(item.wpId,item.visitRecordId,item.templateDictKey);
					}
				} else{
					let id = uni.getStorageSync('cardObj').patientId;
					this.$common.navTo('/pages/homeAc/other/salDetail?templateDictKey='+item.templateDictKey+'&patientId='+id + "&title="+item.title + '&templateId=' + item.templateId + '&visitRecordId=' + item.visitRecordId+'&createTime='+item.createTime+'&programmeType='+item.programmeType)
				}

			},
			async getFileData(wpId,visitRecordId,templateDictKey){
				uni.showLoading({
					title: "报告生成中...",
				});
				var res = ''
				if(templateDictKey == 31){
					res = await postPartumPreviewPDF({patientId : uni.getStorageSync('cardObj').patientId,visitRecordId:visitRecordId})
				}else{
					res = await previewPDF({patientId : uni.getStorageSync('cardObj').patientId,visitRecordId:visitRecordId})
				}
				if(res){
					if(res.code == 200){
						uni.hideLoading();
						this.openPDF(wpId,templateDictKey)
					}else{
						this.$common.msg('报告生成失败，请联系医生。')
						uni.hideLoading();
					}
				}
			},
			// 查看PDF报告
			openPDF(wpId,templateDictKey) {
				// console.log('记录详情数据==',item) heath
				let url =templateDictKey ==31?this.$common.domain+'/wx/postPartumTemplate/getPhysicalReport':this.$common.domain+'/wx/heath/getPhysicalReport'
				console.log(wpId,url,'wpId')
			  switch (uni.getSystemInfoSync().platform) {
			    case "android":
			      // console.log("安卓");
			      // 这里直接调用原生的方法，我们下面定义的
			      this.androidOpenPdf(url+'?id='+wpId);
			      break;
			    case "ios":
			      // console.log("IOS");
			      //这里跳转web-view页面
			      uni.navigateTo({
			        url: "/pages/tongue/component/openH5?openUrl=" + url+'&openType=2&reportId='+wpId,
			      });
			      break;
			    default:
			      this.androidOpenPdf(url);
			      break;
			  }
			},
			androidOpenPdf(url) {
			  uni.downloadFile({
			    url: url,
			    success: function (res) {
			      var filePath = res.tempFilePath;
			      uni.openDocument({
			        filePath: filePath,
			        success: function (res) {
			          // console.log("打开文档成功");
			        },
			      });
			    },
			  });
			},
		},
	}
</script>

<style>
	.bor {
		text-align: center;
		padding: 20rpx 30rpx;
		display: flex;
		align-items: center;
		background: #A18E7A;
		line-height: 100rpx;
		margin: 40rpx 30rpx;
		color: #fff;
		border-radius: 10rpx;
	}
	.bor2 {
		text-align: center;
		padding: 20rpx 30rpx;
		display: flex;
		align-items: center;
		background: #5D808F;
		line-height: 100rpx;
		margin: 40rpx 30rpx;
		color: #fff;
		border-radius: 10rpx;
	}
</style>
