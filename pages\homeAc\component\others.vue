<template>
	<view class="p-20 fs-32">
		<view class="mt-20 mb-50">
			<view class="d-flex mb-20">
				<view>姓名：{{cardObj.userName}}</view>
				<view class="ml-120">性别：{{cardObj.sex == 1 ? "男" : "女"}}</view>
			</view>
			<view>时间：{{obj.create_time ? $common.parseTime(obj.create_time) : '-'}}</view>
		</view>
		<view v-for="(item,index2) in list" :key="index2" class="mb-40">
			<view>{{index2+1}}. {{item.timu}}</view>
			<view v-if="(templateDictKey == 6 || templateDictKey == 9 || templateDictKey == 15|| templateDictKey == 16|| templateDictKey == 22) && item.key == 'coolFoodMore'" class="pt-30 text-grey-74" style="line-height: 45rpx;">
				<view v-for="item2 in JSON.parse(obj[item.key])" :key="item2.name">
					<text>{{item2.name}}:</text>
					<text v-if="item3.check" v-for="item3 in item2.list" :key="item3.name">
						{{item3.name}}-{{item3.check}}；
					</text>
					<view>{{item2.name}}说明：{{item2.desc || '-'}}</view>
				</view>
			</view>
			<view v-else-if="templateDictKey == 16 && item.key == 'spJson'" class="pt-30 text-grey-74" style="line-height: 45rpx;">
				<view v-for="(value,key,index) in JSON.parse(obj[item.key])" :key="index" class="mb-10">
					<text v-show="key == 'recipeOne' && value">食谱一:{{value}}</text>
					<text v-show="key == 'recipeOneDesc' && value">食谱一说明:{{value}}</text>
					<text v-show="key == 'recipeTwo' && value">食谱二:{{value}}</text>
					<text v-show="key == 'recipeTwoDesc' && value">食谱二说明:{{value}}</text>
					<text v-show="key == 'recipeThree' && value">食谱三:{{value}}</text>
					<text v-show="key == 'recipeThreeDesc' && value">食谱三说明:{{value}}</text>
					<text v-show="key == 'recipeFour' && value">食谱四:{{value}}</text>
					<text v-show="key == 'recipeFourDesc' && value">食谱四说明:{{value}}</text>
					<text v-show="key == 'recipeFive' && value">食谱五:{{value}}</text>
					<text v-show="key == 'recipeFiveDesc' && value">食谱五说明:{{value}}</text>
					<text v-show="key == 'recipeSix' && value">食谱六:{{value}}</text>
					<text v-show="key == 'recipeSixDesc' && value">食谱六说明:{{value}}</text>
				</view>
			</view>
			<view v-else class="pt-30 text-grey-74">{{obj[item.key] || '无'}}</view>
		</view>
	</view>
</template>

<script>
	import {getInfoMedical} from '@/api/home.js'
	// 其他模板
	var arrList = require('./moban.js');
	export default {
	  name: 'others',
		props:['templateId','templateDictKey'],
		data(){
			return{
				obj:{},  //获取到的详情
				list:[], // 题目数组
        cardObj: uni.getStorageSync('cardObj')
			}
		},
		watch:{
			templateDictKey(news,olds){
				if(news != 1 && news !=2 && news !=0 && news !=100 && news !=26 && news !=29 && news !=30 && news!=31 && news!=35 && news!=36){
					this.getTzInfo();
				}
			}
		},
		methods:{
			// 获取病历详情
			getTzInfo(){
				// 获取对应的题目
				this.list = arrList.allList[this.templateDictKey];
				// 获取病历详情
				getInfoMedical({
					templateId:this.templateId,
					templateKey:Number(this.templateDictKey)
				}).then(res=>{
					for(var key in res.data){
						if(this.camelCase(key)){
							res.data[this.camelCase(key)] = res.data[key];
						}
					}
					this.obj = res.data;
				})
			},
			// 下划线转驼峰
			camelCase(str) {
			   return str.replace(/_([a-z])/g, function(all, letter) {
			    return letter.toUpperCase();
			  })
			}
		}
	}
</script>

<style>
	.tle {
		width: 10%;
		border: 1px solid #f2f3f4;
		text-align: center;
		writing-mode: vertical-lr;/* 从左向右 从右向左是 writing-mode: vertical-rl;*/
		writing-mode: tb-lr;/*IE浏览器的从左向右 从右向左是 writing-mode: tb-rl；*/
		letter-spacing: 10rpx;
		line-height: 70rpx;
		/* font-size: 28rpx; */
	}

	.gui-td {
		width: 100rpx;
		flex: 1;
		overflow: hidden;
		padding: 20rpx 10rpx;
		display: flexbox;
	}

	.gui-td-text {
		line-height: 40rpx !important;
		/* font-size: 24rpx; */
	}
</style>
