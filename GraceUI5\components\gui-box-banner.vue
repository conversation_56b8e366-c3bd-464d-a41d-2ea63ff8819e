<template>
	<view class="grace-box-banner gui-flex gui-rows gui-nowrap" 
	:style="{backgroundColor:background, 
		paddingTop:padding, 
		paddingBottom:padding, 
		borderRadius:borderRadius}">
	    <view class="grace-box-items gui-border-r" 
		v-for="(item, index) in items" 
		:key="index" @tap.stop="taped(index)" 
		:style="{
			borderRightWidth : index+1 >= items.length ? '0rpx':'1px',
			borderColor : borderColor
		}">
		<view class="gui-flex gui-rows gui-nowrap gui-justify-content-center gui-align-items-center">
			<text class="gui-block-text" 
			:style="{
				lineHeight:lineHeight, 
				color:color[0],
				fontSize:fontSize[0]
			}">{{item[0]}}</text>
			<text class="gui-block-text" 
			:style="{color:color[1], fontSize:fontSize[1], marginLeft:'5rpx'}">{{item[1]}}</text>
		</view>
		<text class="gui-block-text gui-text-center" 
		:style="{color:color[2],fontSize:fontSize[2]}">{{item[2]}}</text>
	    </view>
	</view>
</template>
<script>
export default {
	name  : "gui-box-banner",
	props : {
		items:{
			type : Array,
			default : function () {
				return []
			}
		},
		color:{
			type : Array,
			default : function () {
				return ['#333333', 'rgba(69, 90, 100, 0.5)', 'rgba(69, 90, 100, 0.5)']
			}
		},
		fontSize:{
			type : Array,
			default : function () {
				return ['36rpx', '24rpx', '24rpx']
			}
		},
		background : {
			type : String,
			default : ''
		},
		padding:{
			type : String,
			default : '20rpx'
		},
		borderRadius:{
			type : String,
			default : '10rpx'
		},
		lineHeight:{
			type : String,
			default : '60rpx'
		},
		borderColor : {type:String, default:'#F1F1F1'}
	},
	methods:{
		taped:function (index) {
			this.$emit('taped', index);
		}
	}
}
</script>
<style scoped>
.grace-box-banner{overflow:hidden;}
.grace-box-items{width:100rpx; flex:1;}
</style>