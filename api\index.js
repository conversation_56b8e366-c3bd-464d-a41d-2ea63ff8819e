import Http from "../net/Http.js"
import noHttp from "../net/noHttp.js"

// 经期设置
export function pregnancy(data){
	return Http.post('/wx/pregnancy',data)
}
// 预测经期时间
export function getPregnancyByPatientId(data){
	return Http.get('/wx/pregnancy/getPregnancyByPatientId',data)
}
// 预测经期时间
export function editPregnancy(data){
	return Http.get('/wx/pregnancy/editPregnancy',data)
}
// 控糖库类别列表
export function sugarControlTypeList(data){
	return Http.get('/wx/sugarControlDetail/typeList',data)
}
// 健康数据 包括[1.血糖 2.计步 3.睡眠 4.心率 5.血氧 6.血压] GET
export function getHealthData(data){
	return Http.get('/wx/zwb/iot/health/data',data)
}
//患者设备列表
export function getDeviceDetailList(data){
	return Http.get('/wx/zwb/iot/getDeviceDetailList/'+data)
}
//查询预警指标配置列表
export function indicatorList(data){
	return Http.get('/wx/zwb/iot/indicator/list',data)
}