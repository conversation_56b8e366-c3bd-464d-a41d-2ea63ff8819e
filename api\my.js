import Http from "../net/Http.js"
import noHttp from "../net/noHttp.js"


// 通过openid获取当前登录用户信息（不更新token）
export function getUserInfo(data){
	return Http.post('/wx/user/getUserInfo',data)
}
// 获取就诊卡数据 wx/card/selectByVisitCardNum
export function selectByVisitCardNum(data){
	return Http.get('/wx/card/selectByVisitCardNum',data)
}
// 选择就诊卡信息
export function selectVisitCardInfo(data){
	return Http.post('/wx/card/selectVisitCardInfo',data)
}
// 选择就诊卡信息
export function cardAdd(data){
	return Http.post('/wx/card/',data)
}
// 删除就诊卡
export function deleteCard(data){
	return Http.post('/wx/card/delete/'+data.id,data)
}
// 修改默认就诊卡
export function updatecard(data){
	return Http.post('/wx/card/update',data)
}
// 新增就诊卡
export function addcard(data){
	return Http.post('/wx/card/addcard',data)
}
// 隐私政策 显示开关配置
export function appSetOff(data){
	return noHttp.get('/wx/index/appSetOff',data)
}
// 更新微信用户信息
export function upMember(data){
	return Http.post('/wx/user/upMember',data)
}
// 获取患者的档案信息
export function getPatientInfo(data){
	return Http.get('/wx/patient/getPatientInfo',data)
}
// 修改患者的档案信息
export function updatePatientInfo(data){
	return Http.post('/wx/patient/updatePatientInfo',data)
}

// 获取应用消息设置列表
export function getDeptAppMsgSetList(data){
	return Http.get('/wx/app/getDeptAppMsgSetList',data)
}

// 开启或关闭接收应用消息
export function setDeptAppMsgStatus(data){
	return Http.get('/wx/app/setDeptAppMsgStatus',data)
}