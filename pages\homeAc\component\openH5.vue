<template>
	<view class="">
		<web-view :src="url"></web-view>
	</view>
</template>

<script>
    export default {
        data() {
            return {
				url:'',
            }
        },
	  onLoad(option){
	  	if (option.openUrl) {
			// 解码URL参数
			const decodedUrl = decodeURIComponent(option.openUrl);
			console.log('访问地址====', option.openUrl)
			console.log('解码后地址====', decodedUrl)
			if (option.openType == 2) {
				let reporUrl = decodedUrl+'?id='+option.reportId+'&token='+uni.getStorageSync("token")
				console.log('检查报告地址==',reporUrl)
				this.$set(this,'url',reporUrl)
				uni.setNavigationBarTitle({
					title:'营养干预方案'
				})
				return
			}
	  		this.$set(this,'url',decodedUrl)
	  	}
	  },
      methods:{
        }
    }
</script>

<style>
</style>