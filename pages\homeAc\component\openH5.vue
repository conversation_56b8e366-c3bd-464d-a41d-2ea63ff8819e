<template>
	<view class="">
		<web-view :src="url"></web-view>
	</view>
</template>

<script>
    export default {
        data() {
            return {
				url:'',
            }
        },
	  onLoad(option){
	  	if (option.openUrl) {
			// console.log('访问地址====',option.openUrl)
			if (option.openType == 2) {
				let reporUrl = option.openUrl+'?id='+option.reportId+'&token='+uni.getStorageSync("token")
				// console.log('检查报告地址==',reporUrl)
				this.$set(this,'url',reporUrl)
				uni.setNavigationBarTitle({
					title:'报告详情'
				})
				return
			}
	  		this.$set(this,'url',option.openUrl)
	  	}
	  },
      methods:{
        }
    }
</script>

<style>
</style>