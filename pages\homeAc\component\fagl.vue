<template>
  <!-- 调理方案 -->
  <view class="fs-32">
    <view class="my-20">
	  <!-- <view class="mb-20 text-center">平和质调理建议</view> -->
      <view class="d-flex mb-20">
        <view>姓名：{{userName || cardObj.userName || '-'}}</view>
        <view class="ml-120">性别：{{sexClick()}}</view>
      </view>
	  <view class="mb-20">体质测评得分： {{ systemEvaluationScore || '' }}</view>
	  <view class="mb-20">体质综合结果： {{ systemEvaluationResult || '' }}</view>
      <view>就诊时间：{{createTime ? $common.parseTime(createTime) : '-'}}</view>
    </view>
    <view v-for="(item,index) in programmeList" :key="index" class="d-flex fs-32">
      <view class="tle gui-flex gui-justify-content-center gui-align-items-center">
        {{index}}
      </view>
      <view class="gui-table gui-border-l gui-border-t" style="width: 67%;">
        <view class="gui-tbody gui-flex gui-rows gui-nowrap">
          <view class="gui-td gui-td-text gui-text-left gui-border-r gui-border-b ">
            <view v-for="(items,a) in item" :key="a">
              <view v-if="String(items.taskType) === '11' && items.picturePath" class="gui-flex gui-row gui-wrap">
                <view class="mr-20" v-for="(img,b) in items.picturePath.split(',')">
                  <view class="image-content">
                    <image style="width: 80px;height: 80px;" :src="img" @click="()=>clickImg(img)"/>
                  </view>
                  <view class="image-title">{{ items.questionnaireName.split(',')[b] }}</view>
                </view>
              </view>
              <view v-else>
                <view v-if="item.length === 1" class="gui-fle gui-columns">
					<view class="gui-flex">
						<text>{{ items.questionnaireName }}</text>
						<!-- <text style="flex: none;">{{ items.questionnaireName }}</text> -->
						<text class="text-zhuti ml-20" v-if="items.taskType === 3">
						  每{{(items.frequencyUnit === 1 && '天') || (items.frequencyUnit === 2 && '周') || (items.frequencyUnit === 3 && '月')
						  }}<text >{{
						    (items.frequencyDays && `${items.frequencyDays}次`) || (items.frequencyUnit === 1 && '1次' || '')
						  }}</text>{{
						    (items.frequencyDates && `(第${items.frequencyDates.replace(/,/g, '/')}天执行)`) || ''
						  }}
						</text>
						<text class="text-zhuti ml-20" v-if="items.taskType === 4">
						  每{{(items.frequencyUnit === 1 && '天') || (items.frequencyUnit === 2 && '周') || (items.frequencyUnit === 3 && '月')
						  }}{{(items.frequencyDays && `${items.frequencyDays}次`) || (items.frequencyUnit === 1 && '1次' || '')
						  }}{{(items.frequencyDates && `(第${items.frequencyDates.replace(/,/g, '/')}天执行)`) || ''}}
						</text>
						<!-- <text style="padding-left: 10px;" v-if="items.taskType === 5">
						  {{ setGoaFangUse(items.frequencyUse) }}
						  每次{{items.singleQuantity && items.singleQuantity || 0}} {{setGoaFangSingleUnit(items.singleUnit)}}
						  每{{(items.frequencyUnit === 1 && '天') || (items.frequencyUnit === 2 && '周') || (items.frequencyUnit === 3 && '月')}}
						  {{(items.frequencyDays && `${items.frequencyDays}次`) || (items.frequencyUnit === 1 && '1次' || '') }}
						  {{ (items.frequencyDates && `(第${items.frequencyDates.replace(/,/g, '/')}天执行)`) || ''}}
						</text> -->
					</view>
					<view class="mt-10" v-if="items.taskType === 3||items.taskType === 4">执行时间： <text class="text-zhuti">{{items.times}}</text></view>
                </view>
                <view v-else class="gui-fle gui-columns mb-20">
				  <view class="gui-flex">
				  	<text >{{ a + 1 }}.{{ items.questionnaireName }}</text>
					<!-- <text style="flex: none;">{{ a + 1 }}.{{ items.questionnaireName }}</text> -->
				  	<text class="text-zhuti ml-20" v-if="items.taskType === 3">
				  	  每{{(items.frequencyUnit === 1 && '天') || (items.frequencyUnit === 2 && '周') || (items.frequencyUnit === 3 && '月')
				  	  }}<text >{{
				  	    (items.frequencyDays && `${items.frequencyDays}次`) || (items.frequencyUnit === 1 && '1次' || '')
				  	  }}</text>{{
				  	    (items.frequencyDates && `(第${items.frequencyDates.replace(/,/g, '/')}天执行)`) || ''
				  	  }}
				  	</text>
				  	<text class="text-zhuti ml-20" v-if="items.taskType === 4">
				  	  每{{(items.frequencyUnit === 1 && '天') || (items.frequencyUnit === 2 && '周') || (items.frequencyUnit === 3 && '月')
				  	  }}{{(items.frequencyDays && `${items.frequencyDays}次`) || (items.frequencyUnit === 1 && '1次' || '')
				  	  }}{{(items.frequencyDates && `(第${items.frequencyDates.replace(/,/g, '/')}天执行)`) || ''}}
				  	</text>
				  </view>
				  <view class="mt-10" v-if="items.taskType === 3||items.taskType === 4">执行时间： <text class="text-zhuti">{{items.times}}</text></view>
				</view>
              </view>
            </view>
			<view class=" gui-flex" v-if="index == '监测干预'||index == '治疗调理'">
				<text style="flex: none;">执行周期：</text>
				<text class="text-zhuti" v-if="item[0]">{{$common.parseTime(item[0].frequencyStartDate,'{y}-{m}-{d}')}} 至 {{$common.parseTime(item[0].frequencyEndDate,'{y}-{m}-{d}')}}</text>
			</view>
			<view class=" gui-flex" v-if="index == '监测干预' && item[0].remark">
				<text style="flex: none;">医生嘱托：</text>
				<text class="text-zhuti">{{item[0].remark || '无'}}</text>
			</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
	import {programmeList} from "@/api/home.js"
export default {
  name: 'ysjy',
  props: ['visitRecordId', 'templateDictKey', 'programmeTypeId', 'programmeType', 'schemeTime','patientId','sex','userName','createTimeFilter'],
  data() {
    return {
      createTime: '',
      programmeList: [],
      systemEvaluationScore: '',
      systemEvaluationResult: '',
      cardObj: uni.getStorageSync('cardObj')
    }
  },
  watch: {
	  templateDictKey(news,olds){
	  	if( news == 0 ){
	  		this.loadProgrammeList();
	  	}
	  }
  },
  methods: {
	  sexClick(){
		  var sex = ''
		  if(this.sex){
			sex = this.sex == 1?'男':'女'
		  }else{
			sex = this.cardObj.sex == 1?'男':'女'
		  }
		  return sex
	  },
    loadProgrammeList() {
		if (this.templateDictKey == 0) {
			let params = {
				programmeType: this.programmeType,
				programmeTypeId: this.programmeTypeId,
				patientId: this.patientId,
				// schemeTime: this.$common.parseTime(this.schemeTime, '{y}-{m}-{d}')
			};

			// 如果传递了 createTimeFilter 参数，则添加到请求参数中
			if (this.createTimeFilter) {
				params.createTimeFilter = this.createTimeFilter;
			}

			programmeList(params).then(res=>{
				this.programmeList = res.data || [];
				this.createTime = this.programmeList.createTime || '';
				this.systemEvaluationScore = this.programmeList.systemEvaluationScore || '';
				this.systemEvaluationResult = this.programmeList.systemEvaluationResult?.replace(/评估问卷/g,"");
				delete this.programmeList.programmeTypeName
				delete this.programmeList.systemEvaluationResult
				delete this.programmeList.systemEvaluationScore
				delete this.programmeList.createTime
				delete this.programmeList.问卷
			})
		}

    },
    clickImg(url) {
      uni.previewImage({
        urls: [url],
        indicator: 'none'
      })
    }
  }
}
</script>

<style scoped>
.tle {
  width: 32%;
  border: 1px solid #f2f3f4;
  /* padding: 20rpx 10rpx; */
}

.gui-td {
  width: 100rpx;
  flex: 1;
  overflow: hidden;
  padding: 20rpx 10rpx;
  display: flexbox;
}

.gui-td-text {
  line-height: 40rpx !important;
}
</style>
