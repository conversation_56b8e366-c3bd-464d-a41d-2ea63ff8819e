<template>
	<gui-page :loadmore="true" @loadmorefun="getarticleList" :isLoading="pageLoading"
		:apiLoadingStatus="apiLoadingStatus" ref="guipage" :customHeader="true" :refresh="true" @reload="reload"
		:refreshBgColor="['#c3ad96','#c3af9c','#c3af9c','#c3af9c']"
		statusBarStyle="background: linear-gradient(90deg,#c59f79,#c1c1c1);"
		headerStyle="background: linear-gradient(90deg,#c59f79,#c1c1c1);">
		<view slot="gHeader">
			<view class="w-100 bgs" style="color: #000000; display: flex; align-items: center;align-content: center;"
				@tap.stop="inDetail">
				<view class="logosize">
					<image :src="logo||'https://img.starup.net.cn/xmkj/zwb/img/zyLogo.jpg'"
						style="width: 100%;height: 100%; border-radius: 30rpx; display: inline-block"></image>
				</view>
				<view
					style="margin-left: 20rpx; font-size: 20rpx;display: flex;align-content: center;align-items: center;">
					<view>
						<view>积</view>
						<view>分</view>
					</view>
				</view>
				<view style="font-size: 50rpx;margin-left: 15rpx ;width: 320rpx;height: 48px;line-height: 48px;">
					{{integral || '0'}}
				</view>

			</view>
		</view>

		<!-- 页面主体 -->
		<view slot="gBody" class="gui-bg-gray">

			<view class="pt-20" style="background: linear-gradient(90deg,#c59f79,#c1c1c1);">
				<view class="gui-bg-white top-radius">
					<view class="pb-10 pt-20 gui-bg-white top-radius " >
						<!-- 首次未绑卡 -->
						<view class="chen mx-20 py-20 box-size-border wbk mt-20 text-center" @tap="addCard"
							v-if="!cardObj.id && loginFag">
							<text class="gui-icons fs-28 text-grey-74">您还未绑定就诊卡，扫一扫绑定</text>
						</view>
						<view class="px-40" >
							<view style="border-bottom: 1rpx solid #eeeeee;" class="d-flex ai-center jc-between pb-15" v-if="cardObj.id && loginFag">
								<view class="gui-list-image ucenter-face gui-relative mr-20">
									<image class="gui-list-image ucenter-face-image"
										:src="user.avatar ? user.avatar : '/static/empty_avatar.png'" mode="widthFix"></image>
								</view>
								<view class="flex-1">
									<view class="font-bold">
										{{cardObj.userName || '请先登录'}}
										<text v-if="cardObj.relationship"
											style="margin-left: 10rpx;font-size: 28rpx;">【{{cardObj.relationship}}】</text>
									</view>
									<view class="fs-28 mt-10" style="color: #A5A5A5;"><text
											class="gui-icons mr-10">&#xe62f;</text>{{cardObj.visitCardNum || '-'}}</view>
								</view>
								<view @tap="patient" class="d-flex ai-center">
									<text class="gui-grids-icon gui-icons fs-28 text-zhuti">切换就诊卡</text>
									<image src="https://img.starup.net.cn/xmkj/zwb/img/zy_ent_brow.png"
										class="w30 h30 ml-15 mb-5">
									</image>
								</view>
							</view>
						</view>
					</view>
					<!-- 健康记录、科室 体质、在线-->
					<view class="gui-border-b gui-bg-white px-20 pt-15" >
						<view class="gui-flex gui-rows tab-list gui-wrap">
							<view
								v-for="item in menuArr"
								@tap="tabClick(item.menuIdent)"
								class="tab-list-item gui-flex gui-columns gui-justify-content-center gui-align-items-center" style="position:relative;">
								<!-- 在线咨询 -->
								<text style="z-index: 0;" v-if="item.menuIdent == 'onlineConsultation' && getMsgReadedNum && getMsgReadedNum>0" class="gui-badge gui-bg-red gui-badge-absolute gui-color-white">{{getMsgReadedNum}}</text>
								<!-- 健康建议 -->
								<text style="z-index: 0;" v-if="item.menuIdent == 'conditioningSuggestion' && list && list.length>0" class="gui-badge gui-bg-red gui-badge-absolute gui-color-white">{{list.length}}</text>
								<!-- 健康日记 -->
								<text style="z-index: 0;" v-if="item.menuIdent == 'healthDiary' && healthyNum>0" class="gui-badge gui-bg-red gui-badge-absolute gui-color-white">{{healthyNum}}</text>
								<image :src="item.icon" style="max-height: 55rpx;max-width: 55rpx;"></image>
								<view class="mt-10">{{item.menuName}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="gui-flex w-100 mt-20 h-100" v-if="showcontract">
				<view class="w-50 gui-bg-white mr-10 p-20" style="height: 400rpx;">
					<view class="gui-bold">我的科室与医生</view>
					<view class="mt-10" v-for="(item,index) in contractList " :key="index">
						<view class="gui-flex gui-space-around " :class="index==(contractList.length-1)?'':'gui-border-b pb-20'">
							<view @click="department(item)" class="gui-flex gui-columns gui-align-items-center gui-justify-content-center">
								<image :src="item.deptIcon" style="max-height: 65rpx;max-width: 65rpx;"></image>
								<view class="mt-5">{{item.deptName}}</view>
							</view>
							<view @click="tabSelect(item)" class="gui-flex gui-columns gui-align-items-center gui-justify-content-center">
								<image :src="item.showAvatar || 'https://img.starup.net.cn/xmkj/zwb/img/yisheng.png'" style="max-height: 65rpx;max-width: 65rpx;"></image>
								<view class="mt-5 gui-flex gui-columns gui-align-items-center">
									<view class="">{{item.doctorName}}</view>
									<view v-if="item.docTitle" class="ellipsis-1">{{item.docTitle || '医生'}}</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="w-50 gui-flex gui-columns h-100">
					<view @click="toFuzhen(fuzData)" class="gui-bg-white mb-5 p-20" style="height: 195rpx;">
						<view class="gui-flex gui-space-between">
							<view class="gui-bold">复诊提醒</view>
							<view v-if="fuzData.createTime" class="fs-28 gui-color-gray">{{ $common.parseTime(fuzData.createTime,'{m}-{d} {h}:{i}')}}</view>
						</view>
						<view v-if="fuzData.remark" class="ellipsis-3" style="text-indent: 2em;">{{fuzData.remark}}</view>
						<gui-empty v-else>
							<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
								<image style="height: 90rpx;width: 90rpx;" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
							</view>
							<text slot="text"
							class="gui-text-small gui-block-text gui-text-center" style="color:#B2B2B2;">暂无数据</text>
						</gui-empty>
					</view>
					<view class="gui-bg-white mt-5 p-20" style="height: 195rpx;">
						<view class="gui-bold">用药提醒</view>
						<gui-empty>
							<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
								<image style="height: 90rpx;width: 90rpx;" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
							</view>
							<text slot="text"
							class="gui-text-small gui-block-text gui-text-center" style="color:#B2B2B2;">暂无数据</text>
						</gui-empty>
					</view>
				</view>
			</view>
			<!-- 科室入口 -->
			<view class="fs-28 mt-20 gui-bg-white pt-20" v-if="getListDeptlist.length > 0 && !showcontract">
				<view class="gui-flex gui-space-between mx-40">
					<view class="gui-bold">按科室</view>
					<view class="text-zhuti" @click="$common.msg('暂未开放敬请期待~')">更多 <text class="gui-icons">&#xe601;</text> </view>
				</view>
				<view class="gui-flex gui-rows tab-list gui-wrap px-20 mt-20" >
					<view v-for="i in getListDeptlist" @click="department(i)" class="tab-list-item gui-flex gui-columns gui-align-items-center">
						<image :src="i.logUrl"
							style="max-height: 80rpx;max-width: 80rpx;"></image>
						<view class="mt-10 px-20" style="text-align: center;">{{i.appName}}</view>
					</view>
				</view>
			</view>
			<!-- 弹窗 -->
			<view class="Ashowfu" v-if="isshow">
				<view class="showfu">
					<view class="showtitl">
						温馨提示
					</view>
					<view class="showcon">
						如您已在我院办有就诊卡，请点击添加就诊人。如未办理过就诊卡请点击完善个人信息，完善后可用于在线咨询
					</view>
					<view class="showbtn mt-50">
						<view class="btn01" @click="addPatient">
							添加就诊人
						</view>
						<view class="btn02 " @click="evpi">
							完善信息
						</view>
					</view>
				</view>

			</view>

			<!-- 医案分享 -->
			<view class="mx-20 fs-28 mt-20 gui-bg-white">
				<view class="gui-flex gui-space-between m-20 pt-20">
					<view class="gui-bold">医案分享</view>
					<view class="text-zhuti" @click="toarticle">更多 <text class="gui-icons">&#xe601;</text> </view>
				</view>

				<swiper v-if="articleList.length > 0" circular :indicator-dots="true" :autoplay="true" :interval="5000"
					indicator-active-color="#ad8364" :duration="1000" style="border-radius: 10rpx; height: 300rpx;">
					<swiper-item  v-for="(item,index) in articleList" :key="index" @click="toDetail(item)">
						<view class="swiper-item gui-flex" style="height: 100%;margin: 0rpx 20rpx;">
							<view class="">
								<image :src="item.filePath" mode="aspectFill"
									style="border-radius: 5rpx;height: 260rpx;width: 200rpx;"></image>
							</view>
							<view class="ml-20">
								<view class="gui-bold ellipsis-2">
									{{item.articleName || ''}}
								</view>
								<view class="ellipsis-4 mt-10">
									<text class="gui-color-gray">摘要：</text> {{item.articleAbstract || ''}}
								</view>
							</view>
						</view>
					</swiper-item>
				</swiper>
				<view v-if="articleList.length <= 0">
					<gui-empty>
						<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
							<image class="gui-empty-img" style="margin-top: 0rpx;" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
						</view>
					</gui-empty>
				</view>
			</view>
			<!-- 活动专区  -->
			<view class="mx-20 fs-28 mt-20 gui-bg-white pb-20" v-if="activityShow && systemeven('activityManagement') ">
				<view class="gui-flex gui-space-between m-20 pt-20">
					<view class="gui-bold">活动专区</view>
					<view class="text-zhuti" @click="toActivity">更多 <text class="gui-icons">&#xe601;</text> </view>
				</view>
				<scroll-view class="gui-scroll-x gui-margin-top gui-flex gui-rows" :show-scrollbar="false" :scroll-x="true"
				style="width:690rpx;" :scroll-left="40" >
					<view class="gui-scroll-x-items gui-flex gui-columns mx-10 gui-border" v-for=" (item,index) in activityData" :key="index">
						<image class="gui-scroll-image gui-img-in" :src="item.activityImgUrl" mode="aspectFill" @click="getActivityDetails(item)"></image>
						<view class="mt-20 " >
							<view class="gui-flex gui-rows gui-space-between px-20 mb-10" @click="getActivityDetails(item)">
								<view class="mb-20" style="width: 450rpx;">
									<view class="fs-30 gui-bold" style="width: 450rpx; overflow: hidden;white-space: nowrap; text-overflow: ellipsis;">
										{{item.activityName}}
									</view>
									<view class="fs-28 mt-20">
										活动截止时间：{{item.activityDateStatus ==1?item.activityDate : '不限'}}
									</view>
								</view>
								<view class="">
									<image v-if="item.issueStatus == 1" class="gui-img-in" style="width: 120rpx;height: 120rpx;" src="/static/img/hz_ing.png" mode="aspectFill"></image>
									<image v-else class="gui-img-in" style="width: 120rpx;height: 120rpx;" src="/static/img/hz_end.png" mode="aspectFill"></image>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>

			<!-- 今日资讯 -->
			<view class="mx-20 mt-20 gui-bg-white" style="position: sticky; position: -webkit-sticky; top: 170rpx; ">
				<view class="m-20 pt-40" >
					<gui-switch-navigation-lyb activeLineWidth="50%" activeLineBg="linear-gradient(to right, #C59F79, #C59F79)"
						activeColor="#C59F79" lineHeight="40rpx" activeDirection="center" :fontSize="'32rpx'" :activeFontSize="'34rpx'" :items="specialListByType" :size="170"
						:currentIndex="currentIndex" @change="navchange">
					</gui-switch-navigation-lyb>
				</view>
				<view v-if="homezongSize > 0" class="gui-flex gui-rows gui-space-between px-20" style="width: 100%;">
					<view style="width: 50%;">
						<view @click="toDetail(item)" class=" flex-1 mr-5 gui-border p-10 mb-10" v-for="(item,index) in articleLists" :key="index" v-if="index%2 == 0">
							<view class=" ">
								<gui-image :src="item.filePath" :width="310"></gui-image>
								<text class="gui-text gui-primary-color gui-block-text mt-10 ellipsis-4">{{item.articleName || ''}}</text>
							</view>

						</view>
					</view>
					<view style="width: 50%;">
						<view @click="toDetail(item)"  class=" flex-1 ml-5 gui-border p-10 mb-10" v-for="(item,index) in articleLists" :key="index" v-if="index%2 != 0">
							<view class=" ">
								<gui-image :src="item.filePath" :width="310"></gui-image>
								<text class="gui-text gui-primary-color gui-block-text mt-10 ellipsis-4">{{item.articleName || ''}}</text>
							</view>
						</view>
					</view>
				</view>
				<view v-if="homezongSize <= 0">
					<gui-empty>
						<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
							<image class="gui-empty-img" style="margin-top: 0rpx;" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
						</view>
					</gui-empty>
				</view>
			</view>
			<!-- 引导测评（授权订阅消息） -->
			<gui-popup ref="guipopup1">
				<view class="gui-relative gui-box-shadow gui-bg-white demo-lr">
					<view class="demo-lr-title">登录成功</view>
					<view class="demo-lr-content">
						<span class="demo-lr-content-span">你了解自己的体质状态吗？</span>
						<span class="demo-lr-content-span">快来测评吧～</span>
					</view>
					<view class="demo-lr-btn">
						<view class="cancelText" @click="confirmText(0)">稍后再说</view>
						<view class="confirmText" @click="confirmText(1)">立即测评</view>
					</view>
				</view>
			</gui-popup>
			<!-- 引导授权手机号 -->
			<gui-popup ref="guipopup2">
				<view class="gui-relative gui-box-shadow gui-bg-white demo-lr">
					<view class="demo-lr-title" style="padding-bottom: 40rpx;">请完善手机号</view>
					<view class="demo-lr-btn">
						<view class="cancelText" @click="$refs.guipopup2.close()">稍后再说</view>
						<button class="confirmText btn-phone" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">立即完善</button>
					</view>
				</view>
			</gui-popup>
			<guideStep @addCard="addCard" :step="step" v-if="!cardObj.id && loginFag && completion"></guideStep>
			<view style="position: fixed;bottom: 0;width: 100%;" v-show="isSubscribe == '2'">
				<!-- <official-account @load="bindload"></official-account> -->
				<custom-official-accounts ref="pop" :logo="logo" :gzhName="gzhName" :gzhImg="gzhImg"></custom-official-accounts>
			</view>
		</view>
	</gui-page>
</template>

<script>
	import util from '@/tools/utils/utils.js'
	import customOfficialAccounts from "@/components/custom-official-accounts.vue"
	import guideStep from "@/components/xky-guideStep/xky-guideStep.vue";
	import {postAuth} from '@/api/login.js'
	import {getUserInfo} from '@/api/my.js'
	import {
		getList
	} from '@/api/system.js'
	import {
		mapState
	} from "vuex"
	import {
		constitutionFillContent,getListByDeptId,contractDoctor,
		carouseList,
		getCard,
		heathList,
		register,
		noticeList,
		getPatientInfo,
		bodyhealth,
		countFeedback
	} from '@/api/home.js'
	import {
		mySessionList,
		getImUser
	} from '@/api/im.js'
	import {
		articleSpecialList,
		specialType,
		channelType,
		articleSpecialListByType
	} from '@/api/science.js'
	import { activityList,addActivityByPatient } from '@/api/activity.js'
	export default {
		components:{
		    guideStep,customOfficialAccounts
		},
		data() {
			return {
				healthyNum:0,
				menuArr:[],
				getListDeptlist:[],//科室信息
				step:{
				    name:'workbenchKey1',
				    repeat:false,
				    guideList: [
				        {
				            el: '.chen',
				            tips: '点击一键扫码绑定就诊卡',
				            style: "border-radius: 8rpx;margin: 0",
				            next:"去扫码"
				        },
					]
				},
				imUser: {
					userId: uni.getStorageSync('user').id, // 系统用户id
					userUid: '', //链接im返回的uuid
					userType: '2', // 用户类型  1：医生  2：患者
					uname: uni.getStorageSync('user').nickName, // 用户名
					nickName: uni.getStorageSync('user').nickName, // 昵称
					uface: uni.getStorageSync('user').avatar // 头像
				},
				imNum:this.$store.state.getMsgReadedNum,
				logo:'',
				domainType:this.$common.domainType,
				isSubscribe:'',//用户是否订阅公众号 0未授权，1已订阅，2未订阅
				gzhName:this.$common.gzhName,
				gzhImg:this.$common.gzhImg,
				list: [], //健康建议数据
				activityShow:false,//活动专区展示
				activityData:[],//活动专区数据
				loginFag: false,
				integral: '', //积分
				count: 0,
				index: 1,
				cardList: [],
				// imgList: [],//轮播图
				cardObj: {},
				// msgList: [],//消息列表
				// tzInfo: {},//体质数据
				user: uni.getStorageSync("user"),
				type: 1,
				isshow: false,
				doctorId: '',
				code: false, //false不是扫码进来的  true扫码进来的
				homepage: 1,
				homesize: 10,
				zongPage: 0,
				homezongSize: 0,
				articleList: [], //文章集合
				// loadingStatus:false, //  ， true： 请求中   false :请求结束
				// loadState: 0 ,  // 0 : 默认0，有下一页   1 ：请求中  2： 加载完毕
				productList: [[],[]],
				articleLists: [], //资讯集合
				pageLoading: true,
				// 用于记录是否有 api 请求正在执行
				apiLoadingStatus: false,
				visitCardNum:'',//患者id
				visitShow:false,
				cardList:[],
				completion:false,
				specialListByType:[],
				// 选中选项的 索引
				currentIndex: 0,
				phoneNumber:'',//短信链接场景进入
				contractList:[],//签约医生
				showcontract:false,//显示签约医生
				fuzData:{},//复诊提醒

			}
		},
		computed:{
			...mapState(['getMsgReadedNum','graceIMStatus'])
		},
		onShareAppMessage(res) {// 分享按钮
		},
		onShareTimeline(res) {//分享到朋友圈
		},
		onLoad(query) {
			this.logo = __wxConfig.accountInfo.icon
			if (query.scene) {//扫码场景进入小程序
				this.code = true
				// 患者
				if(query.scene[0] == 'p'){
					var visitCardNum = query.scene.slice(2)
					this.visitCardNum = visitCardNum
				}else{
					// 医生
					this.doctorId = query.scene
				}
			}
			if (query.phone) {//短信链接场景进入
				this.phoneNumber = query.phone;
			}
			this.homepage = 1;
			this.getArticleType();//文章分类
			this.getWxMenu()//获取菜单权限
		},
		onShow() {
			if(uni.getStorageSync('success')){
				// this.$refs.guipopup1.open()
				uni.removeStorageSync('success');
			}
			this.checkRegAndLogin();
			this.getListDept()
		},
		methods: {
			// 菜单权限
			getWxMenu(){
				getList().then(res=>{
					var arr = res.data.sort(function(a,b){
						return a.sort - b.sort //升序 降序：b.sort - a.sort
					})
					this.$common.systemlist = res.data.map(function(item,index){
						return item.menuIdent
					})
					var list = util.menu().list
					this.menuArr = []
					arr.map(item=>{
						for(let i = 0;i<list.length;i++){
							if(list[i].key == item.menuIdent){
								item.icon = list[i].icon
								this.menuArr.push(item)
								break
							}
						}
					})
				})
			},
			// 顶部tab点击事件
			tabClick(e){
				switch(e) {
				    case 'departmentIntroduction':
				        //科室介绍
						this.$common.navTo('/pages/homeAc/other/department')
						break;
					case 'healthRecord':
						// 健康记录
						this.record()
					   break;
					case 'constitutionEvaluation':
					    //体质测评
						this.evaluation()
						break;
					case 'onlineConsultation':
					    //在线咨询
						this.imChat()
						break;
					case 'conditioningSuggestion':
					    //健康建议
						this.$common.openDyMsg(0,2);
						this.$common.navTo('/pages/homeAc/other/proposal')
						break;
					case 'healthDiary':
					    //健康日记
						this.healthy()
						break;
					case 'goodSelection':
					    //好物甄选
						this.$common.navTo('/pages/integral/index')
						break;
					case 'treatmentAppointment':
					    //治疗预约
						this.$common.navTo('/pages/homeAc/treatment/index')
						break;
				}
			},
			//科室名称
			getListDept(){
				getListByDeptId({parentDeptId:100}).then(res =>{
					this.getListDeptlist = res.data;
				})
			},
			// 完善手机号
			getPhoneNumber(e){
				if(e.detail.errMsg ===  "getPhoneNumber:ok" ){
					postAuth({
						openId:uni.getStorageSync("openId"),
						code:e.detail.code,
						encryptedData:e.detail.encryptedData,
						iv:e.detail.iv
					}).then(res=>{
						this.$refs.guipopup2.close();
						uni.setStorageSync("token", res.data.token)
						uni.setStorageSync("user", res.data.user);
					})
				}else{
					// 拒绝
					this.$refs.guipopup2.close();
				}
			},
			// 健康日记
			healthy(){
				this.$common.openDyMsg(0,3);
				if (!this.$common.isLogin()) {
					this.$common.noLoginBox();
					return
				}
				if (uni.getStorageSync('cardObj') && uni.getStorageSync('cardObj').patientId) {
					this.$common.navTo('/pages/homeAc/healthy/healthy')
				} else {
					this.$common.msg("请先绑定就诊卡")
				}
			},
			confirmText(e){
				this.$common.openDyMsg(0,2);
				// this.$common.openDyMsgs();
				if(e){
					// this.$refs.guipopup1.close()
					this.$common.navTo('/pages/homeAc/evaluation/list')
				}else{
					// this.$refs.guipopup1.close()
				}
			},
			reload() {
				this.homepage = 1;
				this.productList = [[],[]];
				this.getFl();
				// this.getHeathList();
				this.getarticleList(true);
				this.getcountFeedback()
			},
			// 完善信息
			evpi() {
				this.isshow = false
				this.$common.navTo('/pages/homeAc/evpi')
			},
			getActivity(){//活动专区
				activityList({
					patientId:uni.getStorageSync('cardObj').patientId,
					userId:uni.getStorageSync('user').id,
					issueStatus:1,
					pageSize:5,
					pageNum:1
				}).then(res =>{
					this.activityData = res.data.records;
					if (res.data.records.length >0) {
					  this.activityShow = true
					 }
				})
			},
			getActivityDetails(item,type){//活动详情
				if (item) {
					if (type == 0) {//报名
						addActivityByPatient({
							activityId:item.id,//活动id
							patientId:uni.getStorageSync('user').id,//用户id
						}).then(res =>{
							if (res.code == 200) {
								this.$common.msg("活动报名成功")
								// this.$common.navTo('/pages/homeAc/activity/index?type=1')
							}
						})
					} else{
						this.$common.navTo("/pages/homeAc/activity/detail?id="+item.id)
					}
				}
			},
			getFl() {
				channelType({appId:''}).then(res => { //医案分享
					res.data.find(item => {
						if (item.dictLabel === '医案分享') {
							articleSpecialList({
								pageNum: 1,
								pageSize: 4,
								specialId: item.dictCode
							}).then(res => {
								this.articleList = res.rows;
							})
						}
					})
				})
			},
			getArticleType(){//最新资讯分类
				articleSpecialListByType({
					pageNum: this.homepage,
					pageSize: this.homesize,
					isInfo: 1, //是否设为资讯，传1则获得设为资讯的文章
				}).then(res =>{
					this.specialListByType = [{dictLabel:"最新资讯",dictCode:""}].concat(res.data);
					this.init()//文章列表
				})
			},
			navchange(index) {
				this.currentIndex = index;
				this.init();
			},
			// 初始化
			init(){
				this.homepage = 1;
				this.apiLoadingStatus = false;
				this.loadState = 0;
				this.$refs.guipage.stoploadmore();
				this.getarticleList();
			},
			// isReload 函数用于识别是不是下拉刷新执行的
			getarticleList(isReload) { //文章信息
				this.$common.openDyMsg(0,3);
				this.apiLoadingStatus = true;
				articleSpecialList({
					pageNum: this.homepage,
					pageSize: this.homesize,
					isInfo: 1, //是否设为资讯，传1则获得设为资讯的文章
					specialId:this.specialListByType[this.currentIndex].dictCode?this.specialListByType[this.currentIndex].dictCode:''
				}).then(res => {
					this.articleLists = this.homepage == 1 ? res.rows : this.articleLists.concat(res.rows);
					this.homezongSize = res.total;
					this.zongPage = Math.ceil(Number(res.total / this.homesize))
					console.log('文章详情8888888888',this.pageLoading)
					this.pageLoading = false;
					console.log('文章详情======',this.pageLoading)
					if (isReload) {
						this.$refs.guipage.endReload();
					}
					if (this.homepage >= this.zongPage) {
						this.$refs.guipage.nomore();
						this.loadState = 2;
						this.apiLoadingStatus  = false;
						return;
					}else{
						this.$refs.guipage.stoploadmore();
					}
					this.homepage++;
					this.loadState = 0;
					this.apiLoadingStatus = false;
				})
			},
			toarticle() { //医案分享列表
				this.$common.navTo('/pages/homeAc/other/articleDetail')
			},
			toActivity(){//活动专区
				this.$common.navTo('/pages/homeAc/activity/index')
			},
			toscience() { //科普列表
				this.$common.navTab('/pages/science/index')
			},
			toDetail(item) { //查看文章详情
				this.$common.openDyMsg(0,2);
				// this.$common.openDyMsgs();
				if (item.articleType == 3) {
					setTimeout(() => {
						this.$common.navTo('/pages/task/other/aboutus?articleId='+item.articleId)
					}, 300)
				} else {
					this.$common.navTo("/pages/science/detail?articleId="+item.articleId)
				}
			},
			getHeathList() {
				heathList({
					patientId: uni.getStorageSync('cardObj').patientId
				}).then(res => {
					this.list = res.data || []
				}, fail => {
					this.list = []
				})
			},
			register() {
				var that = this
				if (uni.getStorageSync('cardObj') && uni.getStorageSync('cardObj').patientId) {
					register({
						doctorId: that.doctorId,
						visitCardNum: that.cardObj.visitCardNum,
						idCard: that.cardObj.idCard,
					}).then(resi => {
						setTimeout(() => {
							that.$common.msg("已成功签到" + resi.data.doctorName +
								"医生进行健康管理！", "none", 5000)
						}, 500);
						that.code = false
					}, fali => {
						that.code = false
					})
				} else {
					that.code = false
					that.$common.navTo('/pages/myAc/other/addPatient?code=true&result=' + that.doctorId)
				}
			},
			// 患者二维码
			visitCard(){
				let item = this.cardList.findIndex(item => {
				    return item.visitCardNum == this.visitCardNum || item.idCard == this.visitCardNum;
				});
				if(item<0){
					this.$common.navTo('/pages/myAc/other/addPatient?visitCardNum=' + this.visitCardNum)
				}
			},
			// 扫码签到
			sign() {
				var that = this
				console.log(that.code,'codeshujuhhaha')
				if (!that.code) {
					return;
				}
				that.code = false
				if(this.visitCardNum){
					this.visitCard()
					return
				}
				this.register()
			},
			//检查登录情况
			checkRegAndLogin() {
				let self = this;
				this.$common.isRegAndLogin(res => {
					if (res === 0) {
						//场景判断
						if (this.phoneNumber) {//短信场景进入
							this.loginFag = true;
							uni.showLoading({
								title: "绑定身份加入中，请稍后..."
							})
							this.$common.RequestDataNo({
								url: this.$common.postAuth,
								data: {
									openId: uni.getStorageSync("openId"),
									avatar: 'https://img.starup.net.cn/xmkj/zwb/img/wx.png',
									phone: this.phoneNumber,
								},
							}, res => {
								uni.hideLoading();
								//缓存token和微信用户信息
								uni.setStorageSync("token", res.data.token)
								uni.setStorageSync("user", res.data.user);
								this.$common.msg(res.msg);
								this.phoneNumber = null;
								this.getCard();
								this.getUserInfo();
								if (this.$common.wxAppId && !uni.getStorageSync("user").wxOpenId && uni.getStorageSync("token")) {
									uni.navigateTo({
										url: '/pages/login/gzhweb'
									})
								}
							})
						} else{
							//只有游客是需要去授权获取手机号的--跳转授权注册页
							self.$common.noLoginBox();
						}
					} else if (res > 0) {
						if (this.$common.wxAppId && !uni.getStorageSync("user").wxOpenId) {
							uni.navigateTo({
								url: '/pages/login/gzhweb'
							})
						}
						if(!uni.getStorageSync("user").phone && this.systemeven('tourist')){
							this.$refs.guipopup2.open()
						}
						// console.log(this.$common.systemlist,'权限')
						// console.log(uni.getStorageSync("user").phone,this.systemeven('tourist'),'是否有手机号码')
						this.loginFag = true
						//确认登录后刷新数据
						//获取当前登录用户默认关联就诊卡
						this.getCard();
						this.getUserInfo();
						// this.getHeathList();
					}
				});
				// this.getarticleList(); //文章信息
				this.getFl();
			},
			//获取用户信息刷新
			getUserInfo(){
				getUserInfo({
					openId: uni.getStorageSync('openId')
				}).then(res=>{
					if(res.data && res.data.openId){
					    this.$set(this,'isSubscribe',res.data.isSubscribe)
						this.$forceUpdate();
					    uni.setStorageSync('user', res.data);
					}
					// console.log('用户信息===',res.data)
					// console.log('公众号关注状态===',res.data.isSubscribe)
				})
			},
			//获取当前登录者绑定的就诊卡
			getCard() {
				getCard().then(res => {
						let cardList = res.data.cardList;
						this.cardList = cardList
						if (cardList.length > 0) {
							if (uni.getStorageSync('cardObj')) {
								const patientId = uni.getStorageSync('cardObj')?.patientId;
								if (patientId) {
									this.cardObj = cardList.find(card => String(card.patientId) === String(patientId));
									uni.setStorageSync("cardObj", this.cardObj); //缓存res是默认的就诊卡
									this.$common.getNoReads()
								} else {
									this.cardObj = cardList[0];
									uni.setStorageSync("cardObj", cardList[0]);
									this.$common.getNoReads()
								}
							} else {
								this.cardObj = cardList[0];
								uni.setStorageSync("cardObj", cardList[0]);
								this.$common.getNoReads()
							}
							//缓存res是默认的就诊卡
							this.getContractDoctor(this.cardObj.patientId)//签约的科室和医生
							this.getFuzhen(this.cardObj.patientId)//复诊提醒
						} else {
							this.showcontract = false;
							this.cardObj = {};
							uni.removeStorageSync("cardObj")
							this.$common.getNoReads()
						}
					this.sign()
					this.getHeathList();
					this.$common.role();
					this.graceIMConnect()
					// 健康日记医生反馈数量
					this.getcountFeedback()
					// 引导绑卡
					this.guideStep()
				}, fail => {
					this.cardObj = {};
					this.sign()
					this.graceIMConnect()
					uni.removeStorageSync("cardObj")
				})
				this.getUserData();
			},
			// 健康日记医生反馈数量
			getcountFeedback() {
				if (!uni.getStorageSync("cardObj")) {return}
				var patientId = uni.getStorageSync("cardObj")?.patientId || ''
				countFeedback({
					patientId:patientId
				}).then(res=>{
					this.healthyNum = res.data
					console.log(res,'健康日记医生反馈数量')
				})
			},
			//查询签约科室和医生
			getContractDoctor(patientId){
				if (!patientId) {return}
				contractDoctor({patientId:patientId}).then(res =>{
					if (res.data.length) {
						this.contractList = res.data;
						this.showcontract = true;
					}
				})
			},
			//复诊提醒
			getFuzhen(patientId){
				if (!patientId) {return}
				noticeList({
					pageNum:1,
					pageSize:1,
					memberId:uni.getStorageSync('user').id,
					patientId:patientId,
					state:6
				}).then(res=>{
					if (res.rows.length) {
						this.fuzData = res.rows[0];
					}else{
						this.fuzData = {}
					}
						this.$forceUpdate()
				})
			},
			//复诊详情
			toFuzhen(item){
				if (item.taskId) {
					this.$common.navTo('/pages/homeAc/other/furtherVisit?id='+item.taskId)
				}
			},
			//立即咨询
			tabSelect(doctorList) {
				getImUser({
					userUid: doctorList.user_id,
					userId: uni.getStorageSync('user').id
				}).then(res => {
					let imObj = {
						groupIndex: res.data.groupIndex, // 组ID
						friendName: doctorList.nickName, // 医生姓名
						friendUid: res.data.dcUser.userUid, // 医生UUid
						userUid: res.data.hzUser.userUid, // 当前用户UUid
						doctorId: doctorList.user_id, // 医生id
						avatar: 'https://img.starup.net.cn/xmkj/zwb/2022/02/11/08050225c71d4722ab100b26c6dac5c9.png',
						name: doctorList.nickName,
						appId:doctorList.appId,
					};
					this.$common.navTo('/pages/home/<USER>/consult?imObj=' + encodeURIComponent(JSON.stringify(imObj)));
				})
			},
			// 引导绑卡
			guideStep(){
				constitutionFillContent({
					patientId :uni.getStorageSync("cardObj").patientId || '',
					memberId :uni.getStorageSync("user").id
				}).then(res=>{
					if (res.data) {
						this.completion = true
					}
				})
			},
			graceIMConnect(){
				this.$store.dispatch('graceIMConnect', this.imUser)
			},
			//获取登录用户的其他关联数据（所有登录后才能拉的数据统一汇集到这）
			getUserData() {
				this.getIntegral()
				if (this.systemeven('activityManagement')) {
					this.getActivity();//活动专区
				}
			},
			// 添加就诊人
			addPatient() {
				this.isshow = false
				this.$common.openDyMsg(0,1);
				this.$common.navTo('/pages/myAc/other/addPatient')
			},

			//查看积分明细
			inDetail() {
				this.$common.navTo('/pages/homeAc/other/integral')
			},
			// 获默认就诊卡总积分
			getIntegral() {
				let patient = uni.getStorageSync('cardObj').patientId
				if (patient) {
					getPatientInfo({
						patientId: patient
					}).then(res => {
						this.integral = res.data.integral
					})
				}else{
					this.integral = 0
				}
			},
			// 绑定就诊卡
			addCard() {
				this.codeClick()
			},
			// // 健康记录
			record() {
				if (!this.$common.isLogin()) {
					this.$common.noLoginBox();
					return
				}
				if (uni.getStorageSync('cardObj') && uni.getStorageSync('cardObj').patientId) {
					// this.$common.openDyMsg(0,2);
					this.$common.navTo('/pages/homeAc/record/index')
				} else {
					this.$common.msg("请先绑定就诊卡")
				}

			},
			// 扫码
			codeClick() {
				var that = this
				uni.scanCode({
					success: (res) => {
						if (res.result || res.path) {
							var val = '';
							val = res.path.split("=")[1]
							if(val&&val[0] == 'p'){
								var visitCardNum = val.slice(2)
								that.visitCardNum = visitCardNum
								that.visitCard()
								return
							}
							that.$common.msg('请重新扫描')
						} else {
							that.$common.msg('请重新扫描')
							return false;
						}
					},fail: (res) => {
					}
				})
			},
			// 科室介绍
			department(e) {
				uni.setStorageSync("departmentList", e);
				this.$common.navTo('/pages/homeAc/other/department')
			},
			//体质测评
			evaluation() {
				this.$common.navTo('/pages/homeAc/evaluation/list')
				// var self=this;
				// if(self.systemeven('tourist')){
				// 	this.$common.navTo('/pages/homeAc/evaluation/list')
				// 	// this.$common.navTo('/pages/homeAc/evaluation/index')
				// 	return
				// }
				// if (uni.getStorageSync('cardObj') && uni.getStorageSync('cardObj').patientId) {
				// 	this.$common.navTo('/pages/homeAc/evaluation/list')
				// } else {
				// 	this.$common.msg("请先绑定就诊卡")
				// }

			},
			//咨询
			imChat() {
				this.$common.openDyMsg(0,1);
				this.$common.navTo('/pages/home/<USER>/chat?key=1')
			},
			// 任务
			task() {
				this.$common.navTo('/pages/care/index')
			},
			// 切换就诊卡
			patient() {
				uni.removeStorageSync('role')
				this.$common.openDyMsg(0,1);
				if (!this.$common.isLogin()) {
					this.$common.noLoginBox();
					return
				}
				this.$common.navTo('/pages/myAc/other/patient')
				this.formData = {}
			}
		}
	}
</script>

<style scoped>
	.tab-list-item{
		width: calc(100% / 5);
		margin-bottom: 20rpx;
	}
	.tab-list{

	}
	.demo-lr-content-span{
		text-align: center;
	}
	.demo-lr{
		border-radius: 20rpx;
	}
	.demo-lr-content{
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		color: #999;
		text-align: center;
		font-size: 38rpx;
	}
	.demo-lr-title{
		padding: 60rpx 30rpx 0rpx 30rpx;
		text-align: center;
		font-weight: bold;
		font-size: 38rpx;
	}
	.btn-phone{
		padding:0 !important
	}
	.confirmText{
		color: #55aaff;
		padding: 30rpx 0;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 38rpx;
		flex:1;
		font-weight: bold;
	}
	.cancelText{
		font-size: 38rpx;
		justify-content: center;
		align-items: center;
		font-weight: bold;
		text-align: center;
		padding: 30rpx 0;
		flex:1;
		border-right: 2rpx solid #eee;
	}
	.demo-lr-btn{
		display: flex;
		border-top: 2rpx solid #eee;
		flex-direction: row;
		justify-content: space-between;
	}
	.gui-scroll-x-items{width:650rpx;  border-top-left-radius: 10rpx;border-top-right-radius: 10rpx;}
	.gui-scroll-image{width:650rpx; height:260rpx; border-top-left-radius: 10rpx;border-top-right-radius: 10rpx;}
	.gui-badge-absolute {
		right: 8rpx;
		top: -10rpx;
	}

	/* 弹窗 */
	.Ashowfu {
		display: flex;
		height: 100%;
		width: 100%;
		z-index: 9999;
		margin: 0 auto;
		position: absolute;
		top: 0rpx;
		background: rgba(0, 0, 0, .5);
	}

	.showfu {
		width: 80%;
		height: 500rpx;
		background-color: #ffffff;
		z-index: 999;
		margin: 0 auto;
		position: absolute;
		top: 550rpx;
		left: 80rpx;
		border-radius: 15rpx;
		padding: 20rpx 20rpx;
		border: 1px solid #eee;
		box-shadow: 1px 1px 3px gray;

	}

	.showtitl {
		padding: 40rpx;
		font-size: 34rpx;
		color: #2e2e2e;
	}

	.showcon {
		margin-top: 100rpx;
		color: #757575;
		width: 90%;
		margin: 0 auto;
		text-indent: 2em;
		font-size: 32rpx;
		line-height: 55rpx;
	}

	.showbtn {
		/* margin-top: 100rpx; */
		margin-bottom: 15rpx;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		color: #2196f3;
		margin-right: 30rpx;
	}

	.btn01 {
		display: flex;
		font-size: 32rpx;
		margin-right: 40rpx;
	}

	.btn02 {
		display: flex;
		font-size: 32rpx;
		/* margin-left: 15rpx; */
	}

	.gui-waterfall-item {
		width: 330rpx;
	}

	.gui-waterfall-items {
		margin-bottom: 28rpx;
	}

	.zxtag {
		padding: 5rpx 20rpx;
		border-radius: 20rpx;
		margin: 0 10rpx;
	}

	.zxtagb {
		color: #ffffff;
		background-color: #c59f79;
	}

	.top-radius {
		border-top-left-radius: 40rpx;
		border-top-right-radius: 40rpx;
	}

	.demo-msg {
		white-space: nowrap;
		line-height: 80rpx;
		font-size: 28rpx;
	}

	.saoicon {
		font-size: 46rpx;
	}

	.logosize {
		display: flex;
		width: 60rpx;
		height: 60rpx;
		margin-left: 60rpx;
	}


	.gui-grids-icon {
		height: 100rpx;
		line-height: 100rpx;
	}

	.shadow {
		box-shadow: 0px 8rpx 32rpx 0rpx #F1F1F1;
	}

	.bar {
		background: #1abb93;
		padding: 7rpx 4rpx;
		margin-right: 20rpx;
	}

	.borShadow {
		border: 1px solid #f1f1f1;
		box-shadow: 0px 0px 3px #f1f1f1;
	}

	.gui-badge {
		/* padding:20rpx; */
	}

	swiper {
		height: 480rpx;
	}

	swiper image {
		width: 100%;
		height: 480rpx;
	}

	.bgs {
		height: 48px;
		background-position: center;
	}

	.btc {
		background-image: url(https://img.starup.net.cn/xmkj/zwb/img/zy_home_zs.png);
		background-position: left bottom;
		background-repeat: no-repeat;
		background-size: 100%;
		opacity: 0.3;
		width: 200rpx;
		height: 60rpx;
	}

	.btc2 {
		font-weight: bold;
		color: #88735E;
		font-size: 32rpx;
		margin-top: -60rpx;
		margin-left: 20rpx;
	}

	.jl-box {
		background: #f3efeb;
		padding: 20rpx 15rpx;
		border-radius: 10rpx;
		position: relative;
		margin-top: 30rpx;
		font-size: 28rpx;
		border: 1rpx solid #b7aaa1;
		color: #88735E;
	}

	.sanjiao {
		width: 20rpx;
		height: 20rpx;
		background: #f3efeb;
		border-left: 1rpx solid #b7aaa1;
		border-top: 1rpx solid #b7aaa1;
		transform: rotate(45deg);
		overflow: hidden;
		position: absolute;
		top: -12rpx;
		left: 30rpx;
	}

	.wbk {
		border-radius: 4px;
		background: #fbf7f5;
		border: 1rpx dashed #c59f79;
	}
</style>
