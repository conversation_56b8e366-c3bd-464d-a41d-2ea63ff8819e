<template>
	<view class="w-100 over-hidden">
		<view class="p-10 shadow2">
			<view class="content font-bold">
				<text style="color: #333">就诊人：</text>
				<text style="color: #333">{{cardObj.userName}}</text>
			</view>
			<view class="content">
				<text>就诊科：</text>
				<text>{{info.deptName}}</text>
			</view>
			<view class="content">
				<text>就诊时间：</text>
				<text>{{info.visitDate || '-'}} {{info.timeShard || ''}}</text>
			</view>
		</view>
		<view class="navlist">
			<view @click="navClick(index)" :class="['nav-item',{'nav-activate':index == navindex}]" v-for="(item,index) in navlist">{{item.name}}</view>
		</view>
		
		<view class="reportBox" v-if="navindex == 0">
			<!-- <view class="d-flex jc-between py-10 mx-30">
				<view class="fs-32 d-flex ai-center">
					<image src="https://img.starup.net.cn/xmkj/zwb/img/zy_ico_bga.png" class="w40 h40 mr-15"></image>
					<text style="color: #9AA7B9;">报告文档（共{{bglist.length}}份）</text>
				</view>
				<view @click="$common.navTo('../other/proposal')">
					<text class="gui-grids-icon gui-icons fs-28" style="color: #9AA7B9;">更多&#xe601;</text>
				</view>
			</view> -->
			<view class="m-30 ellipsis-1 over-hidden p-30" style="line-height: 45rpx;background-color: #F5F6F8;" v-for="(item,index) in bglist" :key="index" @click="toDetail(item)">
				<text class="fs-40 gui-icons" style="color: #9AA7B9;">&#xe62f;</text>
				<text class="ml-30 fs-28 font-bold">{{item.title}}</text>
			</view>
			<view v-if="bglist.length<= 0">
				<gui-empty>
					<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
						<image class="gui-empty-img mt-5" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
					</view>
					<text slot="text" 
					class="gui-text-small gui-block-text gui-text-center mb-30" style="color:#B2B2B2;">暂无数据</text>
				</gui-empty>
			</view>
		</view>
		
		<view class="reportBox"  v-if="navindex == 1">
			<view class="d-flex jc-between py-10 mx-30">
				<view class="fs-32 d-flex ai-center">
					<image src="https://img.starup.net.cn/xmkj/zwb/img/zy_ico_bga.png" class="w40 h40 mr-15"></image>
					<text style="color: #9AA7B9;">健康宣教</text>
				</view>
			</view>
			<view v-for="(item,index) in xjList" :key="index" @click="detail(item)">
				<view style="background-color: #F5F6F8;" class="m-30 p-20">
					<view class="d-flex ai-center">
						<view class="mr-20"><gui-tags :margin="0" :text="$common.parseTime(item.createTime,'{y}-{m}-{d}')" :size="22" bgClass="gui-bg-white" color="#9AA7B9" borderColor="#9AA7B9"></gui-tags></view>
						<text style="color: #9AA7B9;font-size: 28rpx;">宣教</text>
					</view>
					<view class="d-flex ai-center mt-15">
						<view style="flex: 1;" class="ellipsis-1 fs-28 font-bold">{{item.questionnaireName}}</view>
						<text v-if="item.status == 0" class="label2 ml-20 fs-28">待执行</text>
						<text v-else-if="item.status == -1" class="label ml-20 fs-28">已撤回</text>
						<text v-else class="label ml-20 fs-28">已完成</text>
					</view>
				</view>
			</view>
			<view v-if="xjList.length== 0">
				<gui-empty>
					<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
						<image class="gui-empty-img mt-5" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
					</view>
					<text slot="text" 
					class="gui-text-small gui-block-text gui-text-center mb-30" style="color:#B2B2B2;">暂无数据</text>
				</gui-empty>
			</view>
		</view>
		<view class="reportBox" v-if="navindex == 2">
			<view class="d-flex jc-between py-10 mr-30 ml-20">
				<view class="fs-32 d-flex ai-center">
					<image src="https://img.starup.net.cn/xmkj/zwb/img/zy_jkjy_jil.png" class="w50 h50 mr-15"></image>
					<text style="color: #9AA7B9;">随访问卷</text>
				</view>
			</view>
			<view v-for="(item,index) in sfList" :key="index" @click="detail(item)">
				<view style="background-color: #F5F6F8;" class="m-30 p-20">
					<view class="d-flex ai-center">
						<view class="mr-20"><gui-tags :margin="0" :text="$common.parseTime(item.createTime,'{y}-{m}-{d}')" :size="22" bgClass="gui-bg-white" color="#9AA7B9" borderColor="#9AA7B9"></gui-tags></view>
						<text style="color: #9AA7B9;font-size: 28rpx;">问卷</text>
					</view>
					<view class="d-flex ai-center mt-15">
						<view style="flex: 1;" class="ellipsis-1 fs-28 font-bold">{{item.questionnaireName}}</view>
						<text v-if="item.status == 1" class="label ml-20 fs-28">已完成</text>
						<text v-if="item.status == 0" class="label2 ml-20 fs-28">待执行</text>
						<text v-if="item.status == 2" class="label ml-20 fs-28">已终止</text>
						<text v-if="item.status == 3" class="label ml-20 fs-28">已过期</text>
						<text v-if="item.status == -1" class="label ml-20 fs-28">已撤回</text>
					</view>
				</view>
			</view>
			<view v-if="sfList.length== 0">
				<gui-empty>
					<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
						<image class="gui-empty-img mt-5" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
					</view>
					<text slot="text" 
					class="gui-text-small gui-block-text gui-text-center mb-30" style="color:#B2B2B2;">暂无数据</text>
				</gui-empty>
			</view>
		</view>
		<view class="h50"></view>
	</view>
</template>


<script>
	import {healthDetail,heathList} from '@/api/home.js'
	export default {
		data() {
			return {
				navindex:0,
				navlist:[{id:1,name:'报告文档'},{id:2,name:'健康宣教'},{id:3,name:'随访问卷'}],
				cardObj: uni.getStorageSync('cardObj'),
				list:[],
				bglist:[],
				info:{},
				xjList:[],
				sfList:[],
			}
		},
		onLoad(option) {
			this.info = JSON.parse(decodeURIComponent(option.item))
			this.getList();
			this.getDetail();
		},
		methods:{
			navClick(e){
				this.navindex = e
			},
			toDetail(item){// 膳食建议
				let id = uni.getStorageSync('cardObj').patientId;
				this.$common.navTo('/pages/homeAc/other/salDetail?templateDictKey='+item.templateDictKey+'&patientId='+id + "&title="+item.title + '&templateId=' + item.templateId + '&visitRecordId=' + item.visitRecordId+'&createTime='+item.createTime+'&programmeType='+item.programmeType)
				
			},
			getDetail(){
				healthDetail({
					patientId : uni.getStorageSync('cardObj').patientId 
				}).then(res=>{
					if(res.rows.length>0){
						this.xjList = res.rows.filter(item=>{return item.taskType == 2})
						this.sfList = res.rows.filter(item=>{return item.taskType == 1})
					}
				})
			},
			// 获取报告列表
			getList(){
				heathList({
					patientId : uni.getStorageSync('cardObj').patientId, visitRecordId: this.info.id
				}).then(res=>{
					this.bglist = res.data || [];
				})
			},
			// 跳转详情页
			detail(item) {
				if (item.taskType == 2) {
					// 宣教
					this.$common.navTo('/pages/task/other/missionary?id='+item.id+'&taskType='+item.taskType)
				} else if(item.taskType == 1) {
					// 问卷
					this.$common.navTo('/pages/task/other/questionnaire?id='+item.id+'&taskType='+item.taskType)
				}else if(item.taskType == 4) {
					// 治疗
					this.$common.navTo('/pages/task/other/treatment?id='+item.id+'&taskType='+item.taskType)
				}else {
					// 监测
					let i = parseInt(item.questionnaireId);
					switch (i) {
						case 1:
							this.$common.navTo('/pages/task/other/aXueya?id='+item.id+'&taskType='+item.taskType)
							break;
						case 2:
							this.$common.navTo('/pages/task/other/bMaibo?id='+item.id+'&taskType='+item.taskType)
							break;
						case 3:
							this.$common.navTo('/pages/task/other/cXuetang?id='+item.id+'&taskType='+item.taskType)
							break;
						case 4:
							this.$common.navTo('/pages/task/other/dXueyang?id='+item.id+'&taskType='+item.taskType)
							break;
						case 5:
							this.$common.navTo('/pages/task/other/eTiwen?id='+item.id+'&taskType='+item.taskType)
							break;
						case 6:
							this.$common.navTo('/pages/task/other/fHuxi?id='+item.id+'&taskType='+item.taskType)
							break;
					}
					uni.setStorageSync('jcData',item)
					// this.$common.navTo('/pages/task/other/aXueya?id='+item.id)
				}
				// if (item.taskType == 2) { 
				// 	// 宣教
				// 	this.$common.navTo('/pages/task/other/missionary?id='+item.id+'&taskType='+item.taskType)
				// } else {
				// 	// 问卷
				// 	this.$common.navTo('/pages/task/other/questionnaire?id='+item.id)
				// }
			}
		}
	}
	
</script>

<style scoped>
	.navlist{
		display: flex;
		flex-direction: row;
	}
	.nav-item{
		flex:1;
		border-bottom: 2rpx solid #fff;
		text-align: center;
		padding: 20rpx 0;
	}
	.nav-activate{
		color: #007aff;
		border-bottom: 2rpx solid #007aff !important;
	}
	.shadow2 {
		box-shadow: 0px 20rpx 20rpx 0rpx #f7f7f7;
	}
	.content {
		width: 100%;
		padding: 0 28rpx;
		font-size: 28rpx;
		margin: 14rpx 0;
	}

	.content text {
		color: #A5A5A5;
	}

	/* .content text:nth-child(2) {
		margin-left: 48rpx;
		color: #333333;
	} */

	.title {
		text-align: center;
		color: #3DAB7E;
		font-size: 32rpx;
		font-weight: bold;
		margin: 11rpx 0 30rpx 0;
	}

	.title view {
		margin: -16rpx auto;
		width: 0;
		padding: 10rpx 90rpx;
		background: #E3F3ED;
	}

	.label {
		background: #BFCBD9;
		color: #fff;
		padding: 10rpx 16rpx;
		font-size: 24rpx;
	}

	.label2 {
		background: #F66829;
		color: #fff;
		padding: 10rpx 16rpx;
		font-size: 24rpx;
	}

	.bar {
		background: #007aff;
		padding: 2rpx 4rpx;
		margin-right: 20rpx;
		border-radius: 5rpx;
	}

	.reportBox {
		margin: 20rpx auto;
	}

	.borShadow {
		border: 1px solid #f1f1f1;
		box-shadow: 1px 2px 2px #f1f1f1;
	}
</style>
