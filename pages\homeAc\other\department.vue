<template>
	<view>
		<view class="gui-grids gui-flex gui-rows gui-wrap mt-15 demo-nav2">
			<gui-segmented-control style="width: 95%;margin: auto;" :items="tabs" @change="navchange"></gui-segmented-control>
		</view>
		<view class="p-20">
			<view v-show="currentIndex == 0" class="mt-10">
				<view class="text-center" v-show="ksObj.imageUrl">
					<image :src="ksObj.imageUrl" class="w-100" mode="aspectFill" style="height: 350rpx;"></image>
				</view>
				<view class="gui-flex" @click="openks()">
					<view class="text-left py-5 fs-30" style="text-indent: 2em;line-height: 50rpx;" v-html="ellipsis(ksObj.briefIntro)" />
				</view>
				<view class="mt-20" v-show="departmentList.appId || departmentList.id">
					<view class="gui-bold gui-flex fs-30"><view class="icondemo mr-15" ></view>科普文章</view>
					<view class="mt-10">
						<!-- <scroll-view scroll-y="true" > -->
							<kepu ref="kepus" :departmentId="departmentList.appId?departmentList.appId:departmentList.id"></kepu>
							<gui-loadmore ref="loadmorecom" :status="loadState" class="w-100" style="box-sizing: border-box;"></gui-loadmore>
						<!-- </scroll-view> -->
					</view>
				</view>
			</view>
			<view v-show="currentIndex == 1">
				<view class="" v-for="(item,index) in ysList" :key="index">
					<view class=" gui-flex gui-row gui-border-b pb-10 mb-20" @tap="open(item)">
						<view class="pl-5" style="width: 220rpx;height: 250rpx; border: 1px solid #C59F79; padding: 3rpx;">
							<image :src="item.showAvatar || '/static/yisheng.png'" style="width: 100%;height: 100%;"/>
						</view>
						<view class="ml-20 fs-28" style="width: 530rpx;">
							<view class=" gui-flex gui-align-items-end">
								<view class="fs-36 mr-10">{{item.nickName || '-'}}<text class="gui-color-gray pl-10">|</text></view>
								<view class="mr-20 gui-color-gray">{{item.docTitle || '-'}}</view>
							</view>
							<view class="mt-20">
								科 室：<text class="gui-color-gray">{{item.manageRoom || item.dept.deptName || '无'}}</text>
							</view>
							<view class="mt-10 text-coten">
								擅 长 ： <text class="gui-color-gray">{{item.specialty || '-'}}</text>
							</view>
						</view>
					</view>
				</view>
				<gui-empty v-if="ysList.length == 0 ">
					<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
						<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png" mode="widthFix"></image>
					</view>
					<text slot="text"
					class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
				</gui-empty>
			</view>
		</view>
		<!-- 科室简介弹窗 -->
		<uni-popup ref="popup" type="bottom" :safe-area="false">
			<view class="gui-bg-white" style="height: 85vh;border-top-right-radius: 20rpx;border-top-left-radius: 20rpx;">
				<view class="py-20 gui-flex gui-border-b " style="position: relative; border-bottom: 1px solid #eeeeee;" @click="closepopup">
					<view class="fs-32 font-bold w-100" style="text-align: center;"> <text >科室简介</text> </view>
					<view style="position: absolute; right: 45rpx;">
						<text class=" gui-icons gui-color-gray fs-40">&#xe7a5;</text>
					</view>
				</view>
				<view class="gui-padding pb-30 pt-10">
					<scroll-view scroll-y="true" :show-scrollbar="true" style="height: 75vh;">
						<view class="text-left py-5" style="text-indent: 2em;line-height: 50rpx;font-size: 28rpx;" v-html="$common.adjustRichTextImageSize(ksObj.briefIntro)" />
					</scroll-view>
				</view>
			</view>
		</uni-popup>
		<!-- 医生信息弹窗 -->
		<gui-modal ref="guimodal" title="医生信息" titleStyle="line-height:100rpx; font-size:40rpx; font-weight:700; color:#2B2E3D;">
			<view slot="content" class="gui-padding">
				<view class="gui-list fs-30">
					<view class="gui-list-items">
						<view class="gui-relative">
							<image class="gui-grids-icon-img ucenter-face-image" style="width: 150rpx; height: 160rpx;"
							:src="doctorList.showAvatar || '/static/yisheng.png'" ></image>
						</view>
						<view class="gui-list-body">
							<view class="gui-list-title">
								<text class="gui-list-title-text gui-primary-color fs-36">{{doctorList.nickName|| '医生'}}</text>
							</view>
							<text class="gui-color-gray gui-block-text">职 称 : &nbsp;&nbsp;{{ doctorList.docTitle || '暂未填写'}}</text>
							<text class="gui-color-gray gui-block-text">科 室 : &nbsp;&nbsp;{{ doctorList.manageRoom || doctorList.dept.deptName || '无'}}</text>
						</view>
					</view>
					<scroll-view scroll-y="true" style="height: 580rpx;">
							<view class="gui-flex gui-columns pb-20">
								<text class="p-10">专业擅长</text>
								<text class="gui-color-gray ">{{doctorList.specialty || '暂未填写'}}</text>
							</view>
							<view class="gui-flex gui-columns">
								<text class="p-10">医生简介</text>
								<text class="gui-color-gray ">{{doctorList.intro || '暂未填写'}}</text>
							</view>
					</scroll-view>
				</view>
			</view>
			<view slot="btns" class="gui-flex gui-rows gui-justify-content-end" style="border-top:1rpx solid #e2e2e2;">
				<view class="modal-btns gui-flex1" @tap="close1" >
					<text class="modal-btns gui-color-gray" >取消</text>
				</view>
			</view>
		</gui-modal>
	</view>
</template>
<script>
	import {ksjj,getDoctorList} from '@/api/home.js'
	import kepu from "@/pages/homeAc/component/kepu.vue"
	export default {
		components:{kepu},
		data() {
			return {
				loadState: 0 ,  // 0 : 默认0，有下一页   1 ：请求中  2： 加载完毕
				//医生信息
				ysList:[],
				doctorList:[],
				// 选项卡标签
				tabs: [{
					id: 0,
					dictLabel: '科室介绍'
				}, {
					id: 1,
					dictLabel: '专家简介'
				}],
				// 选中选项的 索引
				currentIndex: 0,
				ksObj:{},
				departmentList:{}//科室信息
			}
		},
		onLoad() {
			this.departmentList = uni.getStorageSync('departmentList');
			uni.removeStorageSync('departmentList')
		},
		onShow() {
			this.navchange(this.currentIndex)
			// this.getInfo();
			// this.getDoctorList();
		},
		methods: {
			ellipsis(text) {
				if (text && text.length > 0) {
					var conet =text.replace(/\<img/gi, '<img style="width:100%;margin:auto;height:auto;"');
					var value = conet.replace(/<.*?>/ig,"")       //把v-html的格式标签替换掉
					if (value.length > 66) {
						return value.slice(0, 66) + "...";
					}else{
						return value
					}
				}
			},
			getInfo(){
				if (this.departmentList.deptId) {
					ksjj(Number(this.departmentList.deptId)).then(res=>{
						this.ksObj = res.data;
					})
				}
				
			},
			//医生列表
			getDoctorList(){
				getDoctorList({
					// patientId: uni.getStorageSync("cardObj").patientId,
					deptId:this.departmentList.deptId,
					type : 1
				}).then(res=>{
					this.ysList = res.data
				})
			},
			//医生信息展示
			open(item) {
				this.doctorList = item
				this.$refs.guimodal.open();
			},
			//科室信息展示
			openks() {
				this.$refs.popup.open()
			},
			closepopup(){
				this.$refs.popup.close()
			},
			//弹窗关闭
			close1() {
				this.$refs.guimodal.close();
			},
			// 切换
			navchange: function(index) {
				this.currentIndex = index;
				if (index == 1) {
					this.getDoctorList();
				} else{
					this.getInfo();
					this.$refs.kepus.getFl(this.departmentList.appId?this.departmentList.appId:this.departmentList.id)
				}
			},
		}
	}
</script>
<style scoped>
	.icondemo{
		width: 15rpx;
		height: 40rpx;
		background-color: #C59F79;
		border-radius: 10rpx;
	}
	.text-coten{
		-webkit-line-clamp: 3;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	.tab-card-demo-text {
		line-height: 388rpx;
		font-size: 26rpx;
	}
	.modal-btns{line-height:88rpx; font-size:32rpx; text-align:center; width:200rpx;}
</style>
