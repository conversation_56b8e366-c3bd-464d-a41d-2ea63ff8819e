<template>
	<!-- <view style="padding-top: 5vh;"> -->
	<view>
		<form @submit="submit">
			<view class="gui-form-item gui-border-b" style="margin-top: 50rpx;">
				<text class="gui-form-label" style="margin-left: 30rpx;width: 250rpx;">当前身高（cm）</text>
				<view class="gui-form-body">
					<input type="digit" class="gui-form-input" v-model="formData.height" name="height"
						placeholder="请输入身高" />
				</view>
			</view>
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label" style="margin-left: 30rpx;width: 250rpx">当前体重（kg）</text>
				<view class="gui-form-body">
					<input type="digit" class="gui-form-input" v-model="formData.weight" name="weight"
						placeholder="请输入体重" />
				</view>
			</view>
			<view class="gui-form-item gui-border-b" v-if="openType==3 && weightConfig.includes('1')">
				<text class="gui-form-label" style="margin-left: 30rpx;width: 250rpx">当前腰围（cm）</text>
				<view class="gui-form-body">
					<input type="digit" class="gui-form-input" v-model="formData.waistline" name="waistline"
						placeholder="请输入腰围" />
				</view>
			</view>
			<view class="gui-form-item gui-border-b" v-if="openType==3&& weightConfig.includes('2')">
				<text class="gui-form-label" style="margin-left: 30rpx;width: 250rpx">当前腹围（cm）</text>
				<view class="gui-form-body">
					<input type="digit" class="gui-form-input" v-model="formData.abdomen" name="abdomen"
						placeholder="请输入腹围" />
				</view>
			</view>
			<view class="gui-form-item gui-border-b" v-if="openType==3&& weightConfig.includes('3')">
				<text class="gui-form-label" style="margin-left: 30rpx;width: 250rpx">当前臀围（cm）</text>
				<view class="gui-form-body">
					<input type="digit" class="gui-form-input" v-model="formData.hipline" name="hipline"
						placeholder="请输入臀围" />
				</view>
			</view>
			<view class="gui-form-item gui-border-b" v-if="openType==3&& weightConfig.includes('4')">
				<text class="gui-form-label" style="margin-left: 30rpx;width: 250rpx">腰臀比</text>
				<view class="gui-form-body">
					<input type="digit" class="gui-form-input" v-model="formData.waisHipRatio" name="waisHipRatio"
						placeholder="请输入腰臀比" />
				</view>
			</view>
			<view class="gui-form-item gui-border-b" v-if="openType==3&& weightConfig.includes('5')">
				<text class="gui-form-label" style="margin-left: 30rpx;width: 250rpx">PBF（%）</text>
				<view class="gui-form-body">
					<input type="digit" class="gui-form-input" v-model="formData.pbf" name="pbf"
						placeholder="请输入PBF" />
				</view>
			</view>
			<view class="gui-form-item gui-border-b" v-if="openType==3&& weightConfig.includes('6')">
				<text class="gui-form-label" style="margin-left: 30rpx;width: 280rpx">内脏脂肪指数(cm²)</text>
				<view class="gui-form-body">
					<input type="digit" class="gui-form-input" v-model="formData.visceralFatIndex" name="visceralFatIndex"
						placeholder="请输入内脏脂肪指数" />
				</view>
			</view>
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label" style="margin-left: 30rpx;">记录日期</text>
				<view class="gui-form-body">
					<gui-datetime @confirm="confirm4" :startYear="$common.parseTime(new Date(birthday),'{y}')"
						:value="formData.measureDate" ref="timeBegin" :units="['年', '月', '日']" :isTime="false">
						<text class="demo gui-icons">{{ formData.measureDate }}</text>
					</gui-datetime>
				</view>
			</view>
			<view v-if="submitShow" class=" gui-flex gui-rows gui-nowrap gui-justify-content-center" :style="openType==3?'margin-top: 20%;':'margin-top: 80%;'">
				<button type="default" class="gui-button" style="width:400rpx;background-color: #C59F79;"
					formType="submit">
					<text class="gui-icons gui-color-white gui-button-text">保 存</text>
				</button>
			</view>
			<view style="height:80rpx;"></view>
		</form>
	</view>
</template>

<script>
	import {
		measurelist,
		measure,
		getAssessment
	} from '@/api/children.js'
	// import { measure } from '@/api/children.js'
	import {
		addTaskPerform,
		getFollowTaskInfo,
		getByPlanId,getWeightDataItemConfig
	} from '@/api/task.js'
	export default {
		data() {
			return {
				openType:0,//3的时候为减重记录
				showtask: false,
				latestheight: 0,
				id: '',
				taskType: '',
				formData: {
					measureDate: '',
					height: '',
					weight: ''
				},
				birthday: uni.getStorageSync('legao')?.birthday || '',
				submitShow:true,//显示保存按钮
				weightConfig:[],//获取字典1-腰围,2-腹围,3-髫围,4-腰鹮比,5-PBF,6-内脏脂肪指数，7治疗方案
			}
		},
		onLoad(options) {
			this.id = options.id;
			this.taskType = options.taskType;
			if (options.openType) {
				this.showtask = true;
				this.openType = options.openType;
				uni.setNavigationBarTitle({
					title: '添加减重记录'
				});
				this.getConfig()//获取字典1-腰围,2-腹围,3-髫围,4-腰鹮比,5-PBF,6-内脏脂肪指数，7治疗方案
			}
			if (options.timedata) {
				this.showtask = true;
				this.formData.measureDate = this.$common.parseTime(options.timedata, '{y}-{m}-{d}');
				getFollowTaskInfo({
					id: this.id,
					taskType: this.taskType
				}).then(res => {
					if (res.data.monitorInfo && res.data.monitorInfo.taskId) {
						this.formData = {
							...this.formData,
							monitorId: res.data.monitorInfo.monitorId,
							monitorName: res.data.monitorInfo.monitorName,
							taskId:res.data.monitorInfo.taskId,
							setTaskId:res.data.monitorInfo.setTaskId,
							measureDate : this.$common.parseTime(new Date(), '{y}-{m}-{d}')
						}
					}
				})
			} else {
				this.formData.measureDate = this.$common.parseTime(new Date(), '{y}-{m}-{d}');
			}
			if (uni.getStorageSync("cardObj").crowdType=='5') {
				this.measurelist()
			} else{
				getByPlanId(options.id).then(res =>{
					if (res.data) {
						this.formData = res.data;
						this.formData.measureDate = this.$common.parseTime(res.data.measureTime, '{y}-{m}-{d}');
						this.submitShow = false;
					}
				})
			}

		},
		methods: {
			getConfig(){//获取字典1-腰围,2-腹围,3-髫围,4-腰鹮比,5-PBF,6-内脏脂肪指数，7治疗方案
				getWeightDataItemConfig().then(res=>{
					this.weightConfig = res.data
				})
			},
			measurelist() {
				measurelist({
					patientId: uni.getStorageSync("cardObj") ? uni.getStorageSync("cardObj").patientId : ''
				}).then(res => {
					this.latestheight = res.rows.length > 0 ? res.rows[0].height : 0;
					res.rows.findIndex((item) => {
						let itemDate = this.$common.parseTime(item.measureDate, '{y}-{m}-{d}')
						if (itemDate == this.formData.measureDate) {
							this.formData.height = item.height
							this.formData.weight = item.weight
						}
					})
				})

			},
			// 监听输入框输入事件
			inputting: function(e) {},
			confirm4(res) {
				const birthday = new Date(this.birthday)
				const measureDate = new Date(`${res[0]}-${res[1]}-${res[2]}`)
				if (measureDate < birthday) {
					this.$common.msg("记录日期不可小于生日")
					this.formData.measureDate = this.$common.parseTime(new Date(), '{y}-{m}-{d}')
				} else {
					this.formData.measureDate = `${res[0]}-${res[1]}-${res[2]}`
				}
				this.$forceUpdate()
			},
			// 表单提交
			submit: function() {
				if (!this.formData.height) { return this.$common.msg('请输入身高')}
				if (!this.formData.weight) { return this.$common.msg('请输入体重')}
				if (!this.formData.waistline && this.openType==3 && this.weightConfig.includes('1')) { return this.$common.msg('请输入腰围')}
				if (!this.formData.abdomen && this.openType==3&& this.weightConfig.includes('2')) { return this.$common.msg('请输入腹围')}
				if (!this.formData.hipline && this.openType==3&& this.weightConfig.includes('3')) { return this.$common.msg('请输入臀围')}
				if (!this.formData.waisHipRatio && this.openType==3&& this.weightConfig.includes('4')) { return this.$common.msg('请输入腰臀比')}
				if (!this.formData.pbf && this.openType==3&& this.weightConfig.includes('5')) { return this.$common.msg('请输入PBF')}
				if (!this.formData.visceralFatIndex && this.openType==3&& this.weightConfig.includes('6')) { return this.$common.msg('请输入内脏脂肪指数')}
				const formData = JSON.parse(JSON.stringify(this.formData))
				let timeData = this.$common.parseTime(new Date(), '{h}:{i}:{s}')
				formData.measureDate = `${formData.measureDate} ${timeData}`
				formData.createBy = uni.getStorageSync("cardObj").userName
				formData.patientId = uni.getStorageSync("cardObj").patientId
				// if(Number(formData.height)<this.latestheight){
				//  this.$common.msg("最新的身高记录不能小于上一次", "success")
				//  return
				// }

				measure(formData).then(res => {
					// this.$common.navTo('/pages/children/report')
					if (res.code == 200) {
						if (res.data != null) {
							this.$common.msg("最新的身高记录不能小于上一次 " + res.data.height + " 厘米")
							return
						}
						if (this.showtask) {
							//从打卡列表进来的添加
							addTaskPerform({
								...formData,
								measureTime:formData.measureDate,
								planId: this.id,
								taskType: this.taskType,
								patientId: uni.getStorageSync("cardObj") ? uni.getStorageSync(
									"cardObj").patientId : '', // 就诊卡用户id
								isRead: 1
							}).then(res => {
								this.$common.msg("提交成功", "success")
								setTimeout(() => {
									if (this.openType == 3) {
										return this.$common.navTab('/pages/task/index')
									}
									if (uni.getStorageSync("cardObj").crowdType=='5') {
										this.$common.navTab('/pages/index/index')
									} else{
										this.$common.navTab('/pages/task/index')
									}
								}, 1000)
							})
						} else {
							//从乐高首页进来的添加
							setTimeout(() => {
								this.$common.navTab('/pages/index/index')
							}, 1000)
						}
					}

				})
			},
			// onUnload() {
			// 	uni.reLaunch({
			// 		url: '/pages/index/index'
			// 	})
			// },
		}
	}
</script>
