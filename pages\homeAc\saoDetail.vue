<template>
	<view class="gui-padding">
		<view class="gui-comments">
			<view class="gui-flex gui-columns">
				<view class="gui-bold gui-flex"><view class="tuicons"></view>名称</view>
				<text class="gui-comments-header-text gui-text gui-primary-color marginlt">{{formList.name || '-'}}</text>
			</view>
			<view class="gui-comments-items gui-flex gui-columns">
				<view class="gui-bold gui-flex"><view class="tuicons"></view>特征</view>
				<text class="gui-comments-header-text gui-text gui-primary-color marginlt">{{formList.features || '-'}}</text>
			</view>
			<view class="gui-comments-items gui-flex gui-columns">
				<view class="gui-bold gui-flex"><view class="tuicons"></view>药性</view>
				<text class="gui-comments-header-text gui-text gui-primary-color marginlt">{{formList.drugProperties || '-'}}</text>
			</view>
			<view class="gui-comments-items gui-flex gui-columns">
				<view class="gui-bold gui-flex"><view class="tuicons"></view>功效</view>
				<text class="gui-comments-header-text gui-text gui-primary-color marginlt" >{{formList.effect || '-'}}</text>
			</view>
			<view class="gui-comments-items gui-flex gui-columns">
				<view class="gui-bold gui-flex"><view class="tuicons"></view>味</view>
				<text class="gui-comments-header-text gui-text gui-primary-color marginlt">{{formList.taste || '-'}}</text>
			</view>
			<view class="gui-comments-items gui-flex gui-columns">
				<view class="gui-bold gui-flex"><view class="tuicons"></view>描述</view>
				<text class="gui-comments-header-text gui-text gui-primary-color marginlt">{{formList.infoDesc || '-'}}</text>
			</view>
			<view class="gui-comments-items gui-flex gui-columns" v-if="formList.imgs">
				<view class="gui-bold gui-flex"><view class="tuicons"></view>图片</view>
				<view class="gui-comments-body marginlt">
					<view class="gui-comments-imgs gui-flex gui-rows gui-wrap">
						<view class="gui-comments-image" 
						v-for="(img, indexImg) in formList.imgs" :key="indexImg" 
						@click.stop="showImgs(index, indexImg)">
							<gui-image :src="img" :width="180"></gui-image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="">
			<gui-page-loading ref="guipageloading"></gui-page-loading>
		</view>
	</view>
</template>
<script>
	import {getGoods} from '@/api/home.js'
export default {
	data() {
		return {
			ID:'',
			formList:{
				imgs:[],
			}
		}
	},
	onShow() {
		this.$refs.guipageloading.open();
		this.ID = uni.getStorageSync('id');
		this.getlist();
	},
	onUnload() {
		uni.removeStorageSync('id')
	},
	methods:{
		getlist(){
			getGoods(this.ID).then(res=>{
				if (res.code==200) {
					this.formList = res.data
					var str = res.data.imgs
					this.formList.imgs = str.split(",")
					this.$refs.guipageloading.close();
				} else{
					return this.$common.msg('暂无数据')
				}
			})
		},
		showImgs : function(commentsIndex, imgIndex){
			uni.previewImage({
				urls:this.formList.imgs,
				current:this.formList.imgs[imgIndex]
			})
		}
	}
}
</script>
<style scoped>
	.tuicons{
		margin-right: 20rpx;
		width: 15rpx;
		height: 40rpx;
		background-color: #C59F79;
		border-radius: 10rpx;
	}
	.marginlt{
		margin-left: 20rpx;
		margin-top: 10rpx;
	}
</style>