<template>
	<gui-page ref="guipage" :isLoading="pageLoading"  :apiLoadingStatus="apiLoadingStatus" :refresh="true" @reload="reload"  :loadmore="true" @loadmorefun="getList"
	:refreshBgColor="['#ffffff','#ffffff','#ffffff','#c59f79']"
	statusBarStyle="background: linear-gradient(90deg,#ffffff,#ffffff);"
	headerStyle="background: linear-gradient(90deg,#c59f79,#c1c1c1);">
		<view slot="gBody" class="record gui-flex1" >
			<view class="record-tab pos-fixed bg-white" style="top: 0;left: 0;width: 100%;z-index: 99;">
				<view class="record-tab-item" @click="tabClick(item.id)" v-for="(item,index) in tablist" :key="item.id">
					<view :class="['record-tab-item-name',{'record-tab-item-name-i':tabindex == item.id}]">{{item.name}}
					</view>
				</view>
			</view>
			<view class="record-list mt-60" v-if="list.length > 0">
				<view class="gui-card-body" v-for="item in list" @click="details(item)">
					<view class="card-top">
						<view style="width: 460rpx;display: flex;">
							<text style="width: 120rpx;display: block;">订单号: </text>
							<text style="word-wrap:break-word; width: 340rpx;display: block;">{{item.serialNumber}}</text>
						</view>
						<view class="right" style="width: 120rpx; padding-left: 10rpx;">
							<text v-if="item.status == 1" class="card-top-name">待兑换</text>
							<text v-else class="card-top-name">已兑换</text>
							<!-- <image style="width: 30rpx;height: 30rpx;  transform:scaleX(-1);" src="@/static/img/leftarrow.png"></image> -->
						</view>
					</view>
					<view class="gui-flex gui-rows gui-nowrap gui-cate-product-list">
						<view class="gui-cate-pimg">
							<image class="gui-cate-pimg" :src="item.cover"></image>
						</view>
						<view class="gui-cate-pbody">
							<view class="gui-flex gui-rows gui-nowrap gui-space-between">
								<text class="gui-text gui-primary-color gui-block-text ellipsis-1">{{item.giftName}}</text>
								<text class="gui-cate-price" style="width: 120rpx; padding-left: 10rpx;">{{item.giftIntegral}}积分</text>
							</view>
							<view class="gui-flex gui-rows gui-nowrap gui-space-between">
								<text class="gui-cate-price gui-color-gray"></text>
								<text class="gui-cate-price gui-color-gray">x{{item.exchangeNumber}}</text>
							</view>
						</view>
					</view>
					<!-- <view class="num">
						<view>{{item.giftName}}</view>
						<view>{{item.giftIntegral}}积分</view>
					</view> -->
					<view class="num">
						<view>实付积分</view>
						<view class="gui-cate-price gui-color-red" style="font-size: 38rpx;">{{item.integral}}积分</view>
					</view>
					<view class="btn">
						<view class="btn-item" @click.stop="codeClick(item.writeOff)">核销码</view>
					</view>
				</view>
			</view>
			<view v-if="list.length <= 0">
				<gui-empty>
					<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
						<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
					</view>
					<text slot="text" 
					class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
				</gui-empty>
			</view>
			<gui-popup ref="guipopup1">
				<view class="gui-relative gui-box-shadow gui-bg-white demo-lr">
					<view>核销码</view>
					<view class="code gui-color-red">{{writeOff}}</view>
				</view>
			</gui-popup>
		</view>
	</gui-page>
</template>

<script>
	import { giftOrderList } from '@/api/integral.js'
	import guiPopup from '@/GraceUI5/components/gui-popup.vue'
	export default {
		components:{guiPopup},
		data() {
			return {
				tablist: [{
					id: 3,
					name: '全部'
				}, {
					id: 1,
					name: '待兑换'
				}, {
					id: 0,
					name: '已兑换'
				}],
				tabindex: 3,
				list: [],
				writeOff:0,//核销码
				pageSize:10,//分页大小
				pageNum:1,//当前页数
				pageLoading: true,
				apiLoadingStatus : false,// 用于记录是否有 api 请求正在执行
				// img: "https://images.unsplash.com/photo-1660505465468-c898ea7ff674?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHx0b3BpYy1mZWVkfDQ2fHhqUFI0aGxrQkdBfHxlbnwwfHx8fA%3D%3D&auto=format&fit=crop&w=200&q=90",
			}
		},
		onLoad(e) {
			if (e.status) {
				this.tabindex = Number(e.status)
				this.pageNum = 1
			}
		},
		onShow() {
			this.getList()
		},
		methods: {
			reload() {
				//下拉刷新
				this.pageNum = 1;
				this.getList(this.tabindex,true);
			},
			getList(e,isReload){
				var requestData = {
					userId:uni.getStorageSync('cardObj').patientId, //用户id
					// userName:uni.getStorageSync('cardObj').userName, //用户名称
					status:e?e:this.tabindex,//兑换状态
					pageSize:this.pageSize,
					pageNum:this.pageNum,
				}
				this.apiLoadingStatus = true;
				// isReload 函数用于识别是不是下拉刷新执行的
				giftOrderList(requestData).then(res =>{
					var zongPage = Math.ceil(Number(res.total / this.pageSize))
					if(this.pageNum >= 2){
						this.list = this.list.concat(res.rows)
						// 加载完成后停止加载动画
						this.$refs.guipage.stoploadmore();
						// 假定第3页加载了全部数据，通知组件不再加载更多
						// 实际开发由接口返回值来决定
						if(this.pageNum >= zongPage){
							this.$refs.guipage.nomore();
							return
						}
					}
					// 第一页 有可能是第一次加载或者刷新
					else{
						// this.list  = [];
						this.list = res.rows;
						this.pageLoading = false;
						// 刷新
						if(isReload){this.$refs.guipage.endReload();}
					}
					this.pageNum++;
					this.apiLoadingStatus = false;
				})
			},
			tabClick(index) {
				this.pageNum = 1
				this.tabindex = index
				this.getList(this.tabindex)
			},
			codeClick(e) {
				if (e) {
					this.writeOff = e
					this.$refs.guipopup1.open();
				}
			},
			details(item){
				if (item) {
					this.$common.navTo("/pages/integral/record-details?item="+encodeURIComponent(JSON.stringify(item)))
				}
				// uni.navigateTo({
				//      url: './record-details'
				// })
			}
		}
	}
</script>

<style scoped lang="scss">
	.demo-close {
		width: 100rpx;
		height: 100rpx;
		line-height: 100rpx;
		opacity: 0.88;
		text-align: center;
		font-size: 58rpx;
	}

	.demo-lr {
		flex: 1;
		border-radius: 20rpx;
		padding: 20rpx;
		.code{
			padding-top: 20rpx;
			word-spacing:2;
			font-size: 80rpx;
			font-weight: bold;
			text-align: center;
			letter-spacing:10rpx
		}
	}

	.demo-lr-items {
		text-align: center;
		overflow: hidden;
	}

	.record {
		display: flex;
		flex-direction: column;

		.record-list {
			display: flex;
			flex-direction: column;
			padding: 40rpx;

			.gui-card-body {
				box-shadow: 6rpx 6rpx 20rpx #eee;
				padding: 40rpx;
				border-radius: 20rpx;
				margin-bottom: 30rpx;
				font-size: 30rpx;

				.btn {
					flex: 1;
					display: flex;
					flex-direction: row;
					justify-content: flex-end;
					margin-top: 20rpx;

					.btn-item {
						border-radius: 70rpx;
						border: 2rpx solid #C59F79;
						padding: 10rpx 30rpx;
						color: #C59F79;
					}
				}

				.num {
					text-align: right;
					display: flex;
					flex-direction: row;
					padding-top: 20rpx;
					font-size: 30rpx;
					align-items: center;
					justify-content: space-between;
				}

				.gui-cate-product-list {
					margin-top: 40rpx;

					.gui-cate-pimg {
						width: 150rpx;
						height: 150rpx;
						border-radius: 20rpx;
					}

					.gui-cate-pbody {
						margin-left: 30rpx;
						display: flex;
						flex: 1;
						flex-direction: column;
						justify-content: space-between;
						.gui-text{
							font-size: 30rpx;
						}
						.gui-cate-price{
							font-size: 30rpx;
						}
						.gui-space-between {
							align-items: center;
						}
					}
				}

				.card-top {
					display: flex;
					flex-direction: row;
					justify-content: space-between;
					.right{
						display: flex;
						flex-direction: row;
						align-items: center;
					}
					// align-items: center;

					.shop-row {}

					.gui-icons {
						line-height: 40rpx;
					}

					.card-top-name {
						margin-right: 20rpx;
					}
				}
			}
		}

		.record-tab {
			display: flex;
			flex-direction: row;
			justify-content: space-between;

			.record-tab-item {
				text-align: center;
				flex: 1;

				.record-tab-item-name-i {
					color: #C59F79;
					border-bottom: 2rpx solid #C59F79 !important;
				}

				.record-tab-item-name {
					padding: 20rpx 0;
					border-bottom: 2rpx solid #fff;
				}
			}
		}
	}
</style>
