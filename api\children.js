import Http from "../net/Http.js"
import noHttp from "../net/noHttp.js"

// 添加身高评估档案
export function assessment(data){
	return Http.get('/wx/assessment/'+data)
}
export function assessmentPost(data){
	return Http.post('/wx/assessment',data)
}
// 获取儿童编辑2
export function measureUpdate(data){
	return Http.post('/wx/measure/update',data)
}
// 删除历史身高记录
export function measureDel(data){
	return Http.get('/wx/measure/del/'+data)
}
// 获取儿童详情2
export function measureinfo(data){
	return Http.get('/wx/measure/'+data)
}
// 获取儿童身高列表
export function measurelist(data){
	return Http.get('/wx/measure/list',data)
}
// 添加身高2
export function measure(data){
	return Http.post('/wx/measure',data)
}
// 评估档案2
export function getAssessment(data){
	return Http.get('/wx/assessment/getAssessment/'+data)
}
// 儿童身高折线图
export function getHeightList(data){
	return Http.get('/wx/measure/getHeightList',data)
}
// 获取儿童身高标准
export function getHeightStandardList(data){
	return Http.get('/wx/assessment/getHeightStandardList/'+data)
}
// 添加血糖记录
export function addBloodGlucoseRecord(data){
	return Http.post('/wx/zwb/iot/add/bloodGlucoseRecord',data)
}
// 获取血糖记录 GET
export function getBloodGlucoseRecord(data){
	return Http.get('/wx/zwb/iot/get/bloodGlucoseRecord',data)
}
// 获取血糖记录预警信息
export function getBloodGlucoseMonitorInfo(data){
	return Http.get('/wx/zwb/iot/get/bloodGlucoseMonitorInfo',data)
}