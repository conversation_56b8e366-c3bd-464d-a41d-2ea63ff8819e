import Http from "../net/Http.js"
import noHttp from "../net/noHttp.js"

// 任务列表
export function taskList(data){
	return Http.get('/wx/task/taskList',data)
}
// 任务详情
export function getFollowTaskInfo(data){
	return Http.get('/wx/task/getFollowTaskInfo',data)
}
// 问卷提交
export function submitQuestionnaire(data){
	return Http.post('/wx/task/submitQuestionnaire',data)
}
// 康养回复
export function submitGuidMonitor(data){
	return Http.post('/wx/task/submitGuidMonitor',data)
}
// 膏方回复
export function submitPlasterMonitor(data){
	return Http.post('/wx/task/submitPlasterMonitor',data)
}
// 健康监测
export function addTaskPerform(data){
	return Http.post('/wx/perform/addTaskPerform',data)
}
// 健康监测-身高体重的数据查询
export function getByPlanId(data){
	return Http.get('/wx/perform/getByPlanId/'+data)
}
// 宣教回复
export function readFollowTaskAirtcle(data){
	return Http.post('/wx/task/readFollowTaskAirtcle',data)
}
// 文章积分 status  1为转发  其他状态为空
export function getArticle(data){
	return Http.get('/wx/task/article',data)
}
// 文章详情
export function taskgetArticle1(data){
	return noHttp.get('/wx/task/info/getArticle',data)
}
export function taskgetArticletoken(data){
	return Http.get('/wx/task/info/getArticle1',data)
}
//打卡已完成的数据
export function healthData(data){
	return Http.get('/wx/monitor/health/data',data)
}
//打卡-时间轴状态显示（待执行）
export function getTaskDateStatus(data){
	return Http.get('/wx/task/getTaskDateStatus',data)
}

//打卡-已完成-获取身高体重监测任务执行记录
export function getWeightByPatientId(data){
	return Http.get('/wx/perform/getWeightByPatientId/'+data)
}

//打卡-减重管理-获取体重管理数据项显示配置
export function getWeightDataItemConfig(data){
	return Http.get('/wx/perform/getWeightDataItemConfig',data)
}
