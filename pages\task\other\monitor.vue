<template>
	<view class="gui-padding">
		<view class="gui-margin-top-large"></view>
		<view v-if="formData.whetherPerform === -1">
			<view style="padding:50rpx 0;">
				<text slot="text" class="gui-block-text gui-text-center gui-margin-top text-zhuti">此内容已经被撤回...</text>
			</view>
		</view>
		<view v-else>
			<form>
				<view class="gui-form-item gui-border-b" v-if="key === 1">
					<text class="gui-form-label">收缩压：</text>
					<view class="gui-form-body">
						<input type="digit" class="gui-form-input" :disabled="status != 0"
							v-model="formData.systolicPressure" name="systolicPressure" placeholder="请输入有效数字" />
					</view>
					<text class="gui-form-label">mmHg</text>
				</view>
				<view class="gui-form-item gui-border-b" v-if="key === 1">
					<text class="gui-form-label">舒张压：</text>
					<view class="gui-form-body">
						<input type="digit" class="gui-form-input" :disabled="status != 0"
							v-model="formData.diastolicPressure" name="diastolicPressure" placeholder="请输入有效数字" />
					</view>
					<text class="gui-form-label">mmHg</text>
				</view>
				<view class="gui-form-item gui-border-b" v-if="key === 2">
					<text class="gui-form-label">脉搏：</text>
					<view class="gui-form-body">
						<input type="digit" class="gui-form-input" :disabled="status != 0" v-model="formData.pulse"
							name="pulse" placeholder="请输入有效数字" />
					</view>
					<text class="gui-form-label">次/分</text>
				</view>
				<view class="gui-form-item gui-border-b" v-if="key === 3">
					<text class="gui-form-label">血糖：</text>
					<view class="gui-form-body">
						<input type="digit" class="gui-form-input" :disabled="status != 0"
							v-model="formData.randomGlucose" name="randomGlucose" placeholder="请输入有效数字" />
					</view>
					<text class="gui-form-label">mmol/L</text>
				</view>
				<view class="gui-form-item gui-border-b" v-if="key === 4">
					<text class="gui-form-label">血氧：</text>
					<view class="gui-form-body">
						<input type="digit" class="gui-form-input" :disabled="status != 0"
							v-model="formData.bloodOxygen" name="bloodOxygen" placeholder="请输入有效数字" />
					</view>
					<text class="gui-form-label">%</text>
				</view>
				<view class="gui-form-item gui-border-b" v-if="key === 5">
					<text class="gui-form-label">体温：</text>
					<view class="gui-form-body">
						<input type="digit" class="gui-form-input" :disabled="status != 0"
							v-model="formData.temperature" name="temperature" placeholder="请输入有效数字" />
					</view>
					<text class="gui-form-label">℃</text>
				</view>
				<view class="gui-form-item gui-border-b" v-if="key === 6">
					<text class="gui-form-label">呼吸：</text>
					<view class="gui-form-body">
						<input type="digit" class="gui-form-input" :disabled="status != 0" v-model="formData.breathing"
							name="breathing" placeholder="请输入有效数字" />
					</view>
					<text class="gui-form-label">次/分</text>
				</view>
				<text class="text-zhutis mt-80 d-iblock fs-22">提示：{{formData.remark || '-'}}</text>

				<view style="height: 180rpx;"></view>
				<view v-if="status==0"
					class="bg-white w-100 pos-fixed box-size-border box-shadows py-20 px-40 gui-border-radius-small"
					style="height: 130rpx; bottom: 0;left: 0;z-index: 9;">
					<view @click="save" class="bg-zhuti text-white w-100 h-100 d-flex ai-center jc-center">提交完成</view>
				</view>
			</form>
		</view>
	</view>
</template>
<script>
	import {
		getFollowTaskInfo,
		addTaskPerform
	} from '@/api/task.js'
	export default {
		data() {
			return {
				// 表单数据存储
				formData: {
					systolicPressure: '', //收缩
					diastolicPressure: '', //舒张
					pulse: '', //脉搏
					randomGlucose: '', //血糖
					bloodOxygen: '', //血氧
					temperature: '', //体温
					breathing: '', //体温
				},
				id: '',
				taskType: '',
				jcDate: [],
				status: '',
				key: 0 //1血氧2脉搏3血糖4血氧5体温6呼吸
			}
		},
		onLoad(options) {
			this.id = options.id;
			this.key = options.key;
			this.taskType = options.taskType;
			this.jcDate = uni.getStorageSync('jcData');
			uni.removeStorageSync('jcData');
			this.getDetail();
		},
		methods: {
			save() {
				switch (this.key) {
					case 1:
						if (!this.formData.systolicPressure || !this.formData.diastolicPressure) {
							return this.$common.msg("请完整填写血压内容")
						}
						break;
					case 2:
						if (!this.formData.pulse) {
							return this.$common.msg("请完整填写脉搏内容")
						}
						break;
					case 3:
						if (!this.formData.randomGlucose) {
							return this.$common.msg("请完整填写血糖内容")
						}
						break;
					case 4:
						if (!this.formData.bloodOxygen) {
							return this.$common.msg("请完整填写血氧内容")
						}
						break;
					case 5:
						if (!this.formData.temperature) {
							return this.$common.msg("请完整填写体温内容")
						}
						break;
					case 6:
						if (!this.formData.breathing) {
							return this.$common.msg("请完整填写呼吸内容")
						}
						break;
				}
				addTaskPerform({
					...this.formData,
					planId: this.id,
					taskType: this.taskType,
					patientId: uni.getStorageSync('cardObj').patientId, // 就诊卡用户id
				}).then(res => {
					this.$common.msg("提交成功", "success")
					setTimeout(() => {
						this.$common.navTab('/pages/task/index')
						// this.getDetail();
					}, 1000)
				})

			},

			getDetail() {
				getFollowTaskInfo({
					id: this.id,
					taskType: this.taskType
				}).then(res => {
					this.status = res.data.monitorInfo.whetherPerform
					this.formData = res.data.monitorInfo
				})
			},

		}
	}
</script>
<style scoped>
	.gui-text-small {
		line-height: 50rpx;
	}
</style>
