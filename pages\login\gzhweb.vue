<template>
	<view class="list-wrap">
		<web-view @message="handleMessage" @onPostMessage="handleMessage" :src="url"></web-view>
	</view>

</template>

<script>
	import md5 from '@/tools/utils/md5.js'
    export default {
        data() {
            return {
				url:'',
            }
        },
      onShow() {
		let md5Str = md5.hex_md5('n9IY8ddOl5Vaf4MKBL3sha'+ this.$common.parseTime(new Date, '{y}{m}{d}'))
		// console.log('key==',md5Str);
		let openSrc = this.$common.webSrc //正式环境,公众号授权，慢病小程序对接的是和湛的页面及接口
		console.log('授权h5：', openSrc)
		this.url = 'https://zy1fy4.healthan.net/Ask/GetOpenid/getOpen?key='+md5Str+'&from=manbin&redirect_uri='+encodeURIComponent(openSrc)
		console.log('授权页面：' + this.url)
      },
	  onLoad(){
	  	var self=this;
	  	if(!self.isEmpty(uni.getStorageSync("user"))){
	  		self.user=uni.getStorageSync("user");
	  	}
	  },
      methods:{
        handleMessage(e) {
			  var self=this;
			  let obj = e.detail.data[e.detail.data.length - 1];
			  let user = uni.getStorageSync("user")
			  user.wxOpenId = '1'
			  uni.setStorageSync("user",user)
			  console.log('接收到的jscode消息：' + JSON.stringify(obj));
			  console.log('接收到的openId==：', obj.wxopenId,obj.openId);
			  let openId = obj.wxopenId?obj.wxopenId:obj.openId
			  if (openId) {
				  self.$common.RequestDataNo({
				  	url:self.$common.updateWxOpenId,
				  	data:{
				  			wxOpenId:openId,
				  			memberId:self.user.id
				  		},
				  	}, res => {
				  		if (res.code == 200) {
				  			uni.setStorageSync("user",res.data);
				  		}
				  		
				  	})
			  } else{
			  	self.$common.RequestDataNo({
			  		url:self.$common.getWxOpenId,
			  		data:{
			  				jsCode:obj.jscode,
			  				memberId:self.user.id
			  			},
			  		}, res => {
						if (res.code == 200) {
							uni.setStorageSync("user",res.data);
						}
			  			
			  		})
			  }
          }
        }
    }
</script>

<style>

</style>
