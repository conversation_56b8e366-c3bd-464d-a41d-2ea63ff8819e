 // 测试环境
 export default function cofing(e){
	var domain
	var socketUrl
	var tmplIds
	var tmplIdss
	var tmplIdsss
	var wxAppId
	var gzhName //微信公众号名称
	var gzhImg //微信公众号图片
	var webSrc
	var appName //医院名称（就诊卡显示的名字）
	//广中医
	if(e == 1){
		appName = '广西中医药大学第一附属医院';
		gzhName = '广西星铭科技';
		gzhImg = 'http://qiniu.starup.net.cn/img/xmjk/%E6%98%9F%E9%93%AD.jpg';
		wxAppId = '';//wxa6f1df0a89310400星铭健康-需要公众号授权时才填入对应的AppId，否则留空
		webSrc = 'https://pay.frp.starup.net.cn/kcxx/pages/login/webview?type=100'; //公众号授权H5地址，只有测试环境添加运行路径，正式环境不用
		domain = 'https://zwb.frp.starup.net.cn/prod-api';//公司外网地址
		socketUrl = 'wss://zwb.frp.starup.net.cn/ws';     //公司环境socket
		tmplIds = [
			'pYQgSUslxmnUVYHp-kTlH_nfjI5HM1IwPj8DVoAz-lY',//未读消息
			'jdqVs3V7-NvaVSpx3rkwkV7Jm-zDbJimrEFRQ7HP5_8',//宣教-星铭健康
			'VIQ3sdAxBkz0LSTXLxttmBkc4xlGfiL2HScxBUVj_HM'//复诊
			// 'SD1uFgAVCiHNg7DAym2f8MT5UScvL3KNk5rI_O6MRlc',//宣教-互联医通
			// 'jRYhyPl4M_DMPpaXkqit_fntNOVJfcZM1EkdZB1t8Q0',//问题反馈
			// 'WeDMFYkT6YMkpLuhvhDgh6pIwqtXYKB7kZ8c2jxqWo4'//复诊
		] ;
		tmplIdss = [
			'pYQgSUslxmnUVYHp-kTlH_nfjI5HM1IwPj8DVoAz-lY',//未读消息
			'jdqVs3V7-NvaVSpx3rkwkV7Jm-zDbJimrEFRQ7HP5_8',//宣教-星铭健康
			'VIQ3sdAxBkz0LSTXLxttmBkc4xlGfiL2HScxBUVj_HM'//复诊
		];
		tmplIdsss = ['50Vwsc7qMTBVmuovMAtmmewK4iM7iQ5KLPzQ1itic4U']; //活动报名
	}else if(e == 2){
		// 贵阳
		appName = '贵阳市中医名医会馆';
		gzhName = '贵阳市中医名医会馆';
		wxAppId = '';//wxa6f1df0a89310400星铭健康
		gzhImg = 'https://img.starup.net.cn/xmkj/zwb/2023/09/01/a501cd7094e5422b878d999f26204ae3.jpg';
		webSrc = 'https://pay.frp.starup.net.cn/auth_h5'; //公众号授权H5地址
		domain = 'https://zwb.frp.starup.net.cn/prod-api';//公司外网地址
		socketUrl = 'wss://zwb.frp.starup.net.cn/ws';     //公司环境socket
		tmplIds = [
			'SD1uFgAVCiHNg7DAym2f8MT5UScvL3KNk5rI_O6MRlc',//宣教
			'jRYhyPl4M_DMPpaXkqit_fntNOVJfcZM1EkdZB1t8Q0',//问题反馈
			'WeDMFYkT6YMkpLuhvhDgh6pIwqtXYKB7kZ8c2jxqWo4'//复诊
		]; //公司小红船
		tmplIdss = ['jRYhyPl4M_DMPpaXkqit_fntNOVJfcZM1EkdZB1t8Q0'];//健康指导提醒
		tmplIdsss = ['WeDMFYkT6YMkpLuhvhDgh6pIwqtXYKB7kZ8c2jxqWo4']; //问卷填写通知
	}else if(e == 3){
		// 灵山
		appName = '灵山县中医医院';
		gzhName = '灵山县中医医院治未病科';
		wxAppId = '';
		gzhImg = 'https://img.starup.net.cn/xmkj/zwb/2023/09/01/78509be522a14db2b86928a9bf09d553.jpg';
		domain = 'https://lszwb.frp.starup.net.cn/prod-api/';//公司外网地址
		socketUrl = 'wss://lszwb.frp.starup.net.cn/ws';     //公司环境socket
		tmplIds = [
			// 'Yju3w2JgPlOpboLsDMKPVL6svnvu7eGoT5G4KyfS6Pw',//未读消息
			'Yju3w2JgPlOpboLsDMKPVL6svnvu7eGoT5G4KyfS6Pw',//监测逾期提醒
			'o9Ao2kwxRS6cjTv-_Z70bFtFAuOtA4qfBhYVL1a6QZ8'//复诊
		] ;
		tmplIdss = [
			'CGhiyFHdXQz57DVYsn1x_Tmwjnbi0sgpPs3D7_hgdS8', //健康指导提醒-
			'MSZCOjC-e5tJWODdOP05i38YTrDbPKUZyJmIKwNDmok',//问卷填写通知
			'92ofXSfYlNIi1bp3A62v0KnEIj2I1G546_KJuzmSztU'//宣教任务
		];
		tmplIdsss = ['o9Ao2kwxRS6cjTv-_Z70bFtFAuOtA4qfBhYVL1a6QZ8']; //问卷填写通知
	}else if(e == 4){
		appName = '南宁市中医医院';
		gzhName = '广西星铭科技';
		gzhImg = 'http://qiniu.starup.net.cn/img/xmjk/%E6%98%9F%E9%93%AD.jpg';
		wxAppId = '';//wxa6f1df0a89310400//星铭健康-需要微信公众号授权时才需要填入wxAppId，否则留空
		webSrc = 'https://pay.frp.starup.net.cn/kcxx/pages/login/webview?type=100'; //公众号授权H5地址，只有测试环境添加运行路径，正式环境不用
		domain = 'https://zwb.frp.starup.net.cn/prod-api';//公司外网地址
		socketUrl = 'wss://zwb.frp.starup.net.cn/ws';     //公司环境socket
		tmplIds = [
			'pYQgSUslxmnUVYHp-kTlH_nfjI5HM1IwPj8DVoAz-lY',//未读消息
			'jdqVs3V7-NvaVSpx3rkwkV7Jm-zDbJimrEFRQ7HP5_8',//宣教-星铭健康
			'VIQ3sdAxBkz0LSTXLxttmBkc4xlGfiL2HScxBUVj_HM'//复诊
			// 'SD1uFgAVCiHNg7DAym2f8MT5UScvL3KNk5rI_O6MRlc',//宣教-互联医通
			// 'jRYhyPl4M_DMPpaXkqit_fntNOVJfcZM1EkdZB1t8Q0',//问题反馈
			// 'WeDMFYkT6YMkpLuhvhDgh6pIwqtXYKB7kZ8c2jxqWo4'//复诊
		] ;
		tmplIdss = [
			'pYQgSUslxmnUVYHp-kTlH_nfjI5HM1IwPj8DVoAz-lY',//未读消息
			'jdqVs3V7-NvaVSpx3rkwkV7Jm-zDbJimrEFRQ7HP5_8',//宣教-星铭健康
			'VIQ3sdAxBkz0LSTXLxttmBkc4xlGfiL2HScxBUVj_HM'//复诊
		];
		tmplIdsss = ['50Vwsc7qMTBVmuovMAtmmewK4iM7iQ5KLPzQ1itic4U']; //活动报名
	}else if(e == 5){
		appName = '南宁市第七人民医院';
		gzhName = '广西星铭科技';
		gzhImg = 'http://qiniu.starup.net.cn/img/xmjk/%E6%98%9F%E9%93%AD.jpg';
		wxAppId = '';//wxa6f1df0a89310400//星铭健康-需要微信公众号授权时才需要填入wxAppId，否则留空
		webSrc = ''; //公众号授权H5地址，只有测试环境添加运行路径，正式环境不用https://pay.frp.starup.net.cn/kcxx/pages/login/webview?type=100
		domain = 'https://sztest.frp.starup.net.cn/prod-api';//舌诊公司测试地址
		socketUrl = 'wss://sztest.frp.starup.net.cn/ws';     //舌诊公司测试环境socket
		tmplIds = [
			'pYQgSUslxmnUVYHp-kTlH_nfjI5HM1IwPj8DVoAz-lY',//未读消息
			'jdqVs3V7-NvaVSpx3rkwkV7Jm-zDbJimrEFRQ7HP5_8',//宣教-星铭健康
			'VIQ3sdAxBkz0LSTXLxttmBkc4xlGfiL2HScxBUVj_HM'//复诊
			// 'SD1uFgAVCiHNg7DAym2f8MT5UScvL3KNk5rI_O6MRlc',//宣教-互联医通
			// 'jRYhyPl4M_DMPpaXkqit_fntNOVJfcZM1EkdZB1t8Q0',//问题反馈
			// 'WeDMFYkT6YMkpLuhvhDgh6pIwqtXYKB7kZ8c2jxqWo4'//复诊
		] ;
		tmplIdss = [
			'pYQgSUslxmnUVYHp-kTlH_nfjI5HM1IwPj8DVoAz-lY',//未读消息
			'jdqVs3V7-NvaVSpx3rkwkV7Jm-zDbJimrEFRQ7HP5_8',//宣教-星铭健康
			'VIQ3sdAxBkz0LSTXLxttmBkc4xlGfiL2HScxBUVj_HM'//复诊
		];
		tmplIdsss = ['50Vwsc7qMTBVmuovMAtmmewK4iM7iQ5KLPzQ1itic4U']; //活动报名
	}else if(e == 6){
		// 慢病
		appName = '广西中医药大学第一附属医院';
		gzhName = '广西中医药大学一附院智慧医院';
		gzhImg = 'http://oss.gzy.starup.net.cn/zyy/manbing/2023/12/08/********************************.png';
		wxAppId = ''; //wx5f126a213eaeaee1 // 需要微信公众号授权时才需要填入wxAppId，否则留空
		webSrc = 'https://fy.gzy.starup.net.cn/pages/login/webview?type=100'; //公众号授权H5地址
		// domain = 'https://mbgl.frp.starup.net.cn/prod-api';//公司外网地址
		//domain = 'http://192.168.1.71:8084/dev-api'
domain = 'http://192.168.1.71:8880'
		socketUrl = 'wss://mbgl.frp.starup.net.cn/ws';     //公司环境socket
		tmplIds = [
			// 'Yju3w2JgPlOpboLsDMKPVL6svnvu7eGoT5G4KyfS6Pw',//未读消息
			'Yju3w2JgPlOpboLsDMKPVL6svnvu7eGoT5G4KyfS6Pw',//监测逾期提醒
			'o9Ao2kwxRS6cjTv-_Z70bFtFAuOtA4qfBhYVL1a6QZ8'//复诊
		] ;
		tmplIdss = [
			'CGhiyFHdXQz57DVYsn1x_Tmwjnbi0sgpPs3D7_hgdS8', //健康指导提醒-
			'MSZCOjC-e5tJWODdOP05i38YTrDbPKUZyJmIKwNDmok',//问卷填写通知
			'92ofXSfYlNIi1bp3A62v0KnEIj2I1G546_KJuzmSztU'//宣教任务
		];
		tmplIdsss = ['o9Ao2kwxRS6cjTv-_Z70bFtFAuOtA4qfBhYVL1a6QZ8']; //问卷填写通知
	}
	return {
		domain,
		socketUrl,
		tmplIds,
		tmplIdss,
		tmplIdsss,
		wxAppId,
		gzhName,
		gzhImg, //微信公众号图片
		webSrc,
		appName
	}
}
