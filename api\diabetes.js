import Http from "../net/Http.js"
import noHttp from "../net/noHttp.js"

// 控糖库食物详情
export function sugarControlDetail(data){
	return Http.get('/wx/sugarControlDetail/'+data)
}
// 绑定关联设备
export function bindingDevice(data){
	return Http.post('/wx/zwb/iot/binding',data)
}
// 解除绑定设备
export function relieveDevice(data){
	return Http.post('/wx/zwb/iot/relieve',data)
}
//控糖食物列表
export function sugarControlList(data){
	return Http.get('/wx/sugarControlDetail/list',data)
}