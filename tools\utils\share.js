export default{
    // 监听用户点击右上角菜单的「转发」按钮时触发的事件
    onShareAppMessage() {
        // 设置转发的参数
        return {
            title: "标题",
            // path: '',
            imageUrl: "",
            success: function(res) {
                console.log(res, '发生过是');
                if (res.errMsg == 'shareAppMessage:ok') {
                    console.log("成功", res)
                }
            },
            fail: function(res) {
    
                console.log("失败", res)
    
            }
        }
    },
    // 分享到朋友圈
     onShareTimeline:function(res){
        return {
          title: '分享给朋友的标题',
          imageUrl:'/images/logo.png',
          query:''      
        }
      },
      // 收藏
      onAddToFavorites:function(res) {
        return {
          title: '微信收藏上的标题',
          imageUrl:'/images/logo.png',
          query: '',
        }
      }
}