<template>
	<view class="mt-60 px-40 fs-36">
		<view class="gui-flex gui-space-between">
			<view class="">
				姓名：{{detailData.name || '-'}}
			</view>
			<view class="">
				性别：{{detailData.gender==1?'男':detailData.gender==2?'女':'未知'}}
			</view>
			<view class="">
				年龄：{{detailData.age?(detailData.age+'岁'): '-'}}
			</view>
		</view>
		<view class="mt-40 ">
			<view class="gui-flex">
				<view class="">复诊科室：</view>
				<view class="gui-flex1">{{detailData.deptName || '-'}}</view>
			</view>
			<view class="gui-flex mt-20 gui-align-items-end gui-justify-content-end ">
				<view class="">复诊时间：</view>
				<view class="gui-flex1">{{detailData.info.visitTime || '-'}}</view>
			</view>
			<view class="gui-flex mt-20">
				<view class="">复诊嘱托：</view>
				<view class="gui-flex1">{{detailData.info.content || '-'}}</view>
				
			</view>
		</view>
	</view>
</template>

<script>
	import {visitBackId,backUpdate} from '@/api/other.js'
	export default{
		data(){
			return{
				detailData:{}
			}
		},
		onLoad(option) {
			this.getDetail(option.id)
		},
		methods:{
			getDetail(e){
				visitBackId(e?e:'').then(res =>{
					this.detailData = res.data
					if (res.data.info.readFlag=='1') {return}
					backUpdate({
						id:e,
						readFlag: '1',
					}).then(ress =>{
						
					})
					this.detailData.age = this.toAge(res.data.idCard)
				})
			},
			toAge(userCard) {
				//获取年龄
				var myDate = new Date();
				var month = myDate.getMonth() + 1;
				var day = myDate.getDate();
				var age = myDate.getFullYear() - userCard.substring(6, 10) - 1;
				if (userCard.substring(10, 12) < month || userCard.substring(10, 12) == month && userCard.substring(12, 14) <= day) {
					age++;
				}
				return age;
			},
		}
	}
</script>

<style>
</style>