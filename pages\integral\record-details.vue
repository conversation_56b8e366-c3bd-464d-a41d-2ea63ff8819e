<template>
	<gui-page :customHeader="true" :fullPage="true" statusBarStyle="background-color:#f6f6f6;" headerStyle="background-color:#f6f6f6;" pageBg="#f6f6f6">
		<!-- 自定义头部导航 -->
		<view slot="gHeader">
			<view class="gui-flex gui-nowrap gui-rows gui-align-items-center gui-padding">
				<!-- 使用组件实现返回按钮及返回首页按钮 -->
				<gui-header-leading :onlyBack="true"></gui-header-leading>
				<!-- 导航文本此处也可以是其他自定义内容 -->
				<view class="gui-flex1">
					<view v-if="productDta.status == 1" class="title gui-ellipsis gui-primary-color">待核销</view>
					<view v-else class="title gui-ellipsis gui-primary-color">已核销</view>
				</view>
				<!-- 此处加一个右侧展位元素与左侧同宽，实现标题居中 -->
				<!-- 实际宽度请根据自己情况设置 -->
				<view style="width:138rpx;"></view>
				<!-- 如果右侧有其他内容可以利用条件编译和定位来实现-->
			</view>
		</view>
		<!-- 页面主体 -->
		<view slot="gBody" class="record-details">
			<view class="record-details-i">
				<view class="gui-flex gui-rows gui-nowrap gui-cate-product-list">
					<view class="gui-cate-pimg">
						<image class="gui-cate-pimg" :src="productDta.cover"></image>
					</view>
					<view class="gui-cate-pbody">
						<view class="gui-flex gui-rows gui-nowrap gui-space-between">
							<text class="gui-text gui-primary-color gui-block-text ellipsis-1">{{productDta.giftName || ''}}</text>
							<text class="gui-cate-price" style="width: 120rpx; padding-left: 10rpx;">{{productDta.giftIntegral ||''}}积分</text>
						</view>
						<view class="gui-flex gui-rows gui-nowrap gui-space-between">
							<text class="gui-cate-price gui-color-gray"></text>
							<text class="gui-cate-price gui-color-gray">x{{productDta.exchangeNumber ||''}}</text>
						</view>
					</view>
				</view>
				<!-- <view class="num">
					<view>商品积分</view>
					<view>5积分</view>
				</view> -->
				<view class="num">
					<view>实付积分</view>
					<view class="gui-cate-price gui-color-red" style="font-size: 38rpx;">{{productDta.integral ||''}} 积分</view>
				</view>
				<view class="line"></view>
				<view class="num">
					<view>订单编号:</view>
					<view class="right-text">{{productDta.serialNumber ||''}}<!-- <text style="color: #000;margin-left: 10rpx;"> | 复制</text> --></view>
				</view>
				<view class="num">
					<view>创建时间:</view>
					<view class="right-text">{{productDta.createTime ||''}}</view>
				</view>
				<view class="num" v-if="productDta.updateTime">
					<view>兑换时间:</view>
					<view class="right-text">{{productDta.updateTime ||''}}</view>
				</view>
				<view class="line"></view>
				<view class="code">
					<view >核销码</view>
					<view class="code-num">{{productDta.writeOff ||''}}</view>
				</view>
			</view>
		</view>
	</gui-page>
</template>
<script>
export default {
	data() {
		return {
			productDta:[],
			img: "https://images.unsplash.com/photo-1660505465468-c898ea7ff674?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHx0b3BpYy1mZWVkfDQ2fHhqUFI0aGxrQkdBfHxlbnwwfHx8fA%3D%3D&auto=format&fit=crop&w=200&q=90",
		}
	},
	onLoad(e) {
		if (e.item) {
			this.productDta = JSON.parse(decodeURIComponent(e.item))
		}
		
	},
	methods: {}
}
</script>
<style lang="scss" scoped>
	.record-details{
		.record-details-i{
			padding: 40rpx;
			background-color: #fff;
			border-radius: 20rpx;
			margin: 20rpx;
			.code{
				font-size: 30rpx;
				.code-num{
					color: red;
					font-size: 80rpx;
					text-align: center;
					margin-top: 40rpx;
					letter-spacing:10rpx
				}
			}
			.line{
				height: 2rpx;
				background-color: #eee;
				margin:40rpx 0;
				width: auto;
			}
			.num {
				text-align: right;
				display: flex;
				flex-direction: row;
				font-size: 30rpx;
				padding-top: 20rpx;
				align-items: center;
				justify-content: space-between;
				.right-text{
					color: #999;
				}
			}
			.gui-cate-product-list {
			
				.gui-cate-pimg {
					width: 150rpx;
					height: 150rpx;
					border-radius: 20rpx;
				}
			
				.gui-cate-pbody {
					margin-left: 30rpx;
					display: flex;
					flex: 1;
					flex-direction: column;
					justify-content: space-between;
			
					.gui-space-between {
						align-items: center;
					}
				}
			}
		}
	}
	
	.title{
		display: flex;
		flex-direction: row;
		align-items: center;
		font-size: 40rpx;
		line-height: 60rpx;
		font-weight: bold;
		// text-align: left;
	}
</style>