<template>
	<view class="gui-padding">
		<view class="gui-margin-top-large"></view>
		<view v-if="formData.whetherPerform === -1">
			<view style="padding:50rpx 0;">
				<text slot="text"class="gui-block-text gui-text-center gui-margin-top text-zhuti" >此内容已经被撤回...</text>
			</view>
		</view>
		<view v-else>
			<form>
				<view class="gui-form-item gui-border-b">
					<text class="gui-form-label">呼吸：</text>
					<view class="gui-form-body">
						<input type="digit" class="gui-form-input" :disabled="status != 0" 
						v-model="formData.breathing" name="breathing" placeholder="请输入有效数字" />
					</view>
					<text class="gui-form-label">次/分</text>
				</view>
				<text class="text-zhutis mt-80 d-iblock fs-36">提示：{{formData.remark || '-'}}</text>
				
				<view style="height: 180rpx;"></view>
				<view v-if="status==0" class="bg-white w-100 pos-fixed box-size-border box-shadows py-20 px-40 gui-border-radius-small" style="height: 130rpx; bottom: 0;left: 0;z-index: 9;">
					<view @click="save" class="bg-zhuti text-white w-100 h-100 d-flex ai-center jc-center">提交完成</view>
				</view>
			</form>
		</view>
	</view>
</template>
<script>
	import {addTaskPerform,getFollowTaskInfo} from '@/api/task.js'
export default {
	data() {
		return {
			id:'',
			taskType:'',
			jcDate:[],
			status:'',
			// 表单数据存储
			formData : {
				breathing : '',
			},
		}
	},
	onLoad(options) {
		this.id = options.id;
		this.taskType = options.taskType;
		this.jcDate = uni.getStorageSync('jcData');
		uni.removeStorageSync('jcData');
		this.getDetail();
	},
	methods: {
		// 表单提交
		save(){
			if(!this.formData.breathing){return this.$common.msg("请完整填写呼吸内容")}
			addTaskPerform({
				...this.formData,
				planId:this.id,
				taskType:this.taskType,
				patientId:uni.getStorageSync('cardObj').patientId, // 就诊卡用户id
			}).then(res=>{
				this.$common.msg("提交成功","success")
				if (res.data == 1) {
					uni.showModal({
						title: '提示',
						content: '今日呼吸异常，请与主管医生联系',
						cancelText: "取消",
						confirmText: "确定",
						confirmColor: '#576B95',
						cancelColor: '#000000',
						success: res => {
							setTimeout(() => {
								this.$common.navTab('/pages/task/index')
							}, 1000)
						}
					})
				} else {
					this.$common.navTab('/pages/task/index')
				}
			})
		},
		
		getDetail(){
			getFollowTaskInfo({
				id:this.id,taskType:this.taskType
			}).then(res=>{
				this.status = res.data.monitorInfo.whetherPerform
				this.formData = res.data.monitorInfo
			})
		},
	}
}
</script>
<style scoped>
.gui-text-small{line-height:50rpx;}
>>>.gui-form-label{font-size: 36rpx;}
>>>.gui-form-input{font-size: 36rpx;}
</style>