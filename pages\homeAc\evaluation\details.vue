<template>
  <view class="" >
    <view class="gui-padding  pt-50">
    	<view class="mt-150 gui-flex gui-align-items-center gui-justify-content-center gui-columns">
    	  <view style="font-size: 100rpx;" class="gui-bold">{{sum}}</view>
    	  <view class="mt-20">总分</view>
    	</view>
		<view class="mt-80 fs-36 ml-40">
			<view class="mb-20 gui-bold">得分结果参考:</view>
			<view class="gui-flex gui-columns  ml-15" v-for="(item,index) in referData" :key="index">
				<view class="gui-flex">
					<text>{{index+1}}、</text>
					<text v-if="item.judgmentMode != 5">{{item.judgmentMode==1?'＞':item.judgmentMode==2?'＜':item.judgmentMode==3?'≤':item.judgmentMode==4?'≥':''}}</text>
					<text>{{item.standardVal}}，{{item.remark}}</text>
				</view>
			</view>
		</view>
    </view>
	<view class="user-box" v-show="false">
		<view class="px-40 pt-40 gui-flex gui-rows gui-align-items-center">
			<view class="" style="height: 90rpx;width: 170rpx; border-radius: 180rpx;">
				<image src="https://img.starup.net.cn/xmkj/zwb/img/yisheng.png" style="height: 100%;width: 100%;border-radius: 180rpx;" mode=""></image>
			</view>
			<view class="fs-30 ml-20 bg-zhuti gui-color-white p-20 gui-border-radius-large gui-bold">{{tipsContent}}</view>
		</view>
		<view class="mt-40 pt-20 gui-flex gui-justify-content-end pr-10 gui-rows gui-wrap" style="border-top: 2rpx solid #e1e1e1;">
			<view class="gui-flex" v-for="item in 4">
				<view class="text-zhuti mr-20 pb-10 px-15 fs-30 flex-1 mt-20" style="border-bottom: 2rpx solid #C59F79;" @click="navTocontent(item)">
					健康问卷
				</view>
			</view>
		</view>
	</view>
	<gui-page-loading ref="guipageloading">
		<text 
		class="gui-block-text gui-text-small gui-text-center gui-color-gray gui-italic" 
		style="padding-top:10rpx;">loading</text>
	</gui-page-loading>
  </view>
</template>

<script>
	var graceJS = require('@/GraceUI5/js/grace.js');
	import {getCriteria,getQuestSum} from '@/api/home.js'
export default {
  data() {
    return {
		tipsContent:'如果还有时间，建议根据自身情况完成以下问卷，让医生更全面了解您的情况。',
		sum:0,
		memberId:'',
		questionId:'',
		referData:[],
	}
  },
  onLoad(option) {
	  this.openLoading();
	  if (option) {
		  if (option.openType==1) {
		  	this.getSum(option)
		  } else{
	  		this.sum = option.sum?option.sum:0;
		  }
	  		this.memberId = option.memberId?option.memberId:uni.getStorageSync("user")?.id;
	  		//默认id是-广泛性焦虑障碍量表（GAD-7）
	  		this.questionId = option.questionId?option.questionId : 'a7f55cbd3031546fa475e028dcbf2018';
	  		this.getDetails(option)
	  }
  },
  methods: {
	  getSum(option){
		  getQuestSum({
				  patientId:option.patientId,
				  taskId:option.taskId?option.taskId:'',
				  questId:option.questionId?option.questionId:this.questionId,
		  }).then(res =>{
				this.sum = res.data
				this.$forceUpdate();
		  })
	  },
	  openLoading(){
	  	this.$refs.guipageloading.open();
	  },
	  getDetails(option){
	  		  getCriteria({
	  			  memberId:option.memberId?option.memberId:this.memberId,
	  			  questionId:option.questionId?option.questionId:this.questionId,
				  taskId:option.taskId?option.taskId:''
	  		  }).then(res =>{
				  this.$refs.guipageloading.close();
	  			  this.referData = res.data
	  			  this.$forceUpdate();
	  		  })
	  },
    navTocontent(e,typeId,item) {
		return
      this.$common.navCloseTo('/pages/homeAc/evaluation/content?type='+ e +'&questionId='+typeId+'&questionName='+this.questionName)
    }
  }
}
</script>

<style scoped>
	.user-box{
		position: absolute;bottom: 0; z-index: 99;width: 100%; height: 430rpx;
	}
</style>
