<template>
	<view>
		<!-- 加载历史消息 -->
		<view style="margin-top:10px; text-align:center;">
			<text class="grace-im-system-msg" @tap="getHistoryMsg">{{historyLodginText}}</text>
		</view>
		<!-- IM 消息展示区 -->
		<gui-im-message ref="imRef" :msgs="graceIMMsgs" :userid="friendUid" :group="group" @revokeText="revokeText"></gui-im-message>
		<!-- 介绍会话 -->
		<!-- <view v-if="types == 2" class="end">会话已结束</view> -->
		<view style="height: 130rpx;"></view>
		<!-- 底部提交区 -->
		<gui-im-input v-if="types != 2" @chooseImage="chooseImage" @sendText="sendText" @sendVoice="sendVoice"
			:receiver-uid="friendUid" :token="graceIMToken" :group="group" :orderId="orderId" :inputContent="inputContent"
			:adjustPosition="adjustPosition"></gui-im-input>
	</view>
</template>
<script>
	// 挂载 vuex
	import {
		mapState,
		mapMutations
	} from 'vuex';
	import {
		msgCount,
		msgReaded,
		sessionRecord,
		isReadMsgI
	} from '@/api/im.js'
	import url from "@/common.js"
	export default {
		data() {
			return {
				adjustPosition: true, //默认软键盘当上块内容区
				msgs: [],
				userInfo: uni.getStorageSync('user'),
				graceIMScTops: 9999,
				imUser: {
					userId: uni.getStorageSync('user').id, // 系统用户id
					userUid: '', //链接im返回的uuid
					userType: '2', // 用户类型  1：医生  2：患者
					uname: uni.getStorageSync('user').nickName, // 用户名
					nickName: uni.getStorageSync('user').nickName, // 昵称
					uface: uni.getStorageSync('user').avatar // 头像
				},
				friendUid: 0,
				group: '',
				page: 1,
				size: 10,
				historyLodginText: '点击加载历史消息',
				title: '',
				orderId: "",
				types: null,
				inputContent:'',//撤回的消息内容
			}
		},
		onLoad: function(options) {
			let imObj = JSON.parse(decodeURIComponent(options.imObj));
			this.group = imObj.groupIndex;
			this.orderId = imObj.orderId;
			this.types = imObj.types;
			this.friendUid = imObj.friendUid;
			this.imUser.userUid = imObj.userUid;
			uni.setNavigationBarTitle({
				title: imObj.friendName
			})
			this.getMsgReaded(); // 标记已读
		},
		onShow() {
			let openType = uni.getStorageSync("openIMconsult")
			if(this.graceIMMsgs && openType == 3){
				uni.removeStorageSync("openIMconsult")
				this.graceIMPage[this.group] = undefined;
				this.getMsgReaded(); // 标记已读
				this.historyLodginText = '点击加载历史消息';
			}
			this.$store.dispatch('graceIMConnect', this.imUser);
			setTimeout(() => {
				this.pageScroll();
			}, 500)

		},
		onUnload() {
			this.$refs['imRef'].playStop()
		},
		computed: {
			...mapState([
				'graceIMMsgsCH',
				'graceIMMsgs',
				'graceIMScTop',
				'graceIMHeight',
				'graceIMUIndex',
				'graceIMUID',
				'graceIMToken',
				'graceIMConfig',
				'graceIMPage',
				'graceIMStatus',
				'isReadMsg'
			])
		},
		watch: {
			graceIMScTop(n, o) {
				this.pageScroll(); //监控聊天内容变化滚动屏幕

			},
			graceIMMsgsCH(n,o){
				// 有消息撤回
				if(n.isRevoke == 1){
					console.log('有消息撤回msgId',n)
					console.log(this.graceIMMsgs,'xxxxxxxxx')
					var i = this.graceIMMsgs
					var arrindex=this.graceIMMsgs.findIndex((item)=>item.msgId==n.msgId);
					this.graceIMMsgs.splice(arrindex,1);
				}
			},
			graceIMMsgs() {
				console.log('w jinlai dshdauohd ')
				this.checkImMsgLength();
			},
			isReadMsg(n, o){
			}
		},
		methods: {
			...mapMutations(['clearGraceIMMsgs,closeSocket']),
			checkImMsgLength() {
				console.log("判断情况===========",this.graceIMMsgs)
				//聊天内容少于5条时 输入框输入是软键盘不顶掉上面内容模块保证最上面可见到。
				// 多于5条时顶上去看下放最新的
				if (this.graceIMMsgs.length < 5) {
					this.adjustPosition = false
				} else {
					this.adjustPosition = true
				}
				if (this.graceIMMsgs.length>0) {
					// this.graceIMMsgs.filter(e=>{
					// 	if (e.isRevoke == 1) {//撤回消息
					// 		console.log('撤回消息==',e)
					// 		uni.showToast({
					// 			title: '正在撤回',
					// 			icon: 'loading'
					// 		});
					// 		this.historyLodginText = '点击加载历史消息';
					// 		this.getHistoryMsg(3)
					// 	}
					// })
					if(this.friendUid == this.graceIMMsgs[this.graceIMMsgs.length-1].senderUid){
						console.log('我是已读未读，我不能进来的')
						isReadMsgI({
							receiverUid:uni.getStorageSync("userUid"),
							groupIndex:this.group,
							senderUid:this.friendUid
						})
					}
					this.$forceUpdate()
				}
			},
			revokeText(e){
				// this.$set(this,'inputContent',e.content)
			},
			// 标记已读
			getMsgReaded() {
				let that = this;
				if (uni.getStorageSync("user") && uni.getStorageSync("user").id) {
					msgReaded({
						senderUid: this.friendUid,
						receiverUid: uni.getStorageSync("userUid")
					}).then(res => {
						// this.getMsgCount();
						// console.log('清空聊天记录，重新加载')
						// 清空聊天记录，重新加载
						that.page = 1;
						that.msgs = [];
						isReadMsgI({
							receiverUid:uni.getStorageSync("userUid"),
							groupIndex:this.group,
							senderUid:this.friendUid
						}).then(res=>{
							that.getHistoryMsg();
						})
					})
				}
			},
			// 获取消息总数
			getMsgCount() {
				if (uni.getStorageSync("user") && uni.getStorageSync("user").id) {
					msgCount({
						userUId: uni.getStorageSync("userUid")
					}).then(res => {
						uni.setStorageSync("msgCount", res.data)
					})
				}
			},
			// 加载历史消息
			getHistoryMsg: function() {
				console.log('加载历史消息==')
				let that = this;
				if (this.historyLodginText != '点击加载历史消息') {
					return;
				}
				if (!this.graceIMPage[this.group]) {
					this.graceIMPage[this.group] = 1;
				}
				this.historyLodginText = '加载中 ...';
				sessionRecord({
					groupIndex: this.group,
					orderId: this.orderId,
					pageSize: this.size,
					pageNum: this.page
				}).then(res => {
					var msg = res.rows;
					if (that.page == 1) {
						that.$store.commit('clearGraceIMMsgs');
					}
					for (var i = 0; i < msg.length; i++) {
						msg[i].date = msg[i].date ? url.parseTime(new Date(msg[i].date.substr(0, 19).replace(
							/T/g, ' ').replace(/-/g, '/')), '{m}-{d} {h}:{i}') : "";
						this.graceIMMsgs.unshift(msg[i]);
					}
					if (this.graceIMMsgs.length >= res.total) {
						this.historyLodginText = '已经加载全部';
						return;
					}
					this.page++;
					this.historyLodginText = '点击加载历史消息';
					this.checkImMsgLength();
				})

			},

			back_up: function() {
				uni.navigateTo({
					url: "../../my/personal/comment-add"
				})
			},
			// 01 发送文本消息
			sendText: function(msg) {
				this.pageScroll();
			},
			// 02 选择图片
			chooseImage: function(img) {
				// this.msgs.push(msg);
				this.pageScroll();
			},
			// 03 发送语音消息
			sendVoice: function(voice, vlength) {
				// console.log(voice);
				// 录音后返回临时语音文件
				// 请提交给 IM 服务器上传，上传后会产生真实语音文件地址，
				// 具体逻辑视 IM 后端情况来实现
				// 然后 IM 服务返回消息数据，添加到消息列表中
				// 模拟IM 服务端返回消息
				var msg = {
					group: 'group1',
					uindex: '10000',
					uname: '老兵张嘎',
					contentType: 'voice',
					uface: 'https://cmsuse.oss-cn-beijing.aliyuncs.com/g5/13.png',
					content: voice,
					length: vlength,
					date: '刚刚'
				}
				this.msgs.push(msg);
				this.pageScroll();
			},
			// 滚动条滚动 [ 有新消息可以自动滚动到底部 ]
			pageScroll: function() {
				setTimeout(() => {
					uni.pageScrollTo({
						scrollTop: 999999 + Math.random(),
						duration: 100
					})
				}, 200);
			}
		},
	}
</script>
<style>
	.pendant {
		z-index: 9999;
		background-color: #F1F1F1;
		border-radius: 50 rpx;
		width: 120 rpx;
		height: 120 rpx;
		border-radius: 100 rpx;
		text-align: center;
		line-height: 120 rpx;
		font-size: 22 rpx;
		position: fixed;
		right: 20 rpx;
		bottom: 150 rpx;
	}

	page {
		background-color: #F7FBFE;
	}

	.end {
		text-align: center;
		margin-top: 120px;
		font-size: 12px;
		color: red;
		text-decoration: underline;
	}

	.grace-im-system-msg {
		background: #C1C1C1;
		color: #FFF;
		font-size: 12px;
		line-height: 1.5em;
		padding: 5px 10px;
		display: inline-block;
		border-radius: 3px;
	}
</style>
