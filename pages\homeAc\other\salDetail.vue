<template>
	<view class="gui-padding">
		<view v-if="templateDictKey == 100">
			<view v-if="articleList.content" v-html="articleList.content"></view>
			<!-- <mp-html v-if="articleList.content" :content="articleList.content" /> -->
			<view v-else class="gui-flex gui-columns gui-align-items-center mt-150 text-zhuti text-center">
				<view class="gui-color-white gui-bg-orange" style="font-size: 90rpx;width: 100rpx;height: 100rpx;line-height: 100rpx;border-radius: 100rpx;">!</view>
				<view class="mt-50">此内容已被移除</view>
			</view>
		</view>
		<view v-else>
			<!-- 治疗方案  -->
			<fagl v-show="templateDictKey == 0 && templateDictKey != 100" :userName="userName" :sex="sex" :patientId="patientId" :visitRecordId="visitRecordId" :templateDictKey="templateDictKey" :programmeTypeId="templateId" :programmeType="programmeType" :schemeTime="schemeTime" :createTimeFilter="createTimeFilter"></fagl>
			<!-- 中医4P饮食健康管理建议-饮食 -->
			<ysjy v-show="templateDictKey == 1 && templateDictKey != 100" :patientId="patientId"  :visitRecordId="visitRecordId" :templateDictKey="templateDictKey"></ysjy>
			<!-- 居民中医体质记录 -->
			<jmzy v-show="templateDictKey == 2 && templateDictKey != 100" :patientId="patientId"  :templateId="templateId" :templateDictKey="templateDictKey"></jmzy>
			<!-- 其他模板 -->
			<others :patientId="patientId"  v-show="templateDictKey != 0 && templateDictKey != 1 && templateDictKey != 2 && templateDictKey != 26 && templateDictKey != 29 && templateDictKey != 30 && templateDictKey != 31  && templateDictKey != 35 && templateDictKey != 36 && templateDictKey != 100" :templateId="templateId" :templateDictKey="templateDictKey"></others>
			<!-- 中医饮食指导（儿科一般指导） -->
			<rkyb :patientId="patientId"  v-show="(templateDictKey == 26 || templateDictKey == 29 || templateDictKey == 30) && templateDictKey != 100" :templateId="templateId" :visitRecordId="visitRecordId" :templateDictKey="templateDictKey"></rkyb>
			<!-- 运动减脂方案 -->
			<ydjz :patientId="patientId"  v-show="templateDictKey == 31" :templateId="templateId" :visitRecordId="visitRecordId" :templateDictKey="templateDictKey"></ydjz>
      <!-- 中医禁食疗法方案 -->
      <jslf :patientId="patientId"  v-show="templateDictKey == 35" :templateId="templateId" :visitRecordId="visitRecordId" :templateDictKey="templateDictKey"></jslf>
      <!-- 营养干预方案 - 已改为直接预览PDF，不再显示组件 -->
      <!-- <yygy :patientId="patientId"  v-show="templateDictKey == 36" :templateId="templateId" :visitRecordId="visitRecordId" :templateDictKey="templateDictKey"></yygy> -->
    </view>
		<view class="h50"></view>
	</view>
</template>

<script>
	import fagl from "../component/fagl.vue"
	import ysjy from "../component/ysjy.vue"
	import jmzy from "../component/jmzy.vue"
	import others from "../component/others.vue"
	import rkyb from '../component/rkyb.vue'
	import ydjz from '../component/ydjz.vue'
	import jslf from '../component/jslf.vue'
	import yygy from '../component/yygy.vue'
	import {programmeArticle} from "@/api/home.js"
	export default {
		components:{fagl,ysjy,jmzy,others,rkyb,ydjz,jslf,yygy},
		data() {
			return {
				articleList:{},//宣教方案详情
				templateDictKey: "",  // 模板id
				patientId:"",         // 患者id
				templateId:"",        // 病历详情id
				programmeType: "",        // 方案类型
				visitRecordId:"",        // 就诊记录id
				obj:{},
				schemeTime:"",
				userName:'',
				sex:'',
				createTimeFilter:""   // 创建时间过滤器
			}
		},
		onLoad(op) {
			this.userName = op.userName
			this.sex = op.sex
			this.templateDictKey = op.templateDictKey;  // 病历模板 key
			this.patientId = op.patientId;              // 患者id
			this.templateId = op.templateId;              //病历详情id
			this.visitRecordId = op.visitRecordId;        //就诊记录id
			this.schemeTime = op.createTime;        //方案下达时间
			this.programmeType = op.programmeType;        //方案类型
			this.createTimeFilter = op.createTimeFilter; //创建时间过滤器
			if(op.title){
				uni.setNavigationBarTitle({
					title:op.title
				})
			}
			if (this.templateDictKey == 100) {
				this.getDetail()
			}
			// 如果是营养干预方案(templateDictKey == 36)，直接跳转到PDF预览页面
			if (this.templateDictKey == 36) {
				this.previewNutritionPDF()
			}
		},
		methods: {
			getDetail(){
				programmeArticle({
					id: this.templateId,
				}).then(res=>{
					this.articleList = res.data
				})
			},
			// 预览营养干预方案PDF
			previewNutritionPDF() {
				if (!this.patientId || !this.visitRecordId) {
					uni.showToast({
						title: '缺少必要参数',
						icon: 'none'
					})
					return
				}

				// 显示加载提示
				uni.showLoading({
					title: '正在加载PDF...'
				})

				// 跳转到PDF预览页面
				this.$common.navTo(`/pages/homeAc/other/pdfPreview?patientId=${this.patientId}&visitId=${this.visitRecordId}`)

				// 延迟隐藏加载提示
				setTimeout(() => {
					uni.hideLoading()
				}, 1000)
			}
		}
	}
</script>

<style>

</style>
