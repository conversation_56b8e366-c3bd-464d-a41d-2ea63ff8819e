<template>
    <view>
        <web-view :webview-styles="webviewStyles" :src="url"></web-view>
    </view>
</template>

<script>
	import {getArticle,taskgetArticle1} from '@/api/task.js'
    export default {
        data() {
            return {
				model:{
					articleName:'',
					content:'',
					createTime:"",
					readNum:'',
					articleId:''
				},
				url:'',
                webviewStyles: {
                    progress: {
                        color: '#FF3333'
                    }
                }
            }
        },
		onShow() {
			// let detailId = uni.getStorageSync('detailId');
			// this.getIntegral(detailId);
			// uni.removeStorageSync('detailId')
			
		},
		onShareAppMessage: function( options ){
			this.getIntegral(1)
			// 设置菜单中的转发按钮触发转发事件时的转发内容
			var shareObj = {
		    title: this.model.articleName,    // 默认是小程序的名称(可以写slogan等)
		    path: '/pages/task/other/aboutus?articleId='+this.model.articleId,    // 默认是当前页面，必须是以‘/'开头的完整路径
		    imageUrl: this.model.filePath   //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
			}
			// 返回shareObj
			return shareObj;
		},
		//分享到朋友圈
		onShareTimeline(res) {
			this.getIntegral(1)
			var shareObj1 = {
				title: this.model.articleName,
				query: 'articleId='+this.model.articleId,
				// path: '/pages/science/detail?articleId='+this.model.articleId,
				imageUrl: this.model.filePath
			}
		    return shareObj1
		},
		onLoad(e) {
			this.model.articleId = e.articleId
			// this.url= uni.getStorageSync('aboutus');
			// uni.removeStorageSync('aboutus')
			wx.showShareMenu({
			    withShareTicket:true,
			    menus:["shareAppMessage","shareTimeline"]
			})
			this.taskgetArticle1()
		},
		methods:{
			// 文章详情
			taskgetArticle1(){
				let self = this
				taskgetArticle1({
					id:this.model.articleId,
				}).then(res=>{
					this.model = res.data
					this.model.articleId = res.data.id
					this.url = res.data.content.replace(/<[^>]+>/g, '')
					this.$common.isRegAndLogin(res => {
						if (res === 0) {
							//跳转授权注册页
							self.$common.noLoginBox();
						} else if (res > 0) {
							this.getIntegral()
						}
					})
				})
			},
			// 积分
			getIntegral(i){
				getArticle({
					id:this.model.articleId,
					patientId:uni.getStorageSync('cardObj').patientId,
					status:i == 1 ? i : ''
				}).then(res=>{
					// this.model.readNum = res.data.readNum
				})
			},
		}
    }
</script>

<style>

</style>