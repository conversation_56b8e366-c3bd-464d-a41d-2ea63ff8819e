<template>
	<view>
		<view class="gui-padding">
			<view class="gui-flex gui-rows">
				<textarea v-model="textareaVal" maxlength='300' class="gui-text-area" placeholder="快记录下此刻的健康...." />
			</view>
			<view class="gui-flex gui-space-around">
				<view @click="changeTasg(item)" style="background-color:#e8e9ea" class="px-40 py-5 b-radius-50 gui-flex gui-align-items-center" v-for="item in tasg">{{item}}</view>
			</view>
			<view class="gui-margin-top">
				<gui-upload-images :progress="false" imgSize="8000000" errorText="大于8M" :header="header" fileName="file"
					@change="change" :btnName="btnName" :maxFileNumber="6" ref="uploadimgcom" @uploaded="uploaded"
					:uploadServerUrl="uploadFuJianFile"></gui-upload-images>
			</view>
			<view class="gui-margin-top">
				<view class="gui-form-item gui-border-b">
					<view class="gui-form-label gui-color-gray">
						<image class="icon" src="@/static/date-i.svg"></image>
						<view>记录时间</view>
					</view>
					<view class="gui-flex gui-align-items-center" @click="showPop">
						<view class=" text-date" >{{defaultData.startTime}}</view>
						<text class="gui-icons ml-10" style="font-size: 25rpx;color:#999">&#xe601;</text>
					</view>
				</view>
			</view>
		</view>
		<view class="save-btn">
			<button class="save-btn-i" @click="saveClick" :disabled="disabled" :loading="disabled">保存</button>
		</view>
		<lingfeng-timepicker :safeArea="false" ref="timePop" type="date" :defaultData="defaultData"
			@change="confirm2"></lingfeng-timepicker>

	</view>
</template>
<script>
	import {
		diaryAdd,
		diary,
		diaryEdit
	} from '@/api/home.js'
	export default {
		data() {
			return {
				//快捷标签
				tasg:['#早餐#','#中餐#','#晚餐#','#喝水#'],
				defaultData:{
				    startTime:this.$common.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')
				},
				demo2Val: this.$common.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}'),
				// 编辑id
				id: '',
				uploadFuJianFile: this.$common.uploadFuJianFile,
				btnName: '',
				disabled: false,
				needPploadedImgs: [],
				// 文本框输入内容记录
				textareaVal: '',
				header: {
					"Content-Type": "application/json; charset=UTF-8",
					"Authorization": 'wx ' + uni.getStorageSync('token')
				},
			}
		},
		onLoad: function(e) {
			// 模拟 api 加载默认图片
			// 不需要默认值删除此函数即可

			if (e.id) {
				this.id = e.id
				this.diary()
			}
			if (e.itemlist) {
				this.init(JSON.parse(e.itemlist))
			}
			// setTimeout(()=>{
			// 	this.$refs.uploadimgcom.setItems(['https://img1.baidu.com/it/u=1960110688,1786190632&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=281']);
			// }, 1000);
		},
		methods: {
			changeTasg(item){//快捷标签
				this.textareaVal = this.textareaVal.concat(item)
			},
			showPop() {
				this.$refs.timePop.show();
			},
			// 详情
			diary() {
				diary(this.id).then(res => {
					this.textareaVal = res.data.content
					this.defaultData.startTime = res.data.recordTime
					// this.items = res.data.imgUrl.split(',')
					this.$refs.uploadimgcom.setItems(res.data.imgUrl.split(','))
					// var imgs = []
					// for(let i = 0; i < this.items.length; i++){
					// 	imgs.push({url:this.items[i],  progress : 100});
					// }
					// this.needPploadedImgs = imgs
				})
			},
			saveClick() {
				var that = this
				this.disabled = true
				if (this.needPploadedImgs.length == 0) {
					this.disabled = false
					return this.$common.msg('请上传图片');
				}
				// console.log(this.needPploadedImgs,'ttttttttttt')
				// for(let i=0;i<this.needPploadedImgs.length;i++){
				//     if( this.needPploadedImgs[i].progress !== 100){
				// 		this.disabled = false
				// 		uni.hideLoading();
				//         this.$common.msg('图片正在上传,请稍后再提交');
				//         return;
				//     }
				// }
				this.$refs.uploadimgcom.upload()
			},
			init(list) {
				if (list.length > 0) {
					// this.items = list
					this.$refs.uploadimgcom.setItemsNo(list)
					// this.$refs.uploadimgcom.setItems(list);
					// var imgs = []
					// for(let i = 0; i < list.length; i++){
					// 	imgs.push({url:list[i],  progress : 100});
					// }
					// this.needPploadedImgs = imgs
				}
			},
			confirm2(e) {
				console.log(e)
				this.defaultData.startTime = e
			},
			// 记录选择图片时的待提交数据
			change: function(e) {
				this.needPploadedImgs = e;
				// this.needPploadedImgs = e.map(item=>{return item.url})
			},
			// 提交动态
			// 过程说明 : 
			// 点击提交按钮 > 首先执行组件的上传函数 > 上传成功后获得已经上传的图片数据 > 提交给后端 api 记录整个内容 
			// 图片上传完毕执行 uploaded 函数
			uploaded: function(uploadedImgs) {
				var that = this
				uni.showLoading({
					title: "正在提交",
					mask: true
				});
				this.needPploadedImgs = uploadedImgs
				var list = uploadedImgs.map(item => {
					return item.url
				})
				var obj = {
					patientId: uni.getStorageSync('cardObj').patientId,
					content: this.textareaVal,
					imgUrl: list.join(','),
					recordTime: this.defaultData.startTime
				}
				if (this.id) {
					diaryEdit({
						...obj,
						id: this.id
					}).then(res => {
						uni.hideLoading();
						this.$common.msg('修改成功', "success");
						this.disabled = false
						setTimeout(function() {
							that.$common.navBack(1)
						}, 1000);
					}, fail => {
						this.disabled = false
						uni.hideLoading();
					})
					return
				}
				diaryAdd(obj).then(res => {
					uni.hideLoading();
					this.$common.msg('保存成功', "success");
					this.disabled = false
					setTimeout(function() {
						that.$common.navBack(1)
					}, 1000);
				}, fail => {
					this.disabled = false
					uni.hideLoading();
				})
			}
		}
	}
</script>
<style scoped>
	.gui-form-label {
		display: flex;
		flex-direction: row;
		align-items: center;
		flex: 1;
		font-size: 35rpx;
	}

	.icon {
		width: 50rpx;
		height: 50rpx;
		margin-right: 20rpx;
	}

	.save-btn {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		margin-top: 100rpx;
	}

	.save-btn-i {
		background-color: #C59F79;
		color: #fff;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 35rpx;
		width: 200rpx;
		height: 80rpx;
	}

	.demo-date {
		margin-right: 20rpx;
	}

	.text-date {
		text-align: right;
		flex: 1;
		;
		font-size: 35rpx;
	}

	.gui-text-area {
		font-size: 35rpx;
		color: #2B2E3D;
		flex: 1;
		height: 200rpx;
		padding: 20rpx;
	}
</style>
<style>

</style>