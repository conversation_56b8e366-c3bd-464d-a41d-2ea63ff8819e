
<template>
	<view class="gui-padding">
		<view class="gui-margin-top-large"></view>
		<form @submit="submit">
			<!-- 姓名 -->
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">姓名</text>
				<view class="gui-form-body">
					<input type="text" class="gui-form-input" 
						v-model="formData.userName" name="userName" placeholder="请输入姓名" />
				</view>
			</view>
			
			<!-- 身份证号 -->
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">身份证号</text>
				<view class="gui-form-body">
					<input type="text" class="gui-form-input" 
						v-model="formData.idCard" name="idCard" maxlength="18" placeholder="请输入证件号码" />
				</view>
			</view>
			
			<!-- 住址 -->
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">住址</text>
				<view class="gui-form-body">
					<picker class="gui-form-input" mode="region" @change="chooseDz">
						<view v-if="formData.addressDetail">{{formData.addressDetail}}</view>
						<view v-else class="text-grey-74">省-市-县/区</view>
					</picker>
				</view>
			</view>
			<!-- 详细地址 -->
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">详细地址</text>
				<view class="gui-form-body">
					<input type="text" class="gui-form-input" 
						v-model="formData.address" name="address" placeholder="请输入详细地址" />
				</view>
			</view>
			
			<!-- 手机号 -->
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">手机号</text>
				<view class="gui-form-body gui-flex gui-rows gui-nowrap gui-align-items-center">
					<input type="number" class="gui-form-input flex-1" 
						v-model="formData.phone" name="phone" maxlength="11" placeholder="请输入手机号" />
					<text style="font-size: 32rpx; color: #C59F79;" class="sendmsg gui-block-text gui-border-l gui-text-right" @tap="getVCode">{{vcodeBtnName}}</text>
				</view>
			</view>
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">短信验证码</text>
				<view class="gui-form-body">
					<input type="number" class="gui-form-input"
					name="pwd" placeholder="请输入短信验证码" />
				</view>
			</view>
			
			<!-- 设置默认就诊卡 -->
			<view class="gui-form-item gui-border-b gui-flex gui-rows gui-space-between">
				<text class="gui-form-label" style="width: 300rpx;">设为默认就诊人</text>
				<view class="">
					<switch :checked="formData.isDefaultPatient == 1" name="isDefaultPatient" color="#C59F79" />
				</view>
			</view>
			<!-- 立即绑定 -->
			
			<view class="gui-flex gui-columns gui-justify-content-center" style="margin-top: 90px;">
				<button type="primary" formType="submit" class="bnt" style="background:  #C59F79;height: 40px;line-height: 40px;font-size: 34rpx;">立即建卡</button>
			</view>
		</form>
	</view>
</template>
<script>
	import {addcard} from '@/api/my.js'
	var graceChecker = require("@/GraceUI5/js/checker.js");
export default {
	data() {
		return {
			vcodeBtnName   : "获取验证码",
			countNum       : 60,
			countDownTimer : null,
			// 表单数据存储
			formData : {
		    userName:"",
				idCard:"",
				phone:"",
				isDefaultPatient:1,
				province:"",
				city:"",
				county:"",
				address:"",
				addressDetail:""
			},
		}
	},
		
	methods: {
		// 表单提交
		submit : function (e) {
			var fromData = e.detail.value;
			if(!fromData.userName){ return this.$common.msg("请输入姓名") }
			if(fromData.idCard.length != 18){ return this.$common.msg("请输入正确的身份证号") }
			if(!this.formData.addressDetail){ return this.$common.msg("请选择住址") }
			if(!fromData.address){ return this.$common.msg("请输入详细地址") }
			if(fromData.phone.length != 11){ return this.$common.msg("请输入手机号") }
			addcard({
				...fromData,
				province:this.formData.province,
				isDefaultPatient:fromData.isDefaultPatient ? 1 : 0,
				city:this.formData.city,
				county:this.formData.county
			}).then(res=>{
				this.$common.msg("建卡成功","success")
				setTimeout(()=>{
					this.$common.back(1)
				},1500)
			})
		},
		chooseDz(e){
			this.formData.addressDetail = e.detail.value[0] + e.detail.value[1] + e.detail.value[2];
			this.formData.province = e.detail.value[0];
			this.formData.city = e.detail.value[1];
			this.formData.county = e.detail.value[2];
		},
		
		getVCode : function(){
			// var myreg=/^[1][0-9]{10}$/;
			// if (!myreg.test(this.phoneno)){
			// 	uni.showToast({ title: '请正确填写手机号码', icon : "none"});
			// 	return false;
			// }
			// 手机号码为 :  this.phoneno
			// vcodeBtnName 可以阻止按钮被多次点击 多次发送 return 会终止函数继续运行
			// if (this.vcodeBtnName != '发送验证码' && this.vcodeBtnName != '重新发送'){return ;}
			// this.vcodeBtnName =  "发送中...";
			// // 与后端 api 交互，发送验证码 【自己写的具体业务代码】
			// // 假设发送成功，给用户提示
			// uni.showToast({ title: '短信已发送，请注意查收', icon : "none"});
			// // 倒计时
			// this.countNum       = 60;
			// this.countDownTimer = setInterval(()=>{this.countDown();}, 1000);
		},
		countDown   : function(){
			if (this.countNum < 1){
			  clearInterval(this.countDownTimer);
			  this.vcodeBtnName = "重新发送";
			  return ;
			}
			this.countNum--;
			this.vcodeBtnName = this.countNum + '秒重发';
		},
		
	}
}
</script>
<style scoped>
.gui-text-small{line-height:50rpx;}
.bnt{
	width: 60%;
	height: 30px;
	font-size: 12px;
	margin: 80rpx auto;
}
</style>