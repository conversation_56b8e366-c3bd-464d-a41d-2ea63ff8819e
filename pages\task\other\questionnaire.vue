<template>
	<view class="p-30">
		<view v-if="obj.taskInfo.status === -1">
			<view style="padding:50rpx 0;">
				<text slot="text"class="gui-block-text gui-text-center gui-margin-top text-zhuti" >此内容已经被撤回...</text>
			</view>
		</view>
		<view v-else>
			<view class="gui-h3 text-center fs-36 font-bold px-20 pb-30">{{obj.taskInfo.questionnaireName || '-'}}</view>
			<view class="fs-40 gui-bold gui-flex gui-justify-content-end mb-20 gui-bg-white">
			  <view class="pageNumstyle">{{pageNums + 1}}/{{obj.answers.length}}</view>
			</view>
			<view :class="['animate', animations[animationIndex]]" >
				<view class="fs-36 mb-30">{{questionnaireData.subjectName}}</view>
				<view class="fs-36 cpbg mx-30" v-show="questionnaireData.subjectType != 'image' ">
					<view class="my-40 mx-60" v-if="questionnaireData.subjectType == 'radio' && questionnaireData.subjectDict">
						<view class="" v-for="(item,index) in questionnaireData.subjectDict.split(/[,]/g)" :key="index" @click="answer(item,questionnaireData)">
							<view class="answertext" @click="answer(item,questionnaireData)"  :class="obj.answers[pageNums].answer === item?'saveanswer':''">
								{{item}}
							</view>
						</view>
					</view>
					<view class="my-40 mx-60 align-center" v-if="questionnaireData.subjectType == 'checkbox' && questionnaireData.subjectDict">
						<view class="" v-for="(item,index) in questionnaireData.subjectDict.split(/[,]/g)" :key="index">
							<view class="answertext" @click="answer(item,questionnaireData)" :class="checkData.indexOf(item) > -1?'saveanswer':''">
								{{item}}
							</view>
						</view>
						<view class="fs-26 gui-color-gray text-center">
							提示：以上选项可多选
						</view>
					</view>
					<view class="w-100" v-if="questionnaireData.subjectType == 'text'">
						<input @blur="val =>{textareaBlur(val,questionnaireData)}" v-model="questionnaireData.answer" style="height: 120rpx;padding-left: 20rpx;" type="text" class="gui-border fs-30" placeholder="请输入" maxlength="100" :disabled="obj.taskInfo.status != 0" />
					</view>
					<view class="w-100 pos-relative" v-if="questionnaireData.subjectType == 'textarea'">
						<textarea @blur="val =>{textareaBlur(val,questionnaireData)}" v-model="questionnaireData.answer" placeholder="请输入" maxlength="200" :disabled="obj.taskInfo.status != 0" class="box-size-border gui-border w-100 p-20 fs-30" placeholder-class="fs-28" />
						<view class="pos-absolute text-grey-b2" style="bottom: 50rpx;right: 50rpx;">{{questionnaireData.answer ? questionnaireData.answer.length : 0}}/200</view>
					</view>
				</view>
				<view class="ml-15 mt-20" v-show="questionnaireData.subjectType == 'image' ">
					<view class="">
						<view class="fs-36">
							使用前 ：
						</view>
						<view class="" v-if="imgs">
							<image :src="imgs" style="width: 700rpx; height: 300rpx; "  @click="clickImg(imgs)"></image>
						</view>
						<view class="" v-else>
							<gui-upload-imageq @change="changeImage" :btnName="'添加使用前的照片'" :maxFileNumber="1" ref="upbefore" uploadServerUrl="https://您的域名/地址">
							</gui-upload-imageq>
						</view>
					</view>
					<view class="mt-40">
						<view class="fs-36">
							使用后 ：
						</view>
						<view class="" v-if="imgAfter">
							<image :src="imgAfter" style="width: 700rpx; height: 300rpx; "  @click="clickImg(imgAfter)"></image>
						</view>
						<view class="" v-else>
							<gui-upload-imageq  @change="changeAfter" :btnName="'添加使用后的照片'" :maxFileNumber="1" ref="upafter" uploadServerUrl="https://您的域名/地址">
							</gui-upload-imageq>
						</view>
					</view>
				</view>
			</view>
			<view style="position: fixed;bottom: 90rpx;width: 690rpx;">
			  <view class="gui-flex gui-row gui-justify-content-center">
			    <view @click="previousPage" v-if="pageNums > 0"
			          class="gui-flex gui-justify-content-center mt-40 mr-20">
			      <view class="contentBnt bg-white">上一页</view>
			    </view>
				<view @click="nextPage" v-if="(obj.taskInfo.status == 4 || obj.taskInfo.status == 1 || obj.taskInfo.status == -1 || questionnaireData.subjectType == 'checkbox' || questionnaireData.subjectType == 'text' || questionnaireData.subjectType == 'textarea') && obj.answers.length != (pageNums+1) "
				      class="gui-flex gui-justify-content-center mt-40 mr-20">
				  <view class="contentBnt bg-white">下一页</view>
				</view>
			    <view @click="reSubmit" v-if="obj.answers.length === (pageNums+1) "
			          class="gui-flex gui-justify-content-center mt-40 ">
			      <view class="saveBnt bg-zhuti">提 交</view>
			    </view>
			  </view>
			</view>
			<view style="height: 180rpx;"></view>
		</view>
	</view>
</template>

<script>
	import {getFollowTaskInfo,submitQuestionnaire} from '@/api/task.js'
	import {constitutionSaveContent} from '@/api/home.js'
	export default {
		data() {
			return {
				animationIndex: 1,
				animations: ['fadeOutLeft · 左侧淡出', 'bounceInRight · 右侧飞入', 'bounceInLeft · 左侧飞入', 'fadeOutRight · 右侧淡出'],
				questionnaireData: [],
				/* 下一页 */
				pageNums: 0,
				/* 所有答案集合 */
				// obj.answers: [],
				lock:false,
				id:"",
				taskType:'',
				checkData:[],
				imgUrl:[],
				imgs:'',
				imgUrlAfter:[],
				imgAfter:'',
				openType:0,//上一页来源  3、咨询页
				obj:{
					answers:[],
					taskInfo:{}
				}
			}
		},
		onShow() {
		 //  if (uni.getStorageSync("obj.answers") && uni.getStorageSync("pageNums")) {
		 //    this.$set(this, 'pageNums', uni.getStorageSync("pageNums"))
		 //    this.$set(this, 'obj.answers', uni.getStorageSync("obj.answers"))
			// if (uni.getStorageSync("obj.answers").subjectType === 'checkbox') {
			// 	let answerarr = uni.getStorageSync("obj.answers").answer.split(/[,，]/g)
			// 	this.$set(this,'checkData',answerarr)
			// }
		 //  }
		},
		onLoad(options) {
			this.openType = options.openType
			this.id = options.id;
			this.taskType = options.taskType;
			this.getDetail();
		},
		methods:{
			clickImg(item) {
				wx.previewImage({
					urls: [item], //需要预览的图片http链接列表，多张的时候，url直接写在后面就行了
					current: '', // 当前显示图片的http链接，默认是第一个
					success: function(res) {},
					fail: function(res) {},
					complete: function(res) {},
				})
			},
			// 记录选择图片时的待提交数据
			changeImage: function(e) {
				// 数组类型
				this.imgUrl = e
			},
			// 记录选择图片时的待提交数据
			changeAfter: function(e) {
				// 数组类型
				this.imgUrlAfter = e
			},
			//预提交-需要先上传
			reSubmit() {
				// 先取出 url地址 赋值给arrs
				if(this.imgUrl.length>0){
					let arrs = [];
					this.imgUrl.forEach((item, index) => {
						uni.compressImage({
						  src: item.url,
						  quality: 80,
						  success: res => {
						    console.log(res.tempFilePath)
							  arrs.push(res.tempFilePath)
						  }
						})
						// arrs.push(item.url)
					})
					this.imgUrlAfter.forEach((item, index) => {
						uni.compressImage({
						  src: item.url,
						  quality: 80,
						  success: res => {
						    console.log(res.tempFilePath)
							console.log(typeof res.tempFilePath)
							  arrs.push(res.tempFilePath)
							  console.log('压缩后图片arrs',arrs)
						  }
						})
						// arrs.push(item.url)
					})
					// 将相同的过滤出来
					let that = this
					setTimeout(function() {
						console.log('图片arrs',arrs)
						if (arrs.length>0) {
							let resultArr = arrs.filter(function(item, index, self) {
								// 字符匹配
								return self.indexOf(item) == index;
							})
							console.log('1111')
							let arr = '';
							let c = 0
							for (let key in resultArr) {
								console.log('2222')
								that.$common.uploadFile(resultArr[key], resx => {
									console.log('33333')
									c += 1; //上传成功一个+1
									if (c == resultArr.length) {
										//所有图片已经传完
										arr += resx.data.url;
										console.log('444')
										that.save(arr) //提交表单

									} else {
										arr += resx.data.url + ',';
									}
								});
							}
						}
					}, 2000);

				}else{
					this.save()
				}

			},
			save(arr){
				console.log('555')
				let isOk = true;
				this.obj.answers.forEach(item=>{
					if (item.subjectType == 'image') {
						item.answer = arr
					}
					if(item.isRequired == 1){
						if(!item.answer){
							isOk = false;
						}
					}
				})
				console.log('666')
				if(!isOk){return this.$common.msg("请完整填写问卷")}
				uni.showModal({
					title: '提示',
					content: '内容已填写完成，确认提交问卷吗？',
					cancelText: "取消",
					confirmText: "提交",
					confirmColor: '#C59F79',
					cancelColor: '#000000',
					success: res => {
						if (res.confirm) {
							console.log('问卷类型===',this.obj.taskInfo.questionType)
							if (this.obj.taskInfo.questionType == 2) {
								console.log('接口入参obj==',this.obj)
								console.log('接口入参==',JSON.parse(JSON.stringify(this.obj.answers)))
								constitutionSaveContent(
									JSON.parse(JSON.stringify(this.obj.answers))
								).then(res => {
									if (res.code === 200) {
										this.$common.msg("问卷提交成功", "success")
										setTimeout(() => {
											this.$common.navLaunch(
												'/pages/homeAc/evaluation/details?sum=' +
												res.data + '&questionId=' + this.obj.taskInfo.questionnaireId +'&taskId='+this.obj.taskInfo.id)
										}, 1000)
									}
								})
							} else{
								submitQuestionnaire({
									patientId:uni.getStorageSync('user').id, // 系统用户id
									id:this.id,
									taskType:this.taskType,
									questionnaireId:this.obj.taskInfo.questionnaireId,
									questionAnswers:this.obj.answers
								}).then(res=>{
									this.$common.msg("提交成功","success")
									setTimeout(()=>{
										if (this.openType == 3) {
										  this.$common.navBack(1)
										} else{
										  // uni.removeStorageSync("obj.answers")
										  // uni.removeStorageSync("pageNums")
										  this.$common.navTab('/pages/task/index')
										  // this.getDetail();
										}
									},1000)
								})
							}
						}
					}
				})

			},
			checkChange(e,index,type){
				this.obj.answers[index].answer = type == 2 ? e.detail.value.join() : e.detail.value;
			},
			getDetail(){
				getFollowTaskInfo({
					id:this.id,
					taskType:this.taskType
				}).then(res=>{
					res.data.answers.forEach(item=>{
						if(item.subjectType == 'checkbox'){
							item.answer = item.answer ? item.answer.split(/[,]/g) : [];
						}
						if (item.subjectType == 'image' && item.answer != null) {
							let imgdata = item.answer.split(",").map(item =>{
								let obj = {}
								obj.img = item
								return obj
							})
							this.imgs = imgdata[0].img
							this.imgAfter = imgdata[1].img
						}
					})
					this.$set(this, 'obj', res.data)
					// this.obj = res.data
					this.$set(this, 'questionnaireData', this.obj.answers[this.pageNums])
					if (this.obj.answers[this.pageNums].subjectType === 'checkbox') {
						let answerarr = this.obj.answers[this.pageNums].answer || []
						this.$set(this,'checkData',answerarr||[])
					}
					this.$forceUpdate();
				})
			},
			// 答案选择事件
			answer(value, data,type) {
			  if (!this.lock) {
			    this.lock = true
			    if (!value) {
			      this.$common.msg("请完整填写问卷！")
			      return
			    }
				if (data.subjectType === 'checkbox') {
					if(typeof(this.checkData) == 'string'){
						this.checkData = this.checkData.split(',')
					}
					if(this.checkData.indexOf(value)>-1){
						this.checkData.splice(this.checkData.indexOf(value),1)
					}else{
						this.checkData.push(value)
					}
				}
				var checkData = this.checkData
			    this.$set(this.obj.answers, this.pageNums, {
			      answer: data.subjectType === 'checkbox' ? checkData.join(',') : value,
				  id:data.id,
				  isRequired:data.isRequired,
			      questionId: data.questionId,
				  scoreStandard:data.scoreStandard,
			      subjectDict: data.subjectDict,
			      subjectName: data.subjectName,
			      subjectOrder: this.pageNums,
			      subjectType: data.subjectType,
				  warming:data.warming,
				  taskId:this.id,
			      // subjectId: data.subjectId,
			      // questionType: data.questionType,
			      // constitutionType: data.constitutionType,
			      patientId: uni.getStorageSync("cardObj")?.patientId || ''
			    })
				console.log('已选中的答案信息==========',this.obj)
			    if (data.subjectType == 'radio' && (Number(this.pageNums + 1) === this.obj.answers.length)) {
					this.save()
					return
			    }
			    if (Number(this.pageNums + 1) !== this.obj.answers.length && data.subjectType != 'checkbox') {
			      setTimeout(() => {
			        this.lock = false
			        this.nextPage()
			      }, 200);
			    } else {
			      this.lock = false
			    }
			  }
			},
			/* 下一页 */
			nextPage() {
			  this.$set(this, 'animationIndex', 0)
			  this.$set(this, 'pageNums', this.pageNums + 1)
			  setTimeout(() => {
			    this.$set(this, 'animationIndex', 1)
			  }, 200);
			  this.$set(this, 'questionnaireData', this.obj.answers[this.pageNums])
			  if (this.obj.answers[this.pageNums].subjectType === 'checkbox') {
			  	let answerarr = this.obj.answers[this.pageNums].answer || []
			  	this.$set(this,'checkData',answerarr||[])
			  }

			},
			/* 上一页 */
			previousPage() {
			  this.$set(this, 'animationIndex', 3)
			  this.$set(this, 'pageNums', this.pageNums - 1)
			  this.$set(this, 'questionnaireData', this.obj.answers[this.pageNums])
			  setTimeout(() => {
			    this.$set(this, 'animationIndex', 2)
			  }, 200);
			if (this.obj.answers[this.pageNums].subjectType === 'checkbox') {
				let answerarr = this.obj.answers[this.pageNums].answer || []
				this.$set(this,'checkData',answerarr||[])
			}
			},
		}
	}
</script>

<style lang="less" scoped>
/* 引入动画库 */
@import "@/GraceUI5/css/animate.css";

/* 定义动画修饰样式 */
.animate {
  animation-duration: 1s;
  animation-timing-function: linear;
}

.cpbg {
  border: 1px solid #C59F79;
  border-radius: 10rpx;
}

.pageNumstyle {
  padding: 10rpx 20rpx 10rpx 40rpx;
  border: 1px solid #C59F79;
  border-right: none;
  border-bottom-left-radius: 50rpx;
  border-top-left-radius: 50rpx;
}

.answertext {
  border: 1px solid #e5e5e6;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.saveanswer {
  background-color: #C59F79;
  color: #ffffff;
}

.contentBnt,
.saveBnt {
  border: 1px solid #C59F79;
  bottom: 30rpx;
  width: 220rpx;
  height: 80rpx;
  font-size: 34rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 5rpx;
}

.saveBnt {
  color: #fff;
}
	page{
		background: #fff;
	}
</style>
