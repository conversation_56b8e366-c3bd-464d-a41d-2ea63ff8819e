<template>
	<view class="exchang">
		<view class="exchang-top">
			<image src="/static/img/cf_ico_appsuc.png"/>
			<view class="title">兑换成功</view>
			<view class="text">已兑换积分：{{productDta.integral}}</view>
			<view class="text">剩余积分：{{productDta.userIntegral}}</view>
		</view>
		<view class="info">
			<view class="title">兑换信息</view>
			<view class="text">下单人：{{productDta.userName}}</view>
			<view class="text">兑换号：{{productDta.serialNumber}}</view>
			<view class="text">兑换时间：{{productDta.createTime}}</view>
		</view>
		<view class="exchang-btn">
			<button @click="check">查看记录</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				productDta:[]
			}
		},
		onLoad(e) {
			if (e.item) {
				this.productDta = JSON.parse(decodeURIComponent(e.item))
			}
			
		},
		methods:{
			check(){
				this.$common.navCloseTo("/pages/integral/record?status=1")
				// uni.navigateTo({
				//      url: './record'
				// })
			}
		}
	}
</script>

<style lang="scss" scoped>
	.exchang{
		.exchang-btn{
			position: absolute;
			display: flex;
			justify-content: center;
			align-items: center;
			width: 100%;
			bottom: 40rpx;
			button{
				background-color: #C59F79;
				color: #fff;
				width: 300rpx;
				border-radius: 50rpx;
			}
		}
		.title{
			font-size: 40rpx;
			font-weight: bold;
			margin-bottom: 20rpx;
			font-size: 35rpx;
		}
		.text{
			line-height: 50rpx;
			color: #999;
			font-size: 32rpx;
		}
		.info{
			padding: 20rpx;
			margin-top: 40rpx;
		}
		.exchang-top{
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding-top: 40rpx;
			image{
				width: 100rpx;
				height: 100rpx;
				margin-bottom: 20rpx;
			}
		}
	}
</style>