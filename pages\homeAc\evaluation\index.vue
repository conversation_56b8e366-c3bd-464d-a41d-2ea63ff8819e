<template>
  <view class="gui-padding gui-flex gui-align-items-center gui-justify-content-center gui-columns pt-50">
    <view class="mt-150">
      <view class="fs-28 gui-bold title">温馨提示：</view>
      <view class="mt-20 content">{{ content }}</view>
    </view>
    <view @click="navTo('2',typeId)" class="contentBnt mt-200 bg-zhuti"
          v-if="String(completion) !== 'undefined' && !completion">
      开始测评
    </view>
    <view @click="navTo('1',typeId)" class="contentBnt mt-200 bg-zhuti"
          v-if="String(completion) != 'undefined' && completion">
      查看测评结果
    </view>
    <view @click="navTo('2',typeId)" class="contentBnt mt-30 " style="color: #C59F79;background-color: #fff;border: 0.5px solid #C59F79;"
          v-if="String(completion) !== 'undefined' && completion">
      重新测评
    </view>
  </view>
</template>

<script>
	import {constitutionFillContent,appraisalFillContent} from '@/api/home.js'
export default {
  data() {
    return {
		domainType:this.$common.domainType || 1,
		patientId:uni.getStorageSync("cardObj")?.patientId,
		memberId:uni.getStorageSync("user")?.id,
		questionName:'',
		typeId:5,//默认以前的体质测评问卷入参 数字 5
		questionType:'',
		ready: false,
		completion: undefined,/* 完成状态 */
		fractionSum:0,//上一次测评的问卷结果分数
		content: '请逐项阅读每一个问题，选择最符合您实际情况的选项。如果某一个问题您不能肯定回答，请选择最接近您实际情况的选择，每一个问题只能选择一个选项。'
    }
  },
  onLoad(option) {
	  this.typeId = option.typeId?option.typeId:5;//默认以前的体质测评问卷入参 数字 5
	  this.questionName = option.questionName?option.questionName:'中医体质综合问卷测评';
	  this.questionType = option.questionType
	  if (option.questionName =='中医体质综合问卷测评') {
		  this.evaluationAppraisa()
	  } else{
	  	this.evaluationSituation(this.typeId)
	  }
  },
  methods: {
	  evaluationAppraisa() {
	  	var patientId = uni.getStorageSync("cardObj")?.patientId || ''
	  	var memberId = patientId ? '' : uni.getStorageSync("user")?.id
	  	appraisalFillContent({
	  		patientId,
	  		memberId
	  	}).then(res=>{
	  		this.ready = true
	  		if (res.data) {
	  		  // 已测评
	  		  this.completion = true
	  		} else {
	  		  // 未测评
	  		  this.completion = false
	  		}
	  	})
	  },
    navTo(e,typeId) {
		if (e == '1' && this.questionName != '中医体质综合问卷测评' && this.questionType == 2) {
			this.$common.navTo('/pages/homeAc/evaluation/details?sum=' +this.fractionSum + '&questionId=' +typeId + '&memberId=' + this.memberId+ '&patientId=' + this.patientId)
		} else{
			this.$common.navCloseTo('/pages/homeAc/evaluation/content?type='+ e +'&questionId='+typeId+'&questionName='+this.questionName+'&questionType='+this.questionType)
		}
    },
    evaluationSituation(typeId) {
		constitutionFillContent({
			patientId:this.patientId || '',
			questionId:typeId?typeId:this.typeId,
			memberId:this.patientId ? '' : this.memberId
		}).then(res=>{
			this.ready = true
			if (res.data) {
				this.$set(this,'fractionSum',res.data.fractionSum)
			  // 已测评
			  this.completion = true
			} else {
			  // 未测评
			  this.completion = false
			}
		})
    }
  }
}
</script>

<style scoped>
.title {
  font-size: 38rpx;
}
.content {
  font-size: 38rpx;
  line-height: 60rpx;
  text-indent: 60rpx;
}

.contentBnt {
  width: 320rpx;
  height: 80rpx;
  font-size: 34rpx;
  line-height: 80rpx;
  border-radius: 5rpx;
  color: #fff;
  text-align: center;
}
</style>
