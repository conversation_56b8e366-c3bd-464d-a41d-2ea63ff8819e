import Http from "../net/Http.js"
import noHttp from "../net/noHttp.js"

// 获取token
export function getToken(data){
	return noHttp.post('/wx/auth/token/',data)
}
// 重新访问接口
export function getshuju(urldata){
	// console.log(urldata,'ddddddddddd')
	if(urldata.method == 'get'){
		return Http.get(urldata.url,urldata.data)
	}
	if(urldata.method == 'post'){
		return Http.post(urldata.url,urldata.data)
	}
}
// 获取健康日记反馈数量 /wx/health/diary/countFeedback
export function countFeedback(data){
	return Http.get('/wx/health/diary/countFeedback',data)
}
// 轮播图
export function carouseList(){
	return noHttp.get('/wx/index/carouseList')
}
// 科室介绍
export function ksjj(data){
	return noHttp.get('/wx/index/getDept/' + data)
}
// 获取就诊卡
export function getCard(){
	return Http.get('/wx/card/list')
}
// 建议列表接口
export function heathList(data){
	return Http.get('/wx/heath/list',data)
}
// 扫一扫接口
export function register(data){
	return Http.post('/wx/zwb/scan/register',data)
}
// 关怀消息列表1
export function noticeList(data){
	return Http.get('/wx/notice/list',data)
}
// 积分明细1
export function getPatientInfo(data){
	return Http.get('/wx/task/getPatientInfo',data)
}
// 小程序首页体质情况1
export function bodyhealth(data){
	return Http.get('/wx/task/bodyhealth',data)
}
// 宣教方案详情
export function programmeArticle(data){
	return Http.get('/wx/task/programmeArticle',data)
}
// 方案详情
export function programmeList(data){
	return Http.get('/wx/task/programmeList',data)
}
// 建议详情接口1
export function dpMedical(data){
	return Http.post('/wx/heath/dp-medical',data)
}
// 健康记录列表接口
export function healthList(data){
	return Http.get('/wx/zwb/contract/health-record-list',data)
}
// 健康评估列表接口
export function queryEvaluateAndTask(data){
	return Http.get('/wx/evaluate/queryEvaluateAndTask',data)
}
// 健康记录详情接口1
export function healthDetail(data){
	return Http.get('/wx/heath/wx-list-record-detail',data)
}
// 健康记录-运动减脂方案详情接口
export function listFatloss(data){
	return Http.get('/wx/heath/fatloss/list',data)
}
// 健康记录-中医禁食疗法方案详情接口
export function listFastingTherapy(data){
	return Http.get('/wx/heath/fastingTherapy/list',data)
}
// 健康记录-营养干预方案
export function listNutritionIntervention(data){
	return Http.get('/wx/heath/nutritionIntervention/list',data)
}
// 患者详情-营养干预方案PDF预览
export function previewNutritionInterventionPDF(data){
	// 超时时间
	return Http.get('/wx/heath/nutritionIntervention/previewPDF',data)
}
// 营养干预方案PDF预览
export function getNutritionInterventionReport(data){
	return Http.get('/wx/heath/nutritionIntervention/getNutritionInterventionReport',data)
}
// 检查结果详情
export function InspectResultInfo(data,type){
	const url = type == 1 ? '/wx/zwb/InspectResultInfo' + data : '/wx/zwb/examinationInfo/report-list'
	return Http.get(url,data)
}
// 通用病历模板详情
export function getInfoMedical(data){
	return Http.post('/wx/zwb/record/getInfoMedical',data)
}
// 咨询医生列表
export function getDoctorList(data){
	return Http.get('/wx/task/getDoctorList',data)
}
//所属科室列表
export function getListByDeptId(data){
	return noHttp.get('/wx/app/getListByDeptId',data)
}
//患者所属科室
export function isIntake(data){
	return noHttp.get('/wx/task/isIntake',data)
}
// 获取未读数
export function getNoRead(data){
	return Http.get('/wx/task/getNoRead',data)
}
// 积分明细
export function getIntegralDetailed(data){
	return Http.get('/wx/task/getIntegralDetailed',data)
}
// 检查报告详情
export function getInspectInfo(data){
	return Http.get('/wx/task/getInspectInfo/'+data)
}
// 检查列表
export function getInspectList(data){
	return Http.get('/wx/task/getInspectList',data)
}
// 检验列表1
export function getExaminationList(data){
	return Http.get('/wx/task/getExaminationList',data)
}
// 用药列表1
export function getMedicationList(data){
	return Http.get('/wx/task/getMedicationList',data)
}
// 检验报告详细
export function getExaminationInfo(data){
	return Http.get('/wx/task/getExaminationInfo/'+data)
}
// 扫一扫获取药品详情
export function getGoods(data){
	return Http.get('/wx/goods/'+data)
}
// 获取其他健康测问卷列表
export function questionnaireList(data){
	return Http.get('/wx/questionnaire/list',data)
}
// 获取问卷填写内容 GET
export function constitutionFillContent(data){
	return Http.get('/wx/question/constitution/fill/content',data)
}
// 保存问卷内容 POST
export function constitutionSaveContent(data){
	return Http.post('/wx/question/constitution/save/content',data)
}
// 获取问卷题目内容
export function constitutionSubjectContent(data){
	return Http.get('/wx/question/constitution/subject/content/'+data)
}
// 获取体质测评问卷填写内容 GET
export function appraisalFillContent(data){
	return Http.get('/wx/question/constitution/appraisal/fill/content',data)
}
// 保存体质测评问卷内容 POST
export function appraisalSaveContent(data){
	return Http.post('/wx/question/constitution/appraisal/save/content ',data)
}
// 获取体质测评问卷题目内容
export function appraisalSubjectContent(data){
	return Http.get('/wx/question/constitution/appraisal/subject/content/'+data)
}

// 健康日记
// 列表
export function diaryList(data){
	return noHttp.get('/wx/health/diary/list',data)
}
// 发布
export function diaryAdd(data){
	return Http.post('/wx/health/diary/add',data)
}
// 删除
export function diaryDel(data){
	return Http.get('/wx/health/diary/del/'+data)
}
// 编辑
export function diaryEdit(data){
	return Http.post('/wx/health/diary/edit',data)
}
// 详情
export function diary(data){
	return Http.get('/wx/health/diary/'+data)
}
// 获取其他健康测评问卷结果接口
export function getCriteria(data){
	return Http.get('/wx/question/constitution/getCriteria',data)
}
// 获取患者问卷填写分数信息
export function getQuestSum(data){
	return Http.get('/wx/question/constitution/getQuestSum',data)
}

// 首页-查询签约科室和医生
export function contractDoctor(data){
	return Http.get('/wx/zwb/contract/doctor',data)
}

// 健康评估-查询可显示小程序问卷分组
export function questionnairegroup(data){
	return Http.get('/wx/questionnaire/group',data)
}
