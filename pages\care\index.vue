<template>
	<view>
		<view >
			<view class="gui-list-items"  v-for="(item,index) in list" :key="index" @click="toDetail(item)">
				<view class="bg-white w-100 box-size-border mx-30 p-30 mt-40">
					<view class="pb-20" style="font-size: 32rpx;">
						<view class="gui-primary-color ellipsis-1 font-bold flex-1">{{item.title}}</view>
					</view>
					<view v-if="item.state == 5" class="gui-list-title-text gui-primary-color ellipsis-2 text-grey-74">{{item.remark}}</view>
					<view v-else class="gui-list-title-text gui-primary-color ellipsis-2 text-grey-74" style="display: -webkit-box;" v-html="item.content"></view>
					<view class="fs-26 gui-color-gray time border-t mt-20 pt-30 ">
						<text>{{item.createTime || '-'}}</text>
						<!-- <text >系统消息</text> -->
					</view>
				</view>
			</view>
			<gui-loadmore ref="loadmorecom" :status="loadState" class="w-100" style="box-sizing: border-box;"></gui-loadmore>
		</view>
		<view v-if="list.length<= 0">
			<gui-empty>
				<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
					<image class="gui-empty-img" src="/static/kong.png"></image>
				</view>
				<text slot="text"
				class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
			</gui-empty>
		</view>
	</view>
</template>

<script>
	import {noticeList} from '@/api/home.js'
	export default {
		data() {
			return {
				useropenId:uni.getStorageSync('user').id,
				loadingStatus:false, //  ， true： 请求中   false :请求结束
				loadState: 0 ,  // 0 : 默认0，有下一页   1 ：请求中  2： 加载完毕
				page:1,
				size:20,
				zongSize:0,
				list:[]
			}
		},
		onShow() {
			// this.$common.getMsgNums();  // 获取消息未读数
			this.init();
		},
		//下拉刷新
		onPullDownRefresh() {
			this.init();
			setTimeout(function() {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		//上拉加载
		onReachBottom: function() {
			//避免多次触发
			if(this.loadingStatus || this.loadState == 2){return}
			this.getList();
		},
		methods: {
			// 初始化
			init(){
				this.page = 1;
				this.loadingStatus = false;
				this.loadState = 0;
				this.getList();
			},
			getList() {
				this.loadingStatus  = true;
				noticeList({
					pageNum:this.page,
					pageSize:this.size,
					memberId:this.useropenId,
					// state:5
				}).then(res=>{
					this.list = this.page == 1 ? res.rows : this.list.concat(res.rows);
					this.zongSize = res.total;
					if(this.list.length >= this.zongSize){
						this.loadState = 2;
						this.loadingStatus  = false;
						return;
					}
					this.page++;
					this.loadState = 0;
					this.loadingStatus  = false;
				})
			},
			toDetail(item){
				if (item.state == 6) {
					// 复诊通知--与治未病的文件路径有差异
					this.$common.navTo('/pages/homeAc/other/furtherVisit?id='+item.taskId)
					return
				}
				noticeList({
					id:item.id
				}).then(res=>{
					let dataarr = res.rows[0]
					if (item.state == 2) {
						// 宣教
						this.$common.navTo('/pages/task/other/missionary?id='+dataarr.taskId+'&taskType='+dataarr.state)
					} else if(dataarr.state == 1) {
						// 问卷
						this.$common.navTo('/pages/task/other/questionnaire?id='+dataarr.taskId+'&taskType='+dataarr.state)
					}else if(dataarr.state == 4) {
						// 康养
						// this.$common.navTo('/pages/task/other/treatment?id='+dataarr.taskId+'&taskType='+dataarr.state)
					}else if(dataarr.state == 5) {
						this.$common.navTo("./detail?item="+encodeURIComponent(JSON.stringify(res.rows?dataarr:[])))
					}else {
						// 监测
						let i = parseInt(dataarr.jcType);
						switch (i) {
							case 1:
								this.$common.navTo('/pages/task/other/aXueya?id='+dataarr.taskId+'&taskType='+dataarr.state)
								break;
							case 2:
								this.$common.navTo('/pages/task/other/bMaibo?id='+dataarr.taskId+'&taskType='+dataarr.state)
								break;
							case 3:
								this.$common.navTo('/pages/task/other/cXuetang?id='+dataarr.taskId+'&taskType='+dataarr.state)
								break;
							case 4:
								this.$common.navTo('/pages/task/other/dXueyang?id='+dataarr.taskId+'&taskType='+dataarr.state)
								break;
							case 5:
								this.$common.navTo('/pages/task/other/eTiwen?id='+dataarr.taskId+'&taskType='+dataarr.state)
								break;
							case 6:
								this.$common.navTo('/pages/task/other/fHuxi?id='+dataarr.taskId+'&taskType='+dataarr.state)
								break;
							case 7:
								// this.$common.msg("正在升级，敬请期待~")
								// 本本升级
								this.$common.navTo('/pages/task/tongue?id='+dataarr.taskId+'&taskType='+dataarr.state)
								break;
						}
					}
				})
			},
		}
	}
</script>

<style>
page{
	background: #f5f5f5;
}
</style>
