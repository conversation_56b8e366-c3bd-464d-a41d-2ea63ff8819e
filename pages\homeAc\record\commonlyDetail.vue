<template>
	<view class="w-100 over-hidden">
		<view class="box-top">
			<view class="content">
				<text>就诊人</text>
				<text>{{cardObj.userName}}</text>
			</view>
			<view class="content">
				<text>就诊科</text>
				<text>{{info.deptName}}</text>
			</view>
			<view class="content">
				<text>就诊时间</text>
				<text>{{info.visitDate || '-'}} {{info.timeShard || ''}}</text>
			</view>
		</view>
		<view class="navlist">
			<view @click="navClick(index)" :class="['nav-item',{'nav-activate':index == navindex}]" v-for="(item,index) in navlist">{{item.name}}</view>
		</view>
		
		<view class="" v-if="navindex == 0">
			<!-- 检查 -->
			<view class="jcBox">
				<!-- <view class="tab-title1">检查信息</view> -->
				<view v-if="jcList.length<= 0">
					<gui-empty>
						<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
							<image class="gui-empty-img mt-5" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
						</view>
						<text slot="text" 
						class="gui-text-small gui-block-text gui-text-center mb-30" style="color:#B2B2B2;">暂无数据</text>
					</gui-empty>
				</view>
				<view class=" w-90 mr-10 gui-flex  mb-20 gui-align-items-center " v-for="(item,index) in jcList" >
					<view class="w-70 fs-30" style="line-height: 60rpx;">{{item.applyItems}}</view> 
					<button style="background-color: #C59F79;" class="fs-28  bcolor gui-color-white"   @click=" jcreport(item)">查看报告</button>
				</view>
			</view>
		</view>
		<view class="" v-if="navindex == 1">
			<!-- 检验 -->
			<view >
				<!-- <view class="tab-title">检验信息</view> -->
				<view v-if="jyList.length<= 0">
					<gui-empty>
						<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
							<image class="gui-empty-img mt-5" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
						</view>
						<text slot="text" 
						class="gui-text-small gui-block-text gui-text-center mb-30" style="color:#B2B2B2;">暂无数据</text>
					</gui-empty>
				</view>
				<view class=" w-90 mr-10 gui-flex  mb-20 gui-align-items-center " v-for="(item,index) in jyList" >
					<view class="w-70 fs-30" style="line-height: 60rpx;">{{item.applyItems}}</view> 
					<button class="fs-28  bcolor gui-color-white"   @click=" jyReport(item)">查看报告</button>
				</view>
			</view>
		</view>
		<view class="" v-if="navindex == 2">
			<!-- 用药信息 -->
			<view >
				<!-- <view class="tab-title">用药信息</view> -->
				<view v-if="yyList.length<= 0">
					<gui-empty>
						<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
							<image class="gui-empty-img mt-5" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
						</view>
						<text slot="text" 
						class="gui-text-small gui-block-text gui-text-center mb-30" style="color:#B2B2B2;">暂无数据</text>
					</gui-empty>
				</view>
				<view class=" w-90 mr-10 gui-flex  mb-20 gui-align-items-center " v-for="(item,index) in yyList" >
					<view class="text-ov fs-30" style="line-height: 60rpx;max-width: 60%;min-width: 40%;">{{item.medicationName}}</view>
					 <view class="s-30 text-purple ml-10" style="line-height: 60rpx;">{{item.doseValue}} &nbsp;&nbsp;{{item.frequency}}</view>
					<!-- <button class="fs-28  bcolor gui-color-white"   @click=" jyReport(item)">查看报告</button> -->
				</view>
			</view>
		</view>
		<!-- <view class="margin"></view> -->
		<!-- <view style="padding-bottom: 94rpx;">
			医生诊断
			<view class="gui-padding" v-for="(item,index) in students" :key="index">
				<view class="gui-table" style="margin-top:50rpx;">
					<view class="tab-title">医生诊断</view>
					<view class="gui-theader gui-flex gui-rows gui-nowrap bgd bor">
						<text class="gui-td gui-td-text gui-bold gui-text-center">诊断</text>
					</view>
					<view class="gui-tbody gui-flex gui-rows gui-nowrap bgd2 bor">
						<text class="gui-td2 gui-text-center">{{info.diagTitle || '-'}}</text>
					</view>
				</view>
			</view>
			检查信息
			<view class="gui-padding">
				<view class="gui-table" style="margin-top:50rpx;">
					轮播项目数量对应  
					<scroll-view :scroll-y="true" class="mt-20" :style="{height:mainHeight+'px'}" >
						检查
						<view class="jcBox">
							<view class="tab-title1">检查信息</view>
							<view v-if="jcList.length<= 0">
								<gui-empty>
									<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
										<image class="gui-empty-img mt-5" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
									</view>
									<text slot="text" 
									class="gui-text-small gui-block-text gui-text-center mb-30" style="color:#B2B2B2;">暂无数据</text>
								</gui-empty>
							</view>
							<view class=" w-90 mr-10 gui-flex  mb-20 gui-align-items-center " v-for="(item,index) in jcList" >
								<view class="w-70 fs-30" style="line-height: 60rpx;">{{item.applyItems}}</view> 
								<button style="background-color: #C59F79;" class="fs-28  bcolor gui-color-white"   @click=" jcreport(item)">查看报告</button>
							</view>
					    </view>
						检验
						<view >
							<view class="tab-title">检验信息</view>
							<view v-if="jyList.length<= 0">
								<gui-empty>
									<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
										<image class="gui-empty-img mt-5" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
									</view>
									<text slot="text" 
									class="gui-text-small gui-block-text gui-text-center mb-30" style="color:#B2B2B2;">暂无数据</text>
								</gui-empty>
							</view>
							<view class=" w-90 mr-10 gui-flex  mb-20 gui-align-items-center " v-for="(item,index) in jyList" >
								<view class="w-70 fs-30" style="line-height: 60rpx;">{{item.applyItems}}</view> 
								<button class="fs-28  bcolor gui-color-white"   @click=" jyReport(item)">查看报告</button>
							</view>
						</view>
						用药信息
						<view >
							<view class="tab-title">用药信息</view>
							<view v-if="yyList.length<= 0">
								<gui-empty>
									<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
										<image class="gui-empty-img mt-5" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
									</view>
									<text slot="text" 
									class="gui-text-small gui-block-text gui-text-center mb-30" style="color:#B2B2B2;">暂无数据</text>
								</gui-empty>
							</view>
							<view class=" w-90 mr-10 gui-flex  mb-20 gui-align-items-center " v-for="(item,index) in yyList" >
								<view class="text-ov fs-30" style="line-height: 60rpx;max-width: 60%;min-width: 40%;">{{item.medicationName}}</view>
								 <view class="s-30 text-purple ml-10" style="line-height: 60rpx;">{{item.doseValue}} &nbsp;&nbsp;{{item.frequency}}</view>
								<button class="fs-28  bcolor gui-color-white"   @click=" jyReport(item)">查看报告</button>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
		</view> -->

	</view>
</template>

<script>
	import {getInspectList,getExaminationList,getMedicationList} from '@/api/home.js'
	export default {
		data() {
			return {
				navindex:0,
				navlist:[{id:1,name:'检查信息'},{id:2,name:'检验信息'},{id:3,name:'用药信息'}],
				resInformation: [],
				jcList: [],// 检查
				jyList: [],// 检验
				yyList:[],// 用药
				info:{},
				cardObj: uni.getStorageSync('cardObj'),
				
			}
		},
		onLoad(option) {
			this.info = JSON.parse(decodeURIComponent(option.item))
			this.getDetail(0);
		},
		methods: {
			navClick(e){
				this.navindex = e
				this.getDetail(e);
			},
			getDetail(clickKey){
				if (clickKey === 1) {
					// 检验信息列表
					getExaminationList({
						patientId: this.info.patientId,
						orderByColumn: 'apply_time',
						isAsc: 'desc',
						type: '0'
					}).then(res=>{
						res.rows.forEach(row =>this.jyList.push(...row.filter(row => this.toDateByYMD(row.applyTime) === this.info.visitDate)))
					})
				} else if (clickKey == 2) {
					// 用药信息列表
					getMedicationList({
						patientId: this.info.patientId,
						orderByColumn: 'giver_time',
						isAsc: 'desc',
						type: '0'
					}).then(res=>{
						res.rows.forEach(row =>this.yyList.push(...row.filter(row => this.toDateByYMD(row.giverTime) === this.info.visitDate)))
					})
				} else{
					// 检查信息列表
					getInspectList({
						patientId: this.info.patientId,
						orderByColumn: 'apply_time',
						isAsc: 'desc',
						type: '0'
					}).then(res=>{
						// this.jcList = res.rows.filter(row => row.applyTime === this.info.visitDate);
						  res.rows.forEach(row =>this.jcList.push(...row.filter(row => this.toDateByYMD(row.applyTime) === this.info.visitDate)))
					})
				}
			},
	        //检查报告
			jcreport(item) {
				this.$common.navTo('/pages/homeAc/record/jcReport?info='+JSON.stringify(item));
				// console.log('asssss');
			 },
			 //检验报告
			 jyReport(item) {
			 	this.$common.navTo('/pages/homeAc/record/jyReport?info='+JSON.stringify(item));
			 	// console.log('asssss');
			  },
			  toDateByYMD : (dateStr) => {
			  	var date = new Date(dateStr);
			  	var y = date.getFullYear();
			  	var m = date.getMonth() + 1;
			  	m = m < 10 ? ('0' + m) : m;
			  	var d = date.getDate();
			  	d = d < 10 ? ('0' + d) : d;
			  	return y + '-' + m + '-' + d;
			  }
		}
	}
</script>

<style scoped>
	.navlist{
		display: flex;
		flex-direction: row;
	}
	.nav-item{
		flex:1;
		border-bottom: 2rpx solid #fff;
		text-align: center;
		padding: 20rpx 0;
	}
	.nav-activate{
		color: #007aff;
		border-bottom: 2rpx solid #007aff !important;
	}
	.gui-td {
		width: 100rpx;
		flex: 1;
		overflow: hidden;
		padding: 0 10rpx;
		display: flexbox;
	}

	.gui-td2 {
		width: 100rpx;
		flex: 1;
		overflow: hidden;
		padding: 21rpx 10rpx;
		display: flexbox;
		font-size: 28rpx;
		color: #333333;
	}

	.gui-td-text {
		line-height: 60rpx !important;
		font-size: 24rpx;
	}

	.bgd {
		background: #C59F79;
		color: #fff;
	}

	.bgd2 {
		background: #F9F9F9;
	}

	.bor text {
		border: 1px solid #FFFFFF;
		box-sizing: border-box;
	}

	.clr {
		color: #F37623;
		text-decoration: underline;
	}

	.title {
		text-align: center;
		color: #3DAB7E;
		font-size: 32rpx;
		font-weight: bold;
		margin: 11rpx 0 30rpx 0;
	}

	.title view {
		margin: -16rpx auto;
		width: 0;
		padding: 10rpx 90rpx;
		background: #E3F3ED;
	}

	.content {
		width: 100%;
		height: 64rpx;
		line-height: 64rpx;
		padding: 0 28rpx;
		font-size: 28rpx;
	}

	.content text:nth-child(1) {
		color: #AAAAAA;
	}

	.content text:nth-child(2) {
		margin-left: 48rpx;
		color: #333333;
	}

	.margin {
		width: 100%;
		height: 12rpx;
		background: #F9F9F9;
	}

	.tab-title1 {
		margin: 12rpx 0;
		color: #c59f79;
		font-size: 35rpx;
		height: 30rpx;
		font-weight: bold;
		box-shadow: 0rpx -20rpx 0rpx 0rpx #ff0000,

	}
	.tab-title {
		margin: 12rpx 0;
		color: #c59f79;
		font-size: 35rpx;
		height: 50rpx;
		font-weight: bold;
		
	}
	
	>>>.gui-scroll-x-items {
		align-items: center;
	}
	
	.demo-nav {
		padding: 15rpx 30rpx;
	}
	
	.demo-text {
		line-height: 200rpx;
		padding-bottom: 3000px;
	}
	
	.grace-box-banner .garce-items .line2 {
		font-size: 32rpx;
		color: #008AFF;
		border: 1px solid #62dbff;
		padding: 0 15rpx;
		margin: 0 10rpx;
		border-radius: 10rpx;
	}
	
	.line2.active {
		color: #ffffff !important;
		/* font-weight:bold; */
		background-color: #008AFF;
	}
	
	.l-timeline-l {
		border-left: 2px solid #008AFF;
	}
	
	.l-timeline-b {
		border-bottom: 2px solid #008AFF;
	}
	
	.l-time {
		position: relative;
		top: -15rpx;
	}
	.acolor{
		background-color: #7784eb;
			
	}
	.bcolor{
		background-color: #7784eb;
		    height: 40rpx;
		    line-height: 40rpx;	
			width: 190rpx;
	}
	
	.l-icon {
		background: #008AFF;
		width: 30rpx;
		height: 30rpx;
		border-radius: 30rpx;
		position: relative;
		top: -50rpx;
		left: -18rpx;
	}
	
	.l-content {
		position: relative;
		top: -25rpx;
	}
	
	.gui-accordion-icon {
		width: 50rpx;
		height: 80rpx;
		line-height: 80rpx;
		font-size: 32rpx;
	}
	.gui-flex-direction-row{
		flex-direction: row-reverse;
	}
	
	.gui-accordion-title-text {
		width: 200rpx;
		flex: 1;
	}
	>>>.gui-block-text{
		font-size: 30rpx !important;
	}
	.fs-28.gui-color-white{
		background-color: #c59f79;
		color:  #c59f79;
	}
	.w-70.fs-30{
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;

	}
	.text-ov{
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
	.fs-28.bcolor.gui-color-white{
		margin-left: 15rpx;
	}
	.mt-20{
		margin-left: 10rpx;
	}
	>>>.fs-28.bcolor.gui-color-white{
		width: 150rpx;
		padding-left: 8rpx;
		padding-right: 8rpx;
	}
</style>
