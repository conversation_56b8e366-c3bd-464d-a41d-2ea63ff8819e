<template>
	<gui-page :customHeader="true" statusBarStyle="background-color:#C59F79;" headerStyle="background-color:#C59F79;">
		<view slot="gHeader">
			<view class="bg-zhuti w-100 bgs gui-flex" style="color: #E8E8E8;padding-bottom:15rpx; " @tap="inDetail()">
				<view class="logosize">
					<image src="https://img.starup.net.cn/xmkj/zwb/img/zyLogo.jpg"
						style="width: 100%;height: 100%; border-radius: 30rpx;" mode=""></image>
				</view>
				<view
					style="margin-left: 20rpx; font-size: 20rpx;display: flex;align-content: center;align-items: center;">
				</view>
			</view>
		</view>
		<!-- 页面主体 -->
		<view slot="gBody">
			<view class="head_A">
				<view class="report_head" @click="$common.navTo('/pages/children/index?typaId=' + 2)">
					<view class="" v-if="Ainfo.name">
						{{ Ainfo.name || '-' }}({{Ainfo.sex ==1?'男' : Ainfo.sex ==2?'女':'' }})
					</view>
					<view class="hx" />
					<view class="head_img">
						<image src="../../../static/empty_avatar.png" mode=""></image>
					</view>
					<view class="hx" />
					<view class="" v-if="Ainfo.age">
						{{ Ainfo.age || '-' }}
						<text class="gui-icons ml-20 gui-color-blue">&#xe69e;</text>
					</view>
				</view>
			</view>
			<view class="head_B" v-if="Ainfo.measureDate !==''">
				记录日期：{{ Ainfo.measureDate && $common.parseTime(Ainfo.measureDate,'{y}-{m}-{d}') || '-' }}
			</view>
			<view class="head_C gui-flex gui-align-items-center gui-border-radius" style="width: 90%;">
				<view style="width: 65%;" class=" gui-flex gui-space-around" @click="toadd(index)" v-for="(item,index) in info" :key="index">
						<view>
							<text class="head_C_value">{{ item.value || '-' }}</text>
							<!--  <span class="Ahead_C_value" v-if="index === 0 && Ainfo.heightState">
				  {{
				    Ainfo.heightState === -1 && '偏矮' ||
				    Ainfo.heightState === 0 && '正常' ||
				    Ainfo.heightState === 1 && '良好' ||
				    Ainfo.heightState === 2 && '偏高'
				  }}
				</span> -->
							<view class="mt-15 head_C_tile">
								{{ item.tile || '-' }}
							</view>
						</view>
					</view>
				<view style="width: 35%;" class="gui-flex fs-26 gui-align-items-center" @click="$common.navTo('/pages/children/report')">评估详情 <text class="gui-icons">&#xe601;</text> </view>
			</view>
			<view class="view_block mt-20">
				<view class="title gui-flex gui-space-between">
					<view class="">
						<text>身高(cm)</text>
						<!-- <image class="gui-icons" src="../../../static/log/bx.png"></image> -->
						<text class="font-small gui-color-blue" @click="$common.navTo('/pages/children/add')"><text class="gui-icons">&#xe6c7;</text>添加记录</text>
					</view>
					<view class="gui-color-blue font-small" @click="$common.navTo('/pages/children/list')">更多记录<text class="gui-icons">&#xe601;</text> </view>
					<!-- <text class="font-small" style="float: right;" @click="jilu(1)">记录></text> -->
				</view>
				<view class="charts-box pt-20">
					<qiun-data-charts type="line" :opts="opts" :chartData="chartData" />
				</view>
			</view>
			<view class="view_block">
				<view class="title">
					<text>体重(kg)</text>
				</view>
				<view class="charts-box">
					<qiun-data-charts type="line" :opts="opts" :chartData="tizData" />
				</view>
			</view>
		</view>
	</gui-page>
</template>

<script>
	import {
		getAssessment,getHeightList
	} from '@/api/children.js'
	export default {
		data() {
			return {
				datacomLoading:false,
				Atype: uni.getStorageSync("type") ? uni.getStorageSync("type") : 1,
				// sex: undefined,
				Ainfo: {
					name: '',
					measureDate:''
					// nianl: 1,
					// -2:较矮 -1:偏矮 0:正常 1:良好 2:偏高
					// heightState: ''
				},
				// measureDate: '',
				// nianl: 2,
				info: [{
						value: '',
						tile: '身高cm',
						id: 1
					},
					{
						value: '',
						tile: '体重kg',
						id: 2
					}
				],
				chartData: {
					categories: [],
					series: [{
						name: "",
						data: []
					}]
				},
				tizData: {
					categories: [],
					series: [{
						name: "",
						data: []
					}]
				},
				//您可以通过修改 config-ucharts.js 文件中下标为 ['line'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
				opts: {
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [15, 10, 0, 15],
					// enableScroll:true,
					// title: {
					// 	name: "压力历史数据曲线图",
					// 	fontSize: 25,
					// 	color: "#333333",
					// 	offsetY: 0
					// },
					legend: {
						show: false
					},
					xAxis: {
						disableGrid: false,
					},
					yAxis: {
						gridType: "dash",
						dashLength: 2,
						// showTitle:true,
						disabled: false,
					},
					extra: {
						line: {
							type: "curve",
							width: 2
						}
					}
				}
			}
		},
		props: {
			isRefresh: {
				type: Number,
				default: 0
			}
		},
		watch: {
			isRefresh: {
				deep: true,
				handler(newValue, oldValue) {
					console.info("子组件更新")
					if (newValue != oldValue) {
						//子组件中监控值变化做 相关业务功能
						//todo
						this.getAssessment()
					}
				}
			}
		},
		mounted() {
			this.$forceUpdate()
			this.getAssessment()
		},
		methods: {
			//获取用户评估档案
			getAssessment() {
				getAssessment(uni.getStorageSync("cardObj").patientId).then(res=>{
					//判断是否填数据
					// if (res.data == null) {
					// 	uni.reLaunch({
					// 		url: '/pages/children/index'
					// 	})
					// 	return
					// } 
					this.getServerData()
					uni.setStorageSync('legao', res.data)
					this.Ainfo = res.data
					this.info[0].value = res.data.height
					this.info[1].value = res.data.weight
					// this.measureDate = res.data.measureDate
					// this.Ainfo.nianl = res.data.age
					// this.Ainfo.heightState = res.data.heightState
					// this.sex = res.data.sex ==1 ? '男':'女'
				})
			},
			inDetail() {},
			getServerData() {
				//模拟从服务器获取数据时的延时 getHeightList
				this.datacomLoading = true
				getHeightList({
					patientId: uni.getStorageSync("cardObj").patientId || ''
				}).then(res=>{
					const data = res.data
					const xData = data.xData
					const yData = data.yData
					/* 过滤数组 */
					//身高数据
					this.chartData = JSON.parse(JSON.stringify({
						categories: xData,
						series: [{
							name: "身高",
							data: yData
						}]
					}));
					//体重数据
					this.tizData = JSON.parse(JSON.stringify({
						categories: xData,
						series: [{
							name: "体重",
							data: data.tzyData
						}]
					}));
				})
			},
			//查看评估详情
			// toReport(){
			// 	this.$common.navTo('/pages/children/report')
			// },
			//添加身高
			// add() {
			// 	this.$common.navTo('/pages/children/add')
			// },
			toadd(e) {
				if (e == 0) {
					this.$common.navTo('/pages/children/report')
				}

			},
			// jilu() {
			// 	this.$common.navTo('/pages/children/list')
			// }
		}
	}
</script>
<style lang="less">
	.top-radius {
		border-top-left-radius: 40rpx;
		border-top-right-radius: 40rpx;
	}
	.view_block {
		background-color: #fff;
		padding: 16rpx 20rpx 10rpx 20rpx;
		border-radius: 20rpx;
		margin-bottom: 40rpx;

		.title {
			margin: 10rpx 10rpx;

			text:nth-child(1) {
				// background-color: red;
				font-size: 20rpx;
				margin-left: 15rpx;
				padding: 0rpx 10rpx;
			}

			image {
				width: 30rpx;
				height: 30rpx;
				margin: 0rpx 10rpx;
			}

			.font-small {
				// background-color: pink;
				font-size: 28rpx;
				color: #3c3c3c;
				padding: 0rpx 10rpx;
			}
		}


	}

	.head_A {
		width: 100%;
		height: 300rpx;
		margin: 0 auto;
		display: flex;
		justify-content: center;
		color: white;
		background-color: #C59F79;
		margin-top: -1px;
		// background: url("https://img.starup.net.cn/xmkj/zwb/img/3.png") no-repeat no-repeat;
		// background-size: 100% 100%;
		// background-position: 50% 50%;

		.report_head {
			display: flex;
			flex-wrap: nowrap;
			text-align: center;
			align-items: center;
			justify-content: center; // padding: 20rpx 10rpx;
			height: 120rpx;
			margin: 10rpx 10rpx;
			font-size: 36rpx;

			.hx {
				width: 50rpx;
				display: block;
				height: 3rpx;
				margin: 0px 15rpx;
				font-size: 30rpx !important;
				background-color: #ffffff;
				line-height: 50rpx;
			}

			.head_img {
				display: block;
				height: 100rpx;
				width: 100rpx;
				color: white;

				image {
					background-image: none;
					max-width: 100rpx;
					max-height: 100rpx;
				}
			}
		}
	}

	.head_B {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: -23%;
		color: white;
	}

	.head_C {
		margin-top: 5% !important;
		width: 90%;
		height: 200rpx;
		background-color: #ffffff;
		margin: 0 auto;
		box-shadow: 1px 2px 4px #747474;
		border-bottom: 1px solid #7d7d7d;
		// display: flex;
		// flex-direction: row;
		// justify-content: space-around;
		// align-items: center;

		.head_C_value {
			font-size: 34rpx;
			font-weight: bold;
		}

		.Ahead_C_value {
			width: 80rpx;
			font-size: 24rpx;
			height: 30rpx;
			text-align: center;
			margin-left: 5rpx;
			display: inline-block;
			border-radius: 50rpx;
			background-color: aqua;
			top: -50rpx;
		}

		.head_C_tile {
			font-size: 24rpx;
			text-align: center;
		}
	}

	.charts-box {
		width: 100%;
		height: 300px;
	}
</style>
