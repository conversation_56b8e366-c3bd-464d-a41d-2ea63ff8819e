<template>
	<gui-page  :isLoading="pageLoading">
		<!-- <view slot="gHeader">
			<view class="bg-zhuti w-100 bgs gui-flex" style="color: #E8E8E8;padding-bottom:15rpx; " @tap="inDetail()">
				<view class="logosize">
					<image src="https://img.starup.net.cn/xmkj/zwb/img/zyLogo.jpg"
						style="width: 100%;height: 100%; border-radius: 30rpx;" mode=""></image>
				</view>
				<view
					style="margin-left: 20rpx; font-size: 20rpx;display: flex;align-content: center;align-items: center;">
				</view>
			</view>
		</view> -->
		<view slot="gBody">
			<!-- 备孕人群 -->
			<!-- <pregnancy :isRefresh='isRefresh' v-if="role== '9'"></pregnancy> -->

			<!-- 糖尿病人群  版本升级-->
			 <sugar :isRefresh="isRefresh"></sugar>
			<!-- 儿童人群 -->
			<!-- <legaoindex @out="out" v-if="showlegao == 3 "></legaoindex> -->
			<!-- <lego :isRefresh='isRefresh' v-if=" role== '5' "></lego> -->
		</view>
	</gui-page>
</template>

<script>
	import sugar from "./component/sugar.vue"
	import pregnancy from "./component/pregnancy.vue"
	import lego from "./component/lego.vue"
	// import legaoindex from "@/pages/children/index.vue"
	import { getAssessment } from '@/api/children.js'
	export default {
		components:{sugar,pregnancy,lego},
		data() {
			return {
				domainType:this.$common.domainType,
				// showlegao:uni.getStorageSync("showlegao")?uni.getStorageSync("showlegao"):0,
				guimodal:true,
				isRefresh: 1,
				showlegao:3,
				// role:'0',//人群类型
				role:'2',
				roleLabel:'',//人群标签
				pageLoading: false,
				timeData:''
			};
		},
		onLoad() {
			// this.$common.role();
			// 添加事件监听，用于更新血糖数据
			uni.$on('update-health-data', () => {
				this.isRefresh += 1;
			})
		},
		onUnload() {
			// 移除事件监听，防止内存泄漏
			uni.$off('update-health-data');
		},
		onShow(){
			if(uni.getStorageSync('showlegao')){
				this.pageLoading = true
				this.role = 0
				uni.removeStorage({
					key:'showlegao'
				})
			}
			console.log('父组件===onShow')
			 //控制属性值改变触发子组件刷新
			this.getAssessment();
		},
		methods: {
			// close1 : function () {
			// 	this.$refs.guimodal1.close();
			// },
			// confirm1 : function () {
			// 	// 客户点击确认按钮后的逻辑请在此处实现
			// 	this.$refs.guimodal1.close();
			// },
			//获取用户评估档案
			getAssessment() {
				if (!uni.getStorageSync("cardObj")) {
					uni.showModal({
						title: '温馨提示',
						content: '您还未绑定就诊卡，请前往个人中心绑定',
						confirmText: '确定',
						confirmColor: '#576B95',
						success: function(res) {
							if (res.confirm) {
								uni.switchTab({
									url: '/pages/my/index'
								})
							} else if (res.cancel) {
								uni.switchTab({
									url: '/pages/home/<USER>'
								})
							}
						}
					});
					return
				}
				getAssessment(uni.getStorageSync("cardObj").patientId).then(res => {
					//判断是否填数据
					// if (res.data == null && uni.getStorageSync("cardObj").crowdType == '5') {
					// 	setTimeout(() => {
					// 		uni.reLaunch({
					// 			url: '/pages/children/index'
					// 		})
					// 		this.pageLoading = false;
					// 	}, 300)
					// 	return
					// }

					this.role = uni.getStorageSync("cardObj").crowdType;
					this.roleLabel = uni.getStorageSync("cardObj").crowdLabel;
					this.isRefresh = this.isRefresh + 1;
					 // if (this.role != '5' && !this.roleLabel?.match('a1d148f339f96248c3b976a3bd4c229a') ) {
					 // 	uni.showModal({
					 // 		title: '温馨提示',
					 // 		content: '您还未加入人群组，请与所属医生联系',
					 // 		confirmText: '已知晓',
					 // 		confirmColor: '#576B95',
					 // 		showCancel:false,
					 // 		success: function(res) {
					 // 			if (res.confirm) {
					 // 				uni.switchTab({
					 // 					url: '/pages/home/<USER>'
					 // 				})
					 // 			}
					 // 		}
					 // 	});
					 // }

					setTimeout(() => {
						this.pageLoading = false;
					}, 300)
				})
			},
			out(e){
				this.$set(this,'showlegao',2)
			}
		}
	}
</script>

<style>

</style>
