<template>
	<view>
		<!-- 头部 -->
		<view class="device_top">
			<view class="p-20 btnDevice">
				<view class="deviceBtn" style="margin-top: 60rpx; margin-right: 20rpx">
					<view class="" style="float: left;display: block;">+</view>
					<view class="pl-10 " style="float: left; " @click="addDevice('4')">添加设备</view>
				</view>
			</view>
			<view class="deviceAll">
				<view class="font-bold fs-30" style="color:#aa8f6d;">
					{{name}}&ensp;的设备
				</view>
				<br>
				<view class="mt-10" style="color:#b89b76;">
					共{{deviceList.length}}个设备
					<!-- 共3个设备 -->
				</view>
			</view>
		</view>
		<!-- 底部 -->
		<view class="gui-list gui-margin-top">
			<view class="gui-list-items" v-for="(item, index) in deviceList" :key="index">
				<view class="gui-list-body gui-border-b">
					<view class="gui-list-title">
						<text class="gui-list-title-text gui-primary-color">{{index + 1 }}.设备名称:{{item.iotName}}</text>
						<text class="gui-list-title-desc gui-color-gray"
							style="position: absolute;right: 30px;margin-top: 30rpx;font-size: 27rpx;color: #2665ee !important;text-decoration:underline;"
							@click="deviceTails(item.iotSn)">解绑</text>
					</view>
					<text class="gui-list-body-desc gui-color-gray">设备编号:{{item.iotSn}}</text>
				</view>
				<!-- <text class="gui-list-arrow-right gui-icons gui-color-gray fs-32">&#xe601;</text> -->
			</view>
		</view>
	</view>
</template>

<script>
	import {getDeviceDetailList} from '@/api/index.js'
	import {relieveDevice} from '@/api/diabetes.js'
	export default {
		data() {
			return {
				name: '',
				deviceList: [],
				patientInfo: uni.getStorageSync("cardObj"),
			}
		},
		onLoad(option) {
			this.name = option.name
			this.getDeviceDetailList()
		},
		onShow() {
			this.getDeviceDetailList()
		},
		methods: {
			// 患者设备列表
			getDeviceDetailList() {
				getDeviceDetailList(this.patientInfo.idCard).then(res=>{
					this.$set(this, 'deviceList', res?.data || [])
				})
				// this.$common.RequestData({
				// 	url: this.$common.getDeviceDetailList + this.patientInfo
				// 		.idCard, // idCard 患者身份证参数（123为测试参数）
				// 	method: 'GET'
				// }, res => {
					
				// })				
			},
			addDevice(type) {
				uni.navigateTo({
					url: `/pages/diabetes/index?type=${type}`
				})
			},
			deviceTails(type) {							
				this.$common.model("提示", "确定删除此设备？", resx => {
					if (resx.confirm) {
						this.relieveDevice(type)
					} else if (!resx.confirm) {
					}
				})
			},
			relieveDevice(e) {		
				relieveDevice({
					addUser: this.patientInfo.idCard,
					iotSn: e
				}).then(res=>{
					if (res.code === 200) {
						this.$common.msg("设备解绑成功")						
						this.getDeviceDetailList()
					}
				})
				// this.$common.RequestData({
				// 	url: this.$common.relieveDevice, // idCard 患者身份证参数（123为测试参数）
				// 	data: {
						
				// 	},
				// 	method: 'POST'
				// }, res => {
					
				// })
			}
		}
	}
</script>

<style scoped>
	>>>.gui-list-arrow-right {
		font-size: 40rpx !important;
	}

	.device_top {
		height: 250rpx;
		display: block;
		/* background-color: #C59F79; */
		/* url 是bares64的编码 本地的没反应 */
		/* https://img.starup.net.cn/xmkj/zwb/img/zy_pre_cardbg.png */
		 background-image: url("https://img.starup.net.cn/xmkj/zwb/img/zy_pre_cardbg.png");
		background-repeat: no-repeat;
		background-position: center;
		background-size: cover;
	}

	.btnDevice {
		display: flex;
		justify-content: flex-end;
		flex-direction: row;
		margin-left: 20rpx;
	}

	.deviceBtn {
		background: #e7bb8e;
		color:#7a684e;
		  /*  background: rgba(255,255,255,0.5);
		    color: #88735E; */
		/* background-color: #3688FF; */
		padding: 15rpx 20rpx;		
		line-height: 30rpx;
		border-radius: 10rpx;
		font-size: 30rpx;
		margin-left: 30rpx;
	}

	.deviceList {
		margin-top: 20rpx;
		display: flex;
		width: 100%;
		flex-wrap: wrap;
		/* justify-content: space-between; */
	}

	.deviceImg {
		display: block;
	}

	.deviceImg image {
		display: block;
		border-radius: 5rpx;
		margin: 5rpx 5rpx 10rpx 5rpx;
	}

	.bg-black-tm {
		background: #efeeea;
	}

	.grace-border-radius-small {
		border-radius: 30rpx;
	}

	.menu-btn {
		position: relative;
		right: 0;
	}

	.menu-box {
		position: absolute;
		z-index: 999;
		width: 300rpx;
		height: auto;
		top: 60rpx;
		right: 0;
	}

	.menu-item {
		height: 100rpx;
		line-height: 100rpx;
		font-size: 32rpx;
		font-weight: bold;
		border-bottom: 1rpx solid #eee;
	}

	.menu-item:last-child {
		border-bottom: 0 solid #eee;
	}

	.grace-border {
		border: 1px solid #ece5e5;
	}

	/* 共用设备 */
	.deviceAll {
		display: block;
		position: absolute;
		top: 80rpx;
		left: 30rpx;
		color:#7a684e;
	}
</style>
