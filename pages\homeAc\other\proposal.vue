<template>
	<view>
		<view v-for="(item,index) in list" :key="index" class="shadow" :class="index%2 != 0 ? 'bor2' : 'bor'"
			@tap="detail(item)">
			<image
				:src="index%2 != 0 ? 'https://img.starup.net.cn/xmkj/zwb/img/zy_car_ydjy.png' : 'https://img.starup.net.cn/xmkj/zwb/img/zy_car_jkjy.png'"
				mode="widthFix" class="w80 h80 mr-30"></image>
			<!-- <text class="flex-1 ellipsis-1 text-left fs-36 ">{{item.title}}</text> -->
			<view>
				<view class="flex-1 ellipsis-1 text-left fs-36 " style="line-height: 100rpx;">{{item.templateDictKey=='32'?'体质评估及健康指导手册':item.title}}</view>
				<view class="fs-26 text-left">更新时间：{{$common.parseTime(item.createTime,'{y}-{m}-{d} {h}:{i}') ||''}}</view>
			</view>
		</view>
		<view v-if="list.length <= 0">
			<gui-empty>
				<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
					<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
				</view>
				<text slot="text" class="gui-text-small gui-block-text gui-text-center gui-margin-top"
					style="color:#B2B2B2;">暂无数据</text>
			</gui-empty>
		</view>
		<view class="h50"></view>
	</view>
</template>

<script>
	import {
		heathList,
		getNutritionInterventionReport,
		previewNutritionInterventionPDF
	} from '@/api/home.js'
	export default {
		data() {
			return {
				list: []
			}
		},
		onShow() {
			this.getList();
		},
		methods: {
			getList() {
				uni.showLoading({
					title: "加载中..."
				})
				heathList({
					patientId: uni.getStorageSync('cardObj').patientId
				}).then(res => {
					uni.hideLoading();
					this.list = res.data;
				})
				// this.$common.RequestData({
				// 	url:this.$common.heathList,
				// 	data:{ },
				// 	method:"get"
				// },res=>{

				// })
			},
			// 膳食建议
			// detail(item) {
			// 	let id = uni.getStorageSync('cardObj').patientId;
			// 	this.$common.navTo('/pages/homeAc/other/salDetail?templateDictKey=' + item.templateDictKey +
			// 		'&patientId=' + id + "&title=" + item.title + '&templateId=' + item.templateId +
			// 		'&visitRecordId=' + item.visitRecordId + '&createTime=' + item.createTime + '&programmeType=' +
			// 		item.programmeType + '&createTimeFilter=' + item.createTime)
			// }
			// 膳食建议
				detail(item) {
					console.log('点击事件触发，item:', item);
					console.log('当前平台:', uni.getSystemInfoSync().platform);

					if (item.templateDictKey == '36' || item.templateDictKey == '36') {
						if (item.fileData) {
							console.log('已有文件数据，直接打开PDF')
							this.openPDF(item.wpId,item.templateDictKey)
						} else{
							console.log('没有文件数据，开始生成PDF')
							this.getFileData(item.wpId,item.visitRecordId,item.templateDictKey);
						}
					} else{
						let id = uni.getStorageSync('cardObj').patientId;
						this.$common.navTo('/pages/homeAc/other/salDetail?templateDictKey='+item.templateDictKey+'&patientId='+id + "&title="+item.title + '&templateId=' + item.templateId + '&visitRecordId=' + item.visitRecordId+'&createTime='+item.createTime+'&programmeType='+item.programmeType)
					}

				},
				async getFileData(wpId,visitRecordId,templateDictKey){
					console.log('开始生成PDF，参数:', {wpId, visitRecordId, templateDictKey});

					uni.showLoading({
						title: "报告生成中...",
					});

					try {
						var res = ''
						if(templateDictKey == 36){
							console.log('调用营养干预PDF预览API');
							res = await previewNutritionInterventionPDF({
								patientId: uni.getStorageSync('cardObj').patientId,
								visitId: visitRecordId
							});
							console.log('API返回结果:', res);
						}
						// else{
						// 	res = await previewPDF({patientId : uni.getStorageSync('cardObj').patientId,visitRecordId:visitRecordId})
						// };

						uni.hideLoading();

						if(res && res.code == 200){
							console.log('PDF生成成功，开始打开');
							this.openPDF(wpId,templateDictKey)
						} else {
							console.error('PDF生成失败:', res);
							this.$common.msg(res?.msg || '报告生成失败，请联系医生。');
						}
					} catch (error) {
						console.error('PDF生成异常:', error);
						uni.hideLoading();
						this.$common.msg('网络异常，请稍后重试');
					}
				},
				// 查看PDF报告
				openPDF(wpId,templateDictKey) {
					console.log('开始打开PDF，参数:', {wpId, templateDictKey});
					console.log('当前域名类型:', this.$common.domainType);
					console.log('当前域名:', this.$common.domain);

					let url = ''
					if (templateDictKey == 36) {
						url = this.$common.domain+'/wx/heath/nutritionIntervention/getNutritionInterventionReport'
					}

					const platform = uni.getSystemInfoSync().platform;
					console.log('当前平台:', platform);
					console.log('基础URL:', url);

				  switch (platform) {
				    case "android":
				      console.log("安卓平台，使用原生PDF打开");
				      this.androidOpenPdf(url+'?id='+wpId+'&token='+uni.getStorageSync("token"));
				      break;
				    case "ios":
				      console.log("iOS平台，使用webview打开");

					  // 根据不同域名类型设置URL
					  if (this.$common.domainType == 9) {//兴安界首特殊处理
					  	url = templateDictKey == 36 ? this.$common.pdfUrl+'/wx/heath/nutritionIntervention/getNutritionInterventionReport' : ''
					  } else if (this.$common.domainType == 4) {//南中医特殊处理
					  	url = templateDictKey == 36 ? 'https://zwb.frp.starup.net.cn/nzypdf/prod-api/wx/heath/nutritionIntervention/getNutritionInterventionReport' : ''
					  } else if (this.$common.domainType == 8) {//周口中医特殊处理
					  	url = templateDictKey == 36 ? this.$common.pdfUrl+'/wx/heath/nutritionIntervention/getNutritionInterventionReport' : ''
					  }

					  const finalUrl = "/pages/homeAc/component/openH5?openUrl=" + url + "&openType=2&reportId=" + wpId;
					  console.log('iOS跳转URL:', finalUrl);

				      uni.navigateTo({
				        url: finalUrl,
				        fail: (err) => {
				          console.error('页面跳转失败:', err);
				          this.$common.msg('页面跳转失败，请重试');
				        }
				      });
				      break;
				    default:
				      console.log("其他平台，使用安卓方式");
				      this.androidOpenPdf(url+'?id='+wpId+'&token='+uni.getStorageSync("token"));
				      break;
				  }
				},
				androidOpenPdf(url) {
				  uni.downloadFile({
				    url: url,
				    success: function (res) {
				      var filePath = res.tempFilePath;
				      uni.openDocument({
				        filePath: filePath,
						fileType: 'pdf',
				        success: function (res) {
				          // console.log("打开文档成功");
				        },
				      });
				    },
				  });
				}
			}
		}
</script>

<style>
	.bor {
		text-align: center;
		padding: 20rpx 30rpx;
		display: flex;
		align-items: center;
		background: #A18E7A;
		/* line-height: 100rpx; */
		margin: 40rpx 30rpx;
		color: #fff;
		border-radius: 10rpx;
	}

	.bor2 {
		text-align: center;
		padding: 20rpx 30rpx;
		display: flex;
		align-items: center;
		background: #5D808F;
		/* line-height: 100rpx; */
		margin: 40rpx 30rpx;
		color: #fff;
		border-radius: 10rpx;
	}
</style>