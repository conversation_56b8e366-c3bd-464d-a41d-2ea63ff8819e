<template>
	<view class="p-30">
		<!-- 检查结果 -->
		<view class="boxs" v-if="type == 1">
			<view>检查项目：{{obj.applyItems || '-'}}</view>
			<view>就诊人：{{obj.patientName || '-'}}</view>
			<view>年龄：{{obj.age || '-'}}岁</view>
			<view>检查时间：{{obj.executeTime || '-'}}</view>
			<view>检查科室：{{obj.applyDept || '-'}}</view>
			<view>检查医生：{{obj.applyDoctor || '-'}}</view>
			<view>检查所见：{{obj.observation || '-'}}</view>
			<view>临床诊断：{{obj.diagnosis || '-'}}</view>
			<view>检查报告图片：</view>
			<view v-if="obj.imagUrls.length > 0">
				<image v-for="(item,index) in obj.imagUrls" :key="index" :src="item" mode="widthFix" class="w-100 mb-20"></image>
			</view>
			<view v-else class="text-center text-grey-74">暂无图片~</view>
			<view>报告人：{{obj.reportor || '-'}}</view>
			<view>报告时间：{{obj.reportTime || '-'}}</view>
		</view>
		<!-- 检验结果 -->
		<view class="boxs" v-if="type == 2">
			<view>项目：{{list[0].applyItems || '-'}}</view>
			<view>就诊人：{{list[0].patientName || '-'}}</view>
			<view>年龄：{{list[0].age || '-'}}岁</view>
			<view>科室：{{list[0].applyDept || '-'}}</view>
			<view>医生：{{list[0].applyDoctor || '-'}}</view>
			
			<!-- 检验结果列表 -->
			<view class="gui-padding">
				<view class="gui-table">
					<view class="gui-theader gui-flex gui-rows gui-nowrap bgd bor">
						<text class="gui-td gui-border-r gui-border-b gui-td-text gui-bold gui-text-center">项目</text>
						<text class="gui-td gui-border-r gui-border-b gui-td-text gui-bold gui-text-center">检验结果</text>
						<text class="gui-td gui-border-r gui-border-b gui-td-text gui-bold gui-text-center">参考值</text>
					</view>
					<view v-for="(item,index) in list" :key="index"
						class="gui-tbody gui-flex gui-rows gui-nowrap bgd2 bor">
						<text class="gui-td2 gui-text-center">{{item.applyItems}}</text>
						<text class="gui-td2 gui-text-center">{{item.itemResult}}</text>
						<text class="gui-td2 gui-text-center">{{item.itemValue}}</text>
					</view>
				</view>
			</view>
			
			<view>检验人：{{list[0].executor || '-'}}</view>
			<view>检验时间：{{list[0].executeTime || '-'}}</view>
			<view>报告人：{{list[0].reportor || '-'}}</view>
			<view>报告时间：{{list[0].reportTime || '-'}}</view>
		</view>
	</view>
</template>

<script>
	import {InspectResultInfo} from '@/api/home.js'
	export default {
		data() {
			return {
				id:"",
				type:"",
				obj:{
					imagUrls:[]
				},
				list:[]
			}
		},
		onLoad(op) {
			this.type = op.type;
			this.id = op.id;
			this.getDetail();
		},
		methods: {
			getDetail(){
				InspectResultInfo({
					barcode:this.id,
				},this.type).then(res=>{
					if(this.type == 1){
						res.data.imagUrls = res.data.imagUrls ? res.data.imagUrls.split(",") : [];
						this.obj = res.data;
					} else {
						this.list = res.rows;
					}
				})
				// this.$common.RequestData({
				// 	url:this.type == 1 ? this.$common.InspectResultInfo + this.id : this.$common.reportResultInfo,
				// 	data:{
				// 		barcode:this.id,
				// 	},
				// 	method:"get"
				// },res=>{
					
				// })
			}
		}
	}
</script>

<style scoped>
.boxs>view{
	padding: 20rpx 0;
}
	.gui-td {
		width: 100rpx;
		flex: 1;
		overflow: hidden;
		padding: 0 10rpx;
		display: flexbox;
	}

	.gui-td2 {
		width: 100rpx;
		flex: 1;
		overflow: hidden;
		padding: 21rpx 10rpx;
		display: flexbox;
		font-size: 28rpx;
		color: #333333;
	}

	.gui-td-text {
		line-height: 60rpx !important;
		font-size: 24rpx;
	}

	.bgd {
		background: #E3F3ED;
		color: #3DAB7E;
	}

	.bgd2 {
		background: #F9F9F9;
	}

	.bor text {
		border: 1px solid #FFFFFF;
		box-sizing: border-box;
	}
</style>
