<template>
	<view class="gui-margin">
		<!-- <view class="gui-margin-top-large"></view> -->
		<form @submit="submit" class="gui-padding ">
			<!-- 姓名 -->
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">姓名</text>
				<view class="gui-form-body">
					<input type="text" style="font-size: 32rpx;" class="gui-form-input" maxlength="10"
						v-model="formData.nickName" name="userName" placeholder="请输入姓名" />
				</view>
			</view>
			<!-- 身份证号 -->
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">身份证号</text>
				<view class="gui-form-body">
					<input type="text" style="font-size: 32rpx;" @input="onInput" class="gui-form-input" maxlength="18"
						v-model="formData.idCard" name="idCard" placeholder="请输入证件号码" />
				</view>
			</view>
			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label fs-30">性 别</text>
				<radio-group class="gui-flex gui-rows" @change="sexChange" v-model="formData.sex">
					<label class="gui-check-item gui-check-item-y">
						<radio color="#2DCF8C" value="1" :checked="formData.sex == 1"></radio>
						<text class="gui-text gui-primary-color">男</text>
					</label>
					<label class="gui-check-item gui-check-item-y">
						<radio color="#2DCF8C" value="2" :checked="formData.sex == 2"></radio>
						<text class="gui-text gui-primary-color">女</text>
					</label>
					<!--					<label class="gui-check-item gui-check-item-y">-->
					<!--						<radio color="#2DCF8C" value="3" :checked="formData.sex == 3"></radio>-->
					<!--						<text class="gui-text gui-primary-color">未知</text>-->
					<!--					</label>-->
				</radio-group>
			</view>

			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">出生日期</text>
				<view class="gui-form-body">
					<text v-show="datahin" style="color: #808080;font-size: 32rpx;">输入身份证获取</text>
					<text v-show="datashow" style="font-size: 32rpx;">
						{{$common.parseTime(formData.birthday,'{y}-{m}-{d}')}}
					</text>

				</view>
			</view>

			<view class="gui-form-item gui-border-b">
				<text class="gui-form-label">手机号</text>
				<view class="gui-form-body">
					<input type="number" style="font-size: 32rpx;" class="gui-form-input" maxlength="11"
						v-model="formData.phone" name="visitCard" placeholder="请输入手机号" />
				</view>
			</view>
			<view v-if="ysxy" class="gui-margin-top">
				<gui-radio :checked="isCheck" @change="radioChange">
					<text class="gui-text gui-primary-color">我已阅读并同意 </text><text class="gui-color-blue"
						@click="open">《用户授权与隐私保护协议》</text>
				</gui-radio>
			</view>
			<!-- 立即绑定 -->
			<view class="gui-flex gui-columns gui-justify-content-center" style="margin-top: 120px;">
				<button type="primary" formType="submit" class="bnt"
					style="height: 40px;line-height: 40px;font-size: 32rpx;background-color: #C59F79;">提交</button>
			</view>
			<!-- 弹窗 -->
			<gui-modal ref="guimodal" title="服务协议和隐私政策">
				<view slot="content" class="gui-padding gui-bg-white">
					<text class="gui-text" style="padding:10rpx;">{{textdata}}</text>
					<!--					<text class="gui-text gui-color-blue">用户协议、隐私政策、免责协议</text>-->
					<text class="gui-text">如您同意，请点击“同意”开始接受我们的服务。</text>
				</view>
				<!-- 利用 flex 布局 可以放置多个自定义按钮哦  -->
				<view slot="btns" class="gui-flex gui-rows gui-space-between">
					<view class="modal-btns gui-flex1" style="margin-right:80rpx;">
						<text class="modal-btns gui-color-gray" @tap="close">不同意</text>
					</view>
					<view class="modal-btns gui-flex1" style="margin-left:80rpx;">
						<text class="modal-btns gui-color-blue" @tap="confirm">同意</text>
					</view>
				</view>
			</gui-modal>
			<!-- <view  @tap="addAddress"  class="gui-flex gui-columns gui-justify-content-center" style="margin-top: 15px;">
			<button type="primary" class="bnt" style="border: 1px dashed #C59F79;background:#fbf7f5;color: #C59F79;height: 40px;line-height: 40px;font-size: 32rpx;">没有卡，在线申领</button>
		</view> -->
		</form>


	</view>
</template>

<script>
	import {
		appSetOff,
		upMember
	} from '@/api/my.js'
	export default {
		data() {
			return {
				ysxy: false,
				textdata: '请您充分理解“服务协议”和“隐私政策”各条款，为了向您提供健康管理和宣教等服务，我们需要收集您的身份证等个人信息。您可以在“我的”个人信息中查看、变更个人信息。',
				isCheck: false, //是否勾选协议
				formData: {
					nickName: '',
					idCard: '',
					sex: '',
					birthday: '',
					phone: ''
				},
				datashow: false,
				datahin: true,
				my: ''
			}
		},
		onLoad(option) {
			if (option.my) {
				this.my = option.my
			}
			this.getUserInfo();
			this.appSetOff();
		},
		methods: {
			//隐私政策 显示开关配置
			appSetOff() {
				appSetOff().then(res => {
					this.ysxy = res.data;
				})
			},
			radioChange: function(e) {
				this.isCheck = e[0];
			},
			open: function() {
				this.$refs.guimodal.open();
			},
			close: function() {
				this.isCheck = false;
				this.$refs.guimodal.close();
			},
			confirm: function() {
				// 客户点击确认按钮后的逻辑请在此处实现
				this.isCheck = true;
				this.$refs.guimodal.close();
			},
			getUserInfo() {
				let user = uni.getStorageSync('user')
				this.formData.nickName = user.nickName
				this.formData.phone = user.phone || ''
				if (user.idCard || user.patientId) {
					this.formData = user
					this.formData.idCard = user.idCard || user.patientId
					this.onInput2(this.formData.idCard)
				}
			},
			//性别选择
			sexChange(e) {
				this.formData.sex = e.detail.value
			},
			// picker 切换
			pickerChange: function(e) {
				this.genderIndex = e.detail.value;
				this.formData.relationship = this.gender[this.genderIndex];
			},
			onInput(e) {
				let birthday = null;
				if (e.detail != null) {
					birthday = e.detail.value.substr(6, 8).replace(/(.{4})(.{2})/, "$1-$2-");
					// console.log("出生日期", birthday)
					this.formData.birthday = birthday + ` 00:00:00`
					this.datashow = true
					this.datahin = false
					this.getAge(birthday)
				}
			},
			onInput2(e) {
				let birthday = null;
				if (e != null) {
					birthday = e.substr(6, 8).replace(/(.{4})(.{2})/, "$1-$2-");
					// console.log("出生日期", birthday)
					this.formData.birthday = birthday + ` 00:00:00`
					this.datashow = true
					this.datahin = false
					this.getAge(birthday)
				}
			},
			// 获取日期			
			getAge(strBirthday) {
				//strBirthday传入格式 2020-04-15
				let returnAge;
				let strBirthdayArr = strBirthday.split('-');
				let birthYear = strBirthdayArr[0];
				let birthMonth = strBirthdayArr[1];
				let birthDay = strBirthdayArr[2];
				//获取当前日期
				let d = new Date();
				let nowYear = d.getFullYear();
				let nowMonth = d.getMonth() + 1;
				let nowDay = d.getDate();
				if (nowYear == birthYear) {
					returnAge = 0; //同年 则为0岁
				} else {
					let ageDiff = nowYear - birthYear; //年之差
					if (ageDiff > 0) {
						if (nowMonth == birthMonth) {
							let dayDiff = nowDay - birthDay; //日之差
							if (dayDiff < 0) {
								returnAge = ageDiff - 1;
							} else {
								returnAge = ageDiff;
							}
						} else {
							let monthDiff = nowMonth - birthMonth; //月之差
							if (monthDiff < 0) {
								returnAge = ageDiff - 1;
							} else {
								returnAge = ageDiff;
							}
						}
					} else {
						returnAge = -1; //返回-1 表示出生日期输入错误 晚于今天
					}
				}
				// console.log("年龄", returnAge)
				// this.formData.birthday = returnAge
				return returnAge; //返回周岁年龄
			},
			submit: function(e) {
				this.formData.id = uni.getStorageSync('user').id
				if (!this.formData.idCard) {
					this.$common.msg('请输入身份证！')
					return
				}
				if (this.ysxy && this.isCheck != true) {
					return this.$common.msg('请勾选授权与隐私保护协议')
				}
				upMember(this.formData).then(res=>{
					if (res.code == '200') {
						uni.setStorageSync('user', res.data)
						if (this.my) {
							uni.switchTab({
								url: '/pages/my/index'
							})
						} else {
							uni.switchTab({
								url: '/pages/home/<USER>'
							})
						}
					
					}
				})
			}
		}
	}
</script>

<style>
	.bnt {
		width: 60%;
		height: 30px;
		font-size: 12px;
		margin: 5rpx auto;
	}

	.gui-form-label {
		width: 20%;
		font-size: 36rpx;
		padding-left: 15rpx;
	}

	.modal-btns {
		line-height: 88rpx;
		font-size: 26rpx;
		text-align: center;
		width: 200rpx;
	}

	.gui-form-body {
		width: 80%;
		font-size: 36rpx;
	}
</style>
