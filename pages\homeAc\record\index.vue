<template>
	<view class="gui-padding">
		<view class="m-20 pb-20">
			<view class="gui-flex gui-space-around gui-align-items-center" >
				<view @tap="val=>{navchange(val,index)}" :class="current == index? 'text-zhuti gui-bold fs-34':''" class=" py-10 px-20 fs-32" :style="current == index?'border-bottom: 2px solid #c59f79;':''" v-for="(item,index) in tagsType" :key="index">{{item.dictLabel}}</view>
			</view>
		</view>
		<view v-show="current == 0 ">
			<view v-if="healthyList.length" v-for="(item,index) in healthyList" :key="index">
				<view class="gui-relative gui-flex gui-rows gui-justify-content-end">
					<view class="gui-time-line-body gui-border-box gui-border-l">
						<view class="gui-flex gui-space-between gui-align-items-center" @tap="changeTo(index)">
							<view class="d-flex ai-center">
								<!-- <text class="gui-time-line-time fs-28 pr-30">就诊时间</text> -->
								<text class="gui-time-line-time fs-28 pr-30">{{item.typeStatus == 0?'指标异常评估':'阶段评估'}}</text>
								<gui-tags :margin="0" :text="item.createTime ? $common.parseTime(item.createTime,'{y}-{m}-{d}') : '-'" :size="20" bgClass="gui-bg-white" color="#9AA7B9" borderColor="#9AA7B9"></gui-tags>
							</view>
							<view class="gui-color-blue gui-flex gui-nowrap gui-align-items-center" >
								<text class="fs-28 pr-10">详情 </text>
								<text v-if="currentIndex != index" class="gui-icons fs-34">&#xe603;</text>
								<text v-else class="gui-icons fs-34">&#xe654;</text>
							</view>
						</view>
						<view v-if="currentIndex == index" class="gui-time-line-txt gui-border-radius-small gui-block-text d-flex text-white ai-center mt-10" style="background-color: #C59F79;">
							<view class="flex-1">
								<view class="font-bold">
									<view class="w-100">就诊人：{{item.patientName}}</view>
									<view class="w-100">医生姓名：{{item.doctorName || '-'}}</view>
									<view class="" v-if="item.typeStatus == 1">
										<view class="w-100" v-if="item. evaluationCycleStart&&item. evaluationCycleEnd">周期时间：{{item.evaluationCycleStart}} ~ {{item. evaluationCycleEnd}}</view>
										<view class="w-100" v-else>评估时间：{{$common.parseTime(item.createTime,'{y}-{m}-{d}') ||''}}</view>
										<view class="w-100">评估描述：{{item.resultDescription || '无'}}</view>
										<view class="w-100" v-if="item.evaluationType==1&&item.detailedEvaluationResults">评估效果：{{item.detailedEvaluationResults==1?'好转':item.detailedEvaluationResults==2?'一般':item.detailedEvaluationResults==3?'无进展':item.detailedEvaluationResults==4?'显著':item.detailedEvaluationResults==5?'痊愈':'无'}}</view>
										<view class="w-100" v-if="item.evaluationType==2">评估效果：{{item.detailedEvaluationResults + ' 分'}}</view>
										<!-- <view class="w-100">评估效果：{{item.evaluationType || '无'}}</view> -->
									</view>
									<view v-else class="">
										<view class="w-100">指标异常：{{item.measureResult || ''}}</view>
										<!-- <view class="w-100">综合评估：{{item.evaluate || ''}}</view>南宁中医院小程序才有指导意见和综合评估两个字段
										<view class="w-100">指导意见：{{item.direction || ''}}</view> -->
										<view class="w-100">处理结果：{{item.handleContent || ''}}</view>
									</view>
								</view>
								<!-- <view class="fs-24"><text class="gui-icons mr-15">&#xe63f;</text>{{item.visitType == 1 ? '治未病门诊' : '一般门诊'}}/医生：{{item.doctorName || '-'}}</view> -->
							</view>
						</view>
					</view>
					<text class="gui-left-block gui-time-line-icon gui-block-text gui-icons gui-color-white"></text>
				</view>
			</view>
			<view v-if="healthyList.length<= 0">
				<gui-empty>
					<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
						<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
					</view>
					<text slot="text"
					class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
				</gui-empty>
			</view>
		</view>
		<view v-show="current == 1">
			<view v-for="(item,index) in  list" :key="index" >
				<view class="gui-relative gui-flex gui-rows gui-justify-content-end" @tap="detail(item)">
					<view class="gui-time-line-body gui-border-box gui-border-l">
						<view class="d-flex ai-center">
							<text class="gui-time-line-time fs-28 pr-30">干预时间</text>
							<gui-tags :margin="0" :text="item.visitDate ? item.visitDate : '-'" :size="20" bgClass="gui-bg-white" color="#9AA7B9" borderColor="#9AA7B9"></gui-tags>
						</view>
						<view class="gui-time-line-txt gui-border-radius-small gui-block-text d-flex text-white ai-center mt-10" style="background-color: #C59F79;">
							<view class="flex-1">
								<view class="mb-10 font-bold">
									<view class="w-60">就诊人：{{cardObj.userName}}</view>
									<view class="w-40">报告数：{{item.visitType == 1 ? item.docNum : item.reportNum}}</view>
								</view>
								<view class="fs-24"><text class="gui-icons mr-15">&#xe63f;</text>{{item.deptName}}/医生：{{item.doctorName || '-'}}</view>
							</view>
							<view class="text-right">
								<text class="gui-icons fs-28">详情&#xe601;&#xe601;</text>
							</view>
						</view>
					</view>
					<text class="gui-left-block gui-time-line-icon gui-block-text gui-icons gui-color-white"></text>
				</view>
			</view>
			<view v-if="list && list.length<= 0">
				<gui-empty>
					<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
						<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
					</view>
					<text slot="text"
					class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
				</gui-empty>
			</view>
		</view>
		<view class="h50"></view>
	</view>
</template>
<script>
	import {healthList,queryEvaluateAndTask} from '@/api/home.js'
	export default {
		data() {
			return {
				cardObj: uni.getStorageSync('cardObj'),
				list:[],
				healthyList:[],
				currentIndex: 0,
				current:0,
				tagsType:[
					{dictLabel:"健康评估",dictCode:""},
					{dictLabel:"干预记录",dictCode:""}
				]
			}
		},
		onShow() {
			this.navchange(this.current);
			// this.getList();
		},
		methods: {
			changeTo(idx) {
				if (this.currentIndex == idx) {
					this.currentIndex = -1;
				} else {
					this.currentIndex = idx;
				}
			},
			navchange(val,index){
				this.current = index
				if (index == 1) {
					this.getList();
				} else{
					this.getHealth()
				}
			},
			getHealth(){
				queryEvaluateAndTask({
					patientId: this.cardObj.patientId,
				}).then(res =>{
					this.healthyList = res.data;
					this.$forceUpdate()
				})
			},
			getList(){
				healthList({
					patientId: this.cardObj.patientId,
					visitType:1,
				}).then(res=>{
					this.list = res.rows;
					this.$forceUpdate()
				})
			},
			// 记录详情
			detail(item) {
				if (item.visitType == 1) {
					// 治未病科
					this.$common.navTo('/pages/homeAc/record/notSickDetail?item='+encodeURIComponent(JSON.stringify(item)))
				} else {
					return
					// 一般门诊 2024-5-20 韦总决定只显示治未病科的数据即可
					this.$common.navTo('/pages/homeAc/record/commonlyDetail?item='+encodeURIComponent(JSON.stringify(item)))
				}
			}
		}
	}
</script>
<style>
	.gui-border-l {
		border-left: 1px solid #cdcdcd;
	}

	.gui-left-block {
		width: 20rpx;
		height: 20rpx;
		position: absolute;
		z-index: 2;
		left: 34rpx;
		top: 0;
		background: #C59F79;
	}

	.gui-time-line-icon {
		line-height: 80rpx;
		text-align: center;
		font-size: 32rpx;
		border-radius: 80rpx;
	}

	.gui-time-line-img {
		width: 80rpx;
		height: 80rpx;
		border-radius: 80rpx;
	}

	.gui-time-line-body {
		width: 650rpx;
		font-size: 0;
		padding-bottom: 30rpx;
		padding-left: 50rpx;
	}

	.gui-time-line-txt {
		line-height: 50rpx;
		font-size: 28rpx;
		padding: 30rpx;
	}

	.gui-time-line-time {
		line-height: 36rpx;
		font-size: 28rpx;
		display: block;
		color: #333333;
		text-align: left;
		margin: 15rpx 0;
	}

	.gui-time-line-image {
		width: 600rpx;
	}
</style>
