<template>
	<view class="">
		<view style="width: 100%;">
			<view style="display: flex;">
				<view style="height: 20rpx;background-color: #e6e6e6; width: 25%;" v-for="(item, index) in 4" :style="index == 1?'background-color: #00aaff;':''" :class="index == 3?'':'mr-5'" :key="index"></view>
			</view>
			<view class="text-text" style="display: flex; align-items: center;margin-top: 10rpx;">
				<view class="text-text-item" style="width: 25%; display: flex;position: relative;" v-for="(item, index) in textList" :key="index">
					<view class="item" style="left: -20rpx;position: relative;" v-if="index == 0">{{item.start}}</view>
					<view style="font-size: 26rpx;" :style="index != 0?'padding-left: 30rpx':'padding-left: 10rpx'">{{item.value}}</view>
					<view class="item" style="right: -30rpx;position: relative;">{{item.end}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			// 当前值
			value: {
				type: Number,
				default: 21,
			},
			// 总值
			sumValue: {
				type: Number,
				default: 35,
			},
			// 进度条宽度
			storkeWidth: {
				type: Number,
				default: 10
			},
			// 圆角
			borderRadius: {
				type: Number,
				default: 15,
			},
			// 范围
			textList: {
				type: Array,
				default: [{start: '0', end: '3.9', value: '过低'}, {start: '3.9', end: '7.8', value: '健康'},{start: '7.8', end: '20', value: '超标'}, {start: '20', end: '35', value: '高危'}]
			},
		},
		computed: {
		},
		methods: {
		}
	}
</script>

<style lang="scss" scoped>
	.mr-5{
		margin-right: 5rpx;
	}
	.propress-container {
		width: 100%;
		position: relative;
		display: flex;
		flex-flow: column;
		
		.text {
			width: 100%;
			position: absolute;
			left: 0;
			top: 0;
			display: flex;
			align-items: center;
			
			.text-item {
				width: 5rpx;
				background-color: #fff;
				z-index: 999;
				position: relative;
			}
		}
		
		.text-text {
			display: flex;
			align-items: center;
			
			.text-text-item {
				color: #000;
				display: flex;
				align-items: center;
				justify-content: space-between;
				position: relative;
				
				.item {
					position: relative;
				}
			}
		}
	}
</style>