import Http from "../net/Http.js"
import noHttp from "../net/noHttp.js"

// 治疗预约 - 查询项目类别
export function getCureProjectTypeList(data){
	return Http.get('/wx/cure/getCureProjectTypeList',data)
}

// 治疗预约 - 查询项目列表
export function getCureProjectList(data){
	return Http.get('/wx/cure/getCureProjectList',data)
}

// 治疗预约 - 查询治疗师列表
export function getCureTherapist(data){
	return Http.get('/wx/cure/getCureTherapist',data)
}

// 治疗预约 - 查询治疗师时间点
export function getTherapistTime(data){
	return Http.get('/wx/cure/getTherapistTime',data)
}
// 治疗预约 - 预约记录
export function getCureOrder(data){
	return Http.get('/wx/cure/getCureOrder',data)
}

// 治疗预约 - 确定预约
export function addSubmitOrder(data){ 
	return Http.post('/wx/cure/submitOrder',data)
}
// 治疗预约 - 取消订单
export function updateOrderStatus(data){
	return Http.get('/wx/cure/updateOrderStatus/'+data)
}