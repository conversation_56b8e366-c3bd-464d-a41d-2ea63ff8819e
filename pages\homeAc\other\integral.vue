<template>
	<view class="gui-padding">
		<view class="gui-list gui-margin-top">
			<view class="gui-list-items" v-for="(item, index) in listData" :key="index">
				<view class="gui-list-body gui-border-b">
					<view class="gui-list-title">
						<view class="mr-10">
							<view class="gui-list-title-text text-zhutis ellipsis-1">{{item.explainDesc}}</view>
							<view class="gui-list-body-desc gui-color-gray">{{item.createTime}}</view>
						</view>
						<view class="">
							<text class="gui-bold text-zhuti fs-36">{{item.integrals}}</text>
						</view>
					</view>
				</view>
			</view>
			<view style="padding:50rpx 0;">
				<gui-empty v-if="listData.length <= 0">
					<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
						<!-- 请根据您的项目要求制作并更换为空图片 -->
						<image class="gui-empty-img" src="/static/kong.png"></image>
					</view>
					<text slot="text"class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="font-size: 30rpx; color:#9DABFF;">暂无数据...</text>
				</gui-empty>
			</view>
		</view>
	</view>
</template>
<script>
	import {getIntegralDetailed} from '@/api/home.js'
export default {
	data() {
		return {
			listData:{},
		}
	},
	onShow() {
		this.getList();
	},
	methods: {
		getList(){
			getIntegralDetailed({
				patientId:uni.getStorageSync('cardObj').patientId
			}).then(res=>{
				this.listData = res.data;
			})
			// let patientId = uni.getStorageSync('cardObj').patientId;
		}
	}
}
</script>
<style scoped>
</style>