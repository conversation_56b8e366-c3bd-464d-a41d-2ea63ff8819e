<template>
  <!-- 中医饮食指导（儿科一般指导） -->
  <view class="fs-32">
    <view class="my-20 fs-32">
      <view class="d-flex mb-20">
        <view>姓名：{{cardObj.userName || '-'}}</view>
        <view class="ml-120">性别：{{cardObj.sex == 1 ? "男" : "女"}}</view>
      </view>
      <view>时间：{{list.length ? $common.parseTime(list[0].createTime) : '-'}}</view>
    </view>
    <uni-table :loading="loading" border stripe emptyText="暂无更多数据" class="custom-table">
      <!-- 表头行 -->
      <uni-tr>
        <uni-th align="center" class="time-header">时间</uni-th>
        <uni-th align="center" class="type-header">类型</uni-th>
        <uni-th align="center" class="content-header">执行方案</uni-th>
      </uni-tr>
      <!-- 表格数据行 -->
      <template v-for="(item, index) in listWithKeys">
        <!-- 治疗行 -->
        <uni-tr :key="item.therapyKey" class="first-row">
          <uni-td align="center" class="time-cell time-cell-first">
            <view class="time-text">{{formatTimeText(item.arrange)}}</view>
          </uni-td>
          <uni-td align="center" class="type-cell">治疗</uni-td>
          <uni-td align="left" class="content-cell">{{item.therapy}}</uni-td>
        </uni-tr>
        <!-- 运动行 -->
        <uni-tr :key="item.exerciseKey" class="sub-row">
          <uni-td align="center" class="time-cell time-cell-sub">
            <view class="time-text-hidden">{{formatTimeText(item.arrange)}}</view>
          </uni-td>
          <uni-td align="center" class="type-cell">运动</uni-td>
          <uni-td align="left" class="content-cell">{{item.exercise}}</uni-td>
        </uni-tr>
        <!-- 饮食行 -->
        <uni-tr :key="item.dietKey" class="sub-row">
          <uni-td align="center" class="time-cell time-cell-sub">
            <view class="time-text-hidden">{{formatTimeText(item.arrange)}}</view>
          </uni-td>
          <uni-td align="center" class="type-cell">饮食</uni-td>
          <uni-td align="left" class="content-cell">{{item.diet}}</uni-td>
        </uni-tr>
        <!-- 文化活动行 -->
        <uni-tr :key="item.cultureKey" class="sub-row last-row">
          <uni-td align="center" class="time-cell time-cell-sub">
            <view class="time-text-hidden">{{formatTimeText(item.arrange)}}</view>
          </uni-td>
          <uni-td align="center" class="type-cell">文化活动</uni-td>
          <uni-td align="left" class="content-cell">{{item.culture}}</uni-td>
        </uni-tr>
      </template>
    </uni-table>
  </view>
</template>

<script>
import {
  listFastingTherapy
} from '@/api/home.js'
export default {
  name: 'jslf',
  props: ['templateId', 'visitRecordId', 'templateDictKey'],
  data() {
    return {
      loading: false,
      cardObj: uni.getStorageSync('cardObj'),
      list: []
    }
  },
  computed: {
    // 为每个列表项生成唯一的key
    listWithKeys() {
      return this.list.map((item, index) => ({
        ...item,
        therapyKey: `therapy_${index}`,
        exerciseKey: `exercise_${index}`,
        dietKey: `diet_${index}`,
        cultureKey: `culture_${index}`
      }));
    },
    // 患者信息
    info() {
      return this.cardObj || {};
    }
  },
  watch: {
    templateDictKey: {
      handler() {
        this.getDetail();
      },
      immediate: true
    }
  },
  methods: {
    // 获取建议详情
    getDetail() {
      this.loading = true
      listFastingTherapy({
        visitId: this.visitRecordId,
        patientId: this.cardObj.patientId
      }).then(res => {
        this.list = res.rows
        this.loading = false
      })
    },
    // 格式化时间文本为垂直显示
    formatTimeText(text) {
      if (!text) return '';
      // 将文本按字符分割并用换行符连接
      return text.split('').join('\n');
    }
  },
}
</script>

<style>
/* 小程序表格适配样式 */
.custom-table {
  width: 100%;
  table-layout: fixed;
}

/* 表头样式 */
.time-header {
  width: 120rpx !important;
  min-width: 120rpx !important;
  max-width: 120rpx !important;
  padding: 15rpx 5rpx !important;
  font-size: 28rpx !important;
  font-weight: bold !important;
  background-color: #e9ecef !important;
}

.type-header {
  width: 140rpx !important;
  min-width: 140rpx !important;
  max-width: 140rpx !important;
  padding: 15rpx 10rpx !important;
  font-size: 28rpx !important;
  font-weight: bold !important;
  background-color: #e9ecef !important;
}

.content-header {
  padding: 15rpx 10rpx !important;
  font-size: 28rpx !important;
  font-weight: bold !important;
  background-color: #e9ecef !important;
}

/* 数据单元格样式 */
.time-cell {
  width: 120rpx !important;
  min-width: 120rpx !important;
  max-width: 120rpx !important;
  padding: 15rpx 5rpx !important;
  vertical-align: middle !important;
  font-weight: bold !important;
  background-color: #f8f9fa !important;
  text-align: center !important;
  border-right: 1px solid #dee2e6 !important;
  position: relative;
}

/* 第一行时间单元格 - 显示时间 */
.time-cell-first {
  border-bottom: none !important;
}

/* 子行时间单元格 - 隐藏时间但保持布局 */
.time-cell-sub {
  border-top: none !important;
  border-bottom: none !important;
  background-color: #f8f9fa !important;
}

/* 最后一行恢复底边框 */
.last-row .time-cell-sub {
  border-bottom: 1px solid #dee2e6 !important;
}

.time-text {
  white-space: pre-line;
  line-height: 1.4;
  font-size: 22rpx;
  letter-spacing: 1rpx;
  word-break: break-all;
}

/* 隐藏子行的时间文本 */
.time-text-hidden {
  opacity: 0;
  white-space: pre-line;
  line-height: 1.4;
  font-size: 22rpx;
  letter-spacing: 1rpx;
  word-break: break-all;
}

.type-cell {
  width: 140rpx !important;
  min-width: 140rpx !important;
  max-width: 140rpx !important;
  padding: 12rpx 8rpx !important;
  font-size: 26rpx !important;
  text-align: center !important;
  vertical-align: middle !important;
  border-right: 1px solid #dee2e6 !important;
}

.content-cell {
  padding: 12rpx 15rpx !important;
  line-height: 1.4 !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  min-height: 60rpx !important;
  font-size: 26rpx !important;
  vertical-align: middle !important;
}

/* 通用表格样式覆盖 */
.uni-table-td {
  padding: 12rpx 15rpx !important;
  font-size: 26rpx !important;
  line-height: 1.4 !important;
  vertical-align: middle !important;
  border: 1px solid #dee2e6 !important;
}

.uni-table-th {
  padding: 15rpx 10rpx !important;
  font-size: 28rpx !important;
  font-weight: bold !important;
  background-color: #e9ecef !important;
  border: 1px solid #dee2e6 !important;
}

/* 行样式 */
.first-row {
  border-top: 2px solid #dee2e6;
}

.sub-row .type-cell,
.sub-row .content-cell {
  border-top: none !important;
}

.last-row .type-cell,
.last-row .content-cell {
  border-bottom: 1px solid #dee2e6 !important;
}

/* 小程序特殊适配 */
/* #ifdef MP-WEIXIN */
.custom-table {
  overflow-x: auto;
}

.time-cell {
  position: relative;
}

.content-cell {
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 小程序中进一步优化时间列合并效果 */
.first-row .time-cell-first::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: -1px;
  height: 1px;
  background-color: #f8f9fa;
  z-index: 1;
}
/* #endif */

/* 废弃的旧样式保留以防需要 */
.tle {
  width: 10%;
  border: 1px solid #f2f3f4;
  text-align: center;
  writing-mode: vertical-lr;
  letter-spacing: 10rpx;
  line-height: 70rpx;
}

.gui-td {
  width: 100rpx;
  flex: 1;
  overflow: hidden;
  padding: 20rpx 10rpx;
  display: flexbox;
}

.gui-td-text {
  line-height: 40rpx !important;
}

#lineTd {
  padding: 0;
  background: #fff url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxsaW5lIHgxPSIwIiB5MT0iMCIgeDI9IjEwMCUiIHkyPSIxMDAlIiBzdHJva2U9IiNFQkVFRjUiIHN0cm9rZS13aWR0aD0iMSIvPjwvc3ZnPg==) no-repeat 100% center;
}
</style>
