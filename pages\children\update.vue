<template>
	<view class="gui-padding">
		<form @submit="submit">
			<view class="gui-form-item gui-border-b" style="margin-top: 20rpx;">
				<text class="gui-form-label" style="width: 200rpx;">身高（厘米）:</text>
				<view class="gui-form-body">
					<input type="text" class="gui-form-input" v-model="update.height" name="height"
						placeholder="请输入身高" />
				</view>
			</view>
			<view class="gui-form-item gui-border-b" style="margin-top: 20rpx;">
				<text class="gui-form-label" style="width: 200rpx;">体重（公斤）:</text>
				<view class="gui-form-body">
					<input type="text" class="gui-form-input" v-model="update.weight" name="weight"
						placeholder="请输入体重" />
				</view>
			</view>
			<view class=" gui-flex gui-rows gui-nowrap gui-justify-content-center" style="margin-top: 80%;">
				<button type="default" class="gui-button" style="width:400rpx;background-color: #C59F79;"
					formType="submit">
					<text class="gui-icons gui-color-white gui-button-text">保 存 修 改</text>
				</button>
			</view>
		</form>
	</view>
</template>

<script>
	import {measureUpdate} from '@/api/children.js'
	export default {
		data() {
			return {
				update: {
					id: '',
					height: '',
					weight: ''
				}
			}
		},
		onLoad(openid) {
			this.update = JSON.parse(openid.item);	
		},
		methods: {
			submit: function() {
				// console.log(this.update)
				measureUpdate(this.update).then(res=>{
					if (res.code == 200) {
						if (res.data != null) {
							this.$common.msg("最新的身高记录不能小于上一次 "+ res.data.height +" 厘米")	  
							return
						} 
						uni.navigateBack({
								delta:1,//返回层数，2则上上页
							})
					}
				})
			}
		}
	}
</script>

<style>

</style>
