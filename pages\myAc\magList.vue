<template>
	<gui-page :custom-footer="'true'">
		<view slot="gBody" class="gui-padding mt-30">
			<view class="" v-for="(item,index) in list" :key="index">
				<view v-if="item.appName" class="gui-flex gui-space-between gui-align-items-center mb-20 gui-border-b pb-20">
					<view class="fs-36 pl-30">{{item.appName}}</view>
					<view class="">
						<switch :checked="item.setType" :name="item.setType" @change="val=>{switchChange(val,item)}" color="#46df6c" />
					</view>
				</view>
			</view>
			<view style="height: 40rpx;"></view>
		</view>
		<!-- <view slot="gFooter" class="gui-flex gui-justify-content-center">
			<view class="bg-zhuti gui-color-white py-20 px-60 fs-40 gui-border-radius-large">确定修改并退出</view>
		</view> -->
	</gui-page>
</template>
<script>
	import {
		getDeptAppMsgSetList,setDeptAppMsgStatus
	} from '@/api/my.js'
export default {
	data() {
		return {
			list:[],
		}
	},
	onShow() {
		this.getlist();
	},
	methods: {
		getlist(){
			getDeptAppMsgSetList({memberId:uni.getStorageSync('user').id}).then(res =>{
				if (res.data.length>0) {
					this.list = res.data
					// res.data.map((item,index)=>{
					// 	this.list.push(Object.assign(item,{setType:true}))
					// })
				}
			})
		},
		// switch 开关
		switchChange : function (e,item) {
			console.log(item)
			setDeptAppMsgStatus({
				memberId:uni.getStorageSync('user').id,
				appId:item.id,
				status:e.detail.value
				}).then(res =>{
					// let changeList = this.list
					// changeList[index].setType = e.detail.value;
				// if (res.data.length>0) {
				// 	this.list = res.data
				// 	// res.data.map((item,index)=>{
				// 	// 	this.list.push(Object.assign(item,{setType:true}))
				// 	// })
				// }
			})
			
		},
	}
}
</script>
<style>
</style>