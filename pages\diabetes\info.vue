<template>
	<view>
		<view class="head_img gui-margin-top">
			<image :src="info.image" />
		</view>
		<view class="info_title gui-ellipsis">
			{{ info.name || '' }}
		</view>
		<view class="info_nav">
			<view class="one_info" v-for="(item,index) in one" :key="index">
				<span :class="item.value > 0 ? 'Aone_info':''">{{ item.value }}</span>
				<span>{{ item.Company }}</span>
				<view class="one_info_title">{{ item.title }}</view>
			</view>
		</view>
		<view class="info_nav">
			<view class="two_info" v-for="(item,index) in two" :key="index">
				<span :class="index >= 1 ? 'Atwo_info':'Btwo_info'">{{ item.value }}</span>
				<view class="two_info_title">{{ item.Company }}</view>
			</view>
		</view>
		<view class="info_nav ">
			<view class="two_info" v-for="(item,index) in three" :key="index">
				<span :class="index >= 1 ? 'Atwo_info':'Btwo_info'">{{ item.value }}</span>
				<view class="two_info_title">{{ item.Company }}</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		sugarControlDetail
	} from '@/api/diabetes.js'
	export default {
		data() {
			return {
				info: {},
				one: [{
						value: '0',
						Company: '千卡',
						title: '卡路里'
					},
					{
						value: '0',
						Company: '克',
						title: '碳水化合物'
					},
					{
						value: '0',
						Company: '克',
						title: '蛋白质'
					}
				],
				two: [{
						id: '1',
						value: 'GI',
						Company: '升糖生成指数'
					},
					{
						id: '2',
						value: '0',
						Company: '指数'
					},
					{
						id: '3',
						value: '-',
						Company: '等级'
					}
				],
				three: [{
						id: '1',
						value: 'GL',
						Company: '升糖负荷指数'
					},
					{
						id: '2',
						value: '0',
						Company: '指数'

					},
					{
						id: '3',
						value: '-',
						Company: '等级'
					}
				]
			}
		},
		onLoad(option) {
			sugarControlDetail(option.id).then(res => {
				this.info = res.data || {}
				this.one[0].value = this.info.calorie || 0
				this.one[1].value = this.info.protein || 0
				this.one[2].value = this.info.carbohydrate || 0
				this.two[1].value = this.info.sugarIndex || 0
				this.two[2].value = this.handleLevel(this.info.sugarLevel)
				this.three[1].value = this.info.fuheIndex || 0
				this.three[2].value = this.handleLevel(this.info.fuheLevel)
			})
		},
		methods: {
			handleLevel(value) {
				return value === "1" && '低' ||
					value === "2" && '中' ||
					value === "3" && '高' ||
					'-'
			}
		}
	}
</script>

<style lang="less">
	.head_img {
		image {
			display: block;
			width: 90%;
			height: 300rpx;
			margin: 0 auto;
		}
	}

	.info_title {
		display: block;
		width: 80%;
		height: 100rpx;
		background-color: #ffffff;
		border-radius: 15rpx;
		margin: 0 auto;
		line-height: 100rpx;
		font-size: 32rpx;
		font-weight: bold;
		position: relative;
		margin-top: -50rpx;
		padding-left: 50rpx;
		box-shadow: 5rpx 10rpx 20rpx #eaeaea;
	}

	.info_nav {
		width: 80%;
		height: 180rpx;
		margin: 0 auto;
		background-color: #ffffff;
		box-shadow: 5rpx 10rpx 20rpx #eaeaea;
		margin-top: 32rpx;
		display: flex;
		flex-wrap: wrap;

		.one_info {
			width: 30%;
			height: 80%;
			align-items: center;
			// background-color: pink;
			margin: 0rpx 10rpx;
			text-align: center;
			font-size: 30rpx;
			// color: #6d6d6d;
			color: rgba(69, 90, 100, 0.6) !important;
			padding: 30rpx 5rpx 20rpx 5rpx;

			// 字体颜色
			.Aone_info {
				font-size: 40rpx;
				color: #e4a841;
			}

			span:nth-child(2) {
				// background-color: red !important;
				margin-left: 10rpx;
			}

			.one_info_title {
				margin-top: 15rpx;
				font-size: 25rpx;

			}

		}

		.two_info {

			width: 30%;
			height: 80%;
			align-items: center;
			// background-color: pink;
			margin: 0rpx 10rpx;
			text-align: center;
			font-size: 30rpx;
			// color: #6d6d6d;
			color: rgba(69, 90, 100, 0.6) !important;
			padding: 30rpx 5rpx 20rpx 5rpx;

			// 字体颜色
			.Atwo_info {
				font-size: 40rpx;
				color: #84d5bf;
			}

			.Btwo_info {
				font-size: 40rpx;
				color: #3a3a3a;
			}

			span:nth-child(2) {
				// background-color: red !important;
				margin-left: 10rpx;
			}

			.two_info_title {
				margin-top: 15rpx;
				font-size: 25rpx;
				width: unset !important;

			}

		}


	}

	page {
		background-color: #f6f7f9;
	}
</style>
