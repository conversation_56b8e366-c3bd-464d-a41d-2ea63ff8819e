<template>
	<!--  微信授权登录，新用户在第一次进入小程序进入该页面-->
	<view>
		<view class="box">
			<view class="title">
				<text>手机号快捷登录：</text>
			</view>
			<view class="box-login">
				<view style="width: 200upx;height: 200upx;border-radius: 50%;overflow: hidden;">
					<image :src="logo ||'https://img.starup.net.cn/xmkj/zwb/img/wx.png'" class="w-100"></image>
				</view>
			</view>
			<view class="btn">
				<!--        新用户获取手机号需要手动触发-->
				<button class="btn-sub" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">登录</button>
				<!-- <view class="btn-sub" @click="regUser">登录</view> -->
			</view>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				logo:'',
				domainType:this.$common.domainType,
				userinfo: {
					openId: uni.getStorageSync("openId"),
					avatar: 'https://img.starup.net.cn/xmkj/zwb/img/wx.png',
					// nickName: '',
					// phone:''
				},
				vType: 1 //2.27.0及以下版本微信基础库走类型1
			};
		},
		onLoad() {
			this.logo = __wxConfig.accountInfo.icon
			this.getUserSystem();
		},
		methods: {
			getPhoneNumber (e) {
				// console.log(e.detail)
			 //    console.log('动态令牌',e.detail.code)  // 动态令牌
			 //    console.log('回调信息',e.detail.errMsg) // 回调信息（成功失败都会返回）
			 //    console.log(e.detail.errno)  // 错误码（失败时返回）
				if (e.detail.code) {
					this.postAuth({...this.userinfo,code:e.detail.code})
				}
			},
			//授权数据进入后台进行保存
			postAuth(userinfo) {
				this.$common.RequestDataNo({
					url: this.$common.postAuth,
					data: userinfo,
				}, res => {
					//缓存token和微信用户信息
					uni.setStorageSync("token", res.data.token)
					uni.setStorageSync("user", res.data.user);
					this.$common.msg(res.msg);
					this.$common.navBack(1) //返回进入授权页之前页面
				})
			},
			//获取用户版本信息
			getUserSystem() {
				let res = uni.getSystemInfoSync(); //获取系统信息(里面包含微信版本号)
				let version = this.compareVersion(res.hostSDKVersion, '2.27.0');
				if (version > 0) {
					this.vType = 2
				}
			},
			//判断版本号
			compareVersion(v1, v2) {
				v1 = v1.split('.')
				v2 = v2.split('.')
				const len = Math.max(v1.length, v2.length)
				while (v1.length < len) {
					v1.push('0')
				}
				while (v2.length < len) {
					v2.push('0')
				}
				for (let i = 0; i < len; i++) {
					const num1 = parseInt(v1[i])
					const num2 = parseInt(v2[i])
					if (num1 > num2) {
						return 1 //大于限定版本
					} else if (num1 < num2) {
						return -1 //小于限定版本
					}
				}
				return 0 //等于限定版本
			}
		}

	}
</script>

<style scoped>
	
	.btn {
		width: 100%;
	}

	.btn-sub {
		width: 670rpx;
		margin: 80rpx auto 0;
		height: 90rpx;
		background-color: #C59F79;
		border-radius: 45rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 36rpx;
		color: #fff;

	}

	.box {
		padding: 14px 10px 0 10px;
	}

	.title {
		font-weight: bold;
		font-size: 16px
	}

	.box-login {
		margin-top: 100rpx;
		width: 100%;
		height: 100%;
		text-align: center;
	}

	.box-login image {
		width: 200upx;
		height: 200upx;
		border-radius: 50%;
	}

	.box-login view {
		margin: 10px auto;
	}
</style>
