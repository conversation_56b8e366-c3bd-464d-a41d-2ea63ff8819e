<template>
	<view class="gui-padding">
		<view v-if="formData.whetherPerform === -1">
			<view style="padding:50rpx 0;">
				<text slot="text"class="gui-block-text gui-text-center gui-margin-top text-zhuti" >此内容已经被撤回...</text>
			</view>
		</view>
		<view v-else>
			<view class="gui-margin-top pb-15" v-if="formData.remark">
				<text class="ui-h6 gui-bold">医嘱备注：</text>
				<text class="fs-30">{{formData.remark || '无'}}</text>
			</view>
			<view v-if="status==0">
				<view class="gui-flex gui-rows gui-space-between">
					<text class="gui-h6 gui-bold">上传舌面：</text>
				</view>
				<view class="gui-margin-top">
					<gui-upload-images @change="change" ref="uploadimgcom" uploadServerUrl="https://您的域名/地址">
					</gui-upload-images>
				</view>
				<view class="gui-form-item">
					<text class="gui-form-label gui-h6 gui-bold" style="width: 160rpx;">记录日期：</text>
					<!-- <view class="gui-form-body gui-flex gui-rows gui-space-between fs-32" @click="showPop">
						<view class="">
							<text class="demo gui-icons">{{ formData.measureTime }}</text>
						</view>
						<view class="">
							<text class="gui-icons">&#xe601;</text>
						</view>
					</view> -->
					<view class="gui-form-body " >
						<gui-datetime @confirm="confirm4" :value="formData.measureTime" ref="timeBegin" :units="['年', '月', '日','时','分']" :isSecond= 'false' >
							<view class="gui-flex gui-rows gui-space-between fs-32">
								<text class="demo gui-icons">{{ formData.measureTime }}</text>
								<text class="gui-icons">&#xe601;</text>
							</view>
						</gui-datetime>
					</view>
				</view>
			</view>
			<view v-else>
				<view class="gui-flex gui-rows gui-space-between">
					<text class="gui-h6 gui-bold">上传舌面：</text>
				</view>
				<view v-if="imgs.length !=0" class="gui-margin-top gui-flex gui-wrap" >
					<image v-for="(item,index) in imgs" :key="index" :src="item.img" class="logo" @click="clickImg(item)"></image>
				</view>
				<view v-else class="py-100" >
					<text>无</text>
				</view>
			</view>
			<view class="gui-margin-top">
				<text class="gui-h6 gui-bold">描 述：</text>
			</view>
			<view class="gui-bg-gray gui-margin-top">
				<textarea v-model="formData.lingualRemark" class="gui-text-area" style="font-size: 30rpx;" placeholder="请输入内容" />
			</view>
			<view v-if="status==0"
				class="bg-white w-100 pos-fixed box-size-border box-shadows py-20 px-40 gui-border-radius-small"
				style="height: 130rpx; bottom: 0;left: 0;z-index: 9;">
				<view @click="reSubmit" class="bg-zhuti text-white w-100 h-100 d-flex ai-center jc-center">提交完成</view>
			</view>
			<!-- <lingfeng-timepicker :safeArea="false" ref="timePop" type="datetime" :defaultData="defaultData"
				@change="confirm"></lingfeng-timepicker> -->
		</view>
	</view>
</template>
<script>
	import {addTaskPerform,getFollowTaskInfo} from '@/api/task.js'
// import { data } from '../../static/js/icon';
	export default {
		data() {
			return {
				// defaultData:{
				//     startTime:this.$common.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}'),
				// 	startTime:'2023-6-9 14:30',
				// },
				id: '',
				taskType: '3',
				jcDate: [],
				status: '',
				// 表单数据存储
				formData: {
					// 记录需要上传的图片数据
					lingualFiles: '',
					remark: '',
					lingualRemark:'',
					measureTime:this.$common.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}'),
				},
				// 原数组
				imgUrl: [],
				// 图片数据
				imgs: [],
				reSubmitBnt:false,
			}
		},
		onLoad(options) {
			if (options.id) {
				this.id = options.id;
				this.taskType = options.taskType;
				this.getDetail();
			}
			this.imgUrl = [];
			this.jcDate = uni.getStorageSync('jcData');
			uni.removeStorageSync('jcData');
		},
		methods: {
			// showPop() {
			// 	this.$refs.timePop.show();
			// },
			// confirm(e) {
			// 	// console.log(e)
			// 	this.formData.measureTime = e
			// },
			confirm4(res) {
				this.formData.measureTime = `${res[0]}-${res[1]}-${res[2]} ${res[3]}:${res[4]}`
			  // const birthday = new Date(this.birthday)
			  // const measureDate = new Date(`${res[0]}-${res[1]}-${res[2]}`)
			  // if (measureDate) {
			  //   this.$common.msg("记录日期不可小于生日")
			  //   this.formData.measureDate = this.$common.parseTime(new Date(), '{y}-{m}-{d}')
			  // } else {
			  //   this.formData.measureDate = `${res[0]}-${res[1]}-${res[2]}`
			  // }
			  this.$forceUpdate()
			},
			clickImg(item) {
				wx.previewImage({
					urls: [item.img], //需要预览的图片http链接列表，多张的时候，url直接写在后面就行了
					current: '', // 当前显示图片的http链接，默认是第一个
					success: function(res) {},
					fail: function(res) {},
					complete: function(res) {},
				})
			},
			getDetail() {
				getFollowTaskInfo({
					id: this.id,
					taskType: this.taskType
				}).then(res=>{
					this.status = res.data.monitorInfo.whetherPerform
					this.formData = res.data.monitorInfo
					this.formData.measureTime = this.$common.parseTime(res.data.taskInfo.sendTime, '{y}-{m}-{d} {h}:{i}');
					let imgstr = res.data.monitorInfo.lingualFiles
					if (res.data.monitorInfo.lingualRemark == null) { this.formData.lingualRemark = '无' }
					if (imgstr != null) {
						this.imgs = imgstr.split(",").map(item =>{
							let obj = {}
							obj.img = item
							return obj
						})
					}
				})
			},
			// 记录选择图片时的待提交数据
			change: function(e) {
				// 数组类型
				this.imgUrl = e
				// let key = this.imgUrl.findIndex(imgdata => imgdata === e.url)
			},
			//预提交-需要先上传
			reSubmit() {
				if (this.imgUrl.length <=0) {
					return this.$common.msg('请上传图片')
				}
				if(!this.reSubmitBnt){
					this.reSubmitBnt = true;
					// 先取出 url地址 赋值给arrs
					let arrs = [];
					this.imgUrl.forEach((item, index) => {
						arrs.push(item.url)
					})
					let that = this
					// 将相同的过滤出来
					let resultArr = arrs.filter(function(item, index, self) {
						// 字符匹配
						return self.indexOf(item) == index;
					})
					let arr = '';
					let c = 0
					for (let key in resultArr) {
						this.$common.uploadFile(resultArr[key], resx => {
							c += 1; //上传成功一个+1
							if (c == resultArr.length) {
								//所有图片已经传完
								arr += resx.data.url;
								that.submit(arr) //提交表单
							} else {
								arr += resx.data.url + ',';
							}
						},err=>{
							console.log('进来报错啦啦啦啦啦')
							this.reSubmitBnt = false;
						},'上传中');
					}
					
				}
			},
			// 提交动态
			submit(arr) {
				uni.showLoading({
					title: "提交中...",
				});
				this.formData.lingualFiles = arr;
				addTaskPerform({
					...this.formData,
					measureTime:this.formData.measureTime+':00',
					planId: this.id,
					taskType: this.taskType,
					monitorId:7,
					monitorName:'舌面象',
					patientId: uni.getStorageSync('cardObj').patientId, // 就诊卡用户id
				}).then(res=>{
					
					this.$common.msg("提交成功", "success")
					setTimeout(() => {
						// this.getDetail();
						this.$forceUpdate()
						this.$common.navTab('/pages/task/index')
						uni.hideLoading();
					}, 1000)
				}).catch(err =>{
						this.reSubmitBnt = false;
						uni.hideLoading();
					})
			}
		}
	}
</script>
<style>
	.logo {
			height: 350rpx;
			width: 300rpx;
		}
	.gui-text-area {
		font-size: 26rpx;
		color: #2B2E3D;
		height: 150rpx;
		padding: 20rpx;
	}

	.demo-sub-btn {
		height: 100rpx;
		line-height: 100rpx;
		width: 320rpx;
		text-align: center;
		font-size: 32rpx;
		border-radius: 10rpx;
		position: absolute;
		bottom: 60rpx;
		right: 40rpx;
	}
</style>
