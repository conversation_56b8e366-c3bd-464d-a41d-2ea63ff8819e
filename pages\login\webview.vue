<template>
  <view class="">微信授权中。。。</view>
</template>

<script>
import wx from 'weixin-js-sdk'

export default {
  name: "webview",
  data() {
    return {
      apptype: '200'//默认200：广中医-慢病管理,201： AI头痛辅助系统
    }
  },
  onLoad() {
	  // alert('路由信息信息');
    // console.log("wx===" + wx)
	  this.GetRequest(window.location.href)

  },
  methods:{
	  GetRequest(str) {
      console.log("授权返回信息：" + str)
	    let codeValue = this.getParameterByName("code",str);//获取公众号code
      this.apptype = this.getParameterByName("type",str);//获取应用类型
      this.apptype = this.getParameterByName("type",str);//获取应用类型
	  
	  if (apptype == 'h5') {
		  // 带openid跳头痛项目
		  location.href = "xxxxxx?openid=" + 
		  return
	  }
	  

      console.log("当前公众号code：" + codeValue)
      console.log("当前apptype：" + this.apptype)

      this.wxgzh(codeValue);
	  },
    //获取url中指定key的值
    getParameterByName (name, url) {
      if (!url) url = window.location.href;
      name = name.replace(/[\[\]]/g, "\\$&");
      //匹配所有符合条件的，并取最后一个
      var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)", 'g');
      var results = url.match(regex);
      var tempResults = results != null && results[results.length - 1] != undefined ? results[results.length - 1] : '';
      var finalResults = regex.exec(tempResults);
      if (!finalResults) return "";
      if (!finalResults[2]) return '';
      return decodeURIComponent(finalResults[2].replace(/\+/g, " "));
    },
    //公众号信息回传到小程序并
	  wxgzh(jscode){
      wx.miniProgram.postMessage({
        //这个方法在小程序文档中有，H5向小程序传值 [附上官方地址](https://developers.weixin.qq.com/miniprogram/dev/component/web-view.html)
        data: {
          jscode: jscode,
          apptype: this.apptype
        }
      });

      setTimeout(this.toMiniapp(),2000);//延迟2秒再跳转页面，以便消息传回小程序后能先做业务处理。

	  },
    //调整回对应指定页面
    toMiniapp(){

      //H5向小程序传值的方法会在特定的时间触发，因此需要我们手动写一个跳转（也可以写后退和摧毁，分享，以需求而定）
      if(this.apptype == 200){
		  
        wx.miniProgram.redirectTo({
          url: '/pages/home/<USER>' //这里是AI头痛辅助系统 H5 页面
        });
      }else {
        wx.miniProgram.switchTab({
          url: '/pages/home/<USER>' //这里是中医慢病管理小程序首页菜单页面-只能用switchTab
        });
      }
    }

  }
}
</script>
