<template>
	<view>
		<scroll-view scroll-x="true">
			<view class="uni-container">
				<uni-table ref="table" :loading="loading" border emptyText="暂无更多数据" >
					<uni-tr class="fs-30">
						<uni-th width="260"></uni-th>
						<!-- <uni-th width="160" align="center">-3SD</uni-th> -->
						<uni-th width="160" align="center">-2SD</uni-th>
						<uni-th width="160" align="center">-1SD</uni-th>
						<uni-th width="160" align="center">中位数</uni-th>
						<uni-th width="160" align="center">+1SD</uni-th>
						<uni-th width="160" align="center">+2SD</uni-th>
						<!-- <uni-th width="160" align="center">+3SD</uni-th> -->
					</uni-tr>
					<uni-tr class="fs-28">
						<uni-th  width="400" align="center">年龄</uni-th>
						<uni-th >
							<view class="uni-group">
								<view class="gui-color-blue">身高(cm)</view>
								<view >体重(kg)</view>
							</view>
						</uni-th>
						<uni-th >
							<view class="uni-group">
								<view class="gui-color-blue">身高(cm)</view>
								<view >体重(kg)</view>
							</view>
						</uni-th>
						<uni-th >
							<view class="uni-group">
								<view class="gui-color-blue">身高(cm)</view>
								<view >体重(kg)</view>
							</view>
						</uni-th>
						<uni-th >
							<view class="uni-group">
								<view class="gui-color-blue">身高(cm)</view>
								<view >体重(kg)</view>
							</view>
						</uni-th>
						<uni-th >
							<view class="uni-group">
								<view class="gui-color-blue">身高(cm)</view>
								<view >体重(kg)</view>
							</view>
						</uni-th>
					</uni-tr>
					
					<uni-tr class="fs-30" v-for="(item, index) in tableData" :key="index">
						<uni-td  align="center">{{item.title}}</uni-td>
						<uni-td>
							<view class="uni-group">
								<view class="gui-color-blue">{{item.fsdTwo}}</view>
								<view  >{{item.fsdTwoWeight}}</view>
							</view>
						</uni-td>
						<uni-td>
							<view class="uni-group">
								<view class="gui-color-blue">{{item.fsdOne}}</view>
								<view  >{{item.fsdOneWeight}}</view>
							</view>
						</uni-td>
						<uni-td>
							<view class="uni-group">
								<view class="gui-color-blue">{{item.median}}</view>
								<view  >{{item.medianWeight}}</view>
							</view>
						</uni-td>
						<uni-td>
							<view class="uni-group">
								<view class="gui-color-blue">{{item.sdOne}}</view>
								<view  >{{item.sdOneWeight}}</view>
							</view>
						</uni-td>
						<uni-td>
							<view class="uni-group">
								<view class="gui-color-blue">{{item.sdTwo}}</view>
								<view  >{{item.sdTwoWeight}}</view>
							</view>
						</uni-td>
					</uni-tr>
				</uni-table>
				<view class="uni-pagination-box"><uni-pagination show-icon :page-size="pageSize" :current="pageCurrent" :total="total" @change="change" /></view>
			</view>
		</scroll-view>
	</view>
</template>
<script>
	export default {
	data() {
		return {
			// 每页数据量
			pageSize: 10,
			// 当前页
			pageCurrent: 1,
			// 数据总量
			total: 0,
			loading: false
		}
	},
	props: {
		tableData: {
			type: Array,
			default() {
				return [{
					name: '张三'
				}, {
					name: '李四'
				}]
			}
		}
	},
	onLoad() {
		this.getData(1)
	},
	watch:{
		tableData:{
		  deep:true,
		  handler(newValue,oldValue){
		  }
		}
	},
	methods: {
		
		// 分页触发
		change(e) {
			this.$refs.table.clearSelection()
			this.getData(e.current)
		},
		// 获取数据
		getData(pageCurrent, value = '') {
			this.loading = true
			this.pageCurrent = pageCurrent
			this.request({
				pageSize: this.pageSize,
				pageCurrent: pageCurrent,
				value: value,
				success: res => {
					// console.log('data', res);
					this.tableData = res.data
					this.total = res.total
					this.loading = false
				}
			})
		},
		// 伪request请求
		request(options) {
			const { pageSize, pageCurrent, success, value } = options
			let total = tableData.length
			let data = tableData.filter((item, index) => {
				const idx = index - (pageCurrent - 1) * pageSize
				return idx < pageSize && idx >= 0
			})
			if (value) {
				data = []
				tableData.forEach(item => {
					if (item.name.indexOf(value) !== -1) {
						data.push(item)
					}
				})
				total = data.length
			}

			setTimeout(() => {
				typeof success === 'function' &&
					success({
						data: data,
						total: total
					})
			}, 500)
		}
	}
}
</script>

<style>
.uni-group {
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content:space-between;
}
</style>
