<template>
	<gui-page :isLoading="pageLoading">
		<!-- 页面主体 -->
		<view slot="gBody">
			<view>
				<view v-if="showlist" class="pb-150 gui-bg-gray"
					style="min-height: 1800rpx;">
					<view class="gui-flex gui-align-items-center px-20 gui-bg-white" style="width: 100%;margin: auto;">
						<gui-select-menu style="width: 30%;"
						:items="selectMenu2"
						@select="select2"
						:selectIndex="selectIndex2"
						ref="selectMenu2"></gui-select-menu>
						<gui-search style="width: 70%; margin-bottom: 10rpx;" :kwd="keyword" @confirm="confirm" @clear="clear" height="85rpx" borderRadius="80rpx" placeholder="搜索医生姓名"></gui-search>
					</view>
					<view class="car-bg mb-20 mx-20 gui-bg-white mt-20" @tap="open(item)" v-for="(item,index) in ysList" v-if="item.isOnline"
						:key="index">
						<view class="gui-flex gui-space-between">
							<view class="gui-flex">
								<view class="">
									<img class="gui-grids-icon-img"
										:src="item.showAvatar || 'https://img.starup.net.cn/xmkj/zwb/img/yisheng.png'">
								</view>
								<view class="gui-flex gui-columns ml-10 mt-10">
									<view class="gui-flex gui-align-items-end">
										<view class="fs-36 mr-10" style="width: 140rpx;">{{item.nickName || '-'}} <text
												class="gui-color-gray pl-15" >|</text></view>
										<view v-if="item.docTitle" class="mr-20 gui-color-gray">{{item.docTitle || '-'}}</view>
										<view v-if="!item.docTitle" class="gui-color-gray">{{item.dept.deptName || '-'}}</view>
									</view>
									<view class="mt-10 gui-color-gray">
										<view class="mr-10" v-if="item.docTitle">{{item.dept.deptName || '-'}}</view>
										<view v-if="item.dept.leader">{{item.dept.leader || '-'}}</view>
									</view>

								</view>
							</view>
							<view class="fs-28 gui-color-blue" style="width: 120rpx;">
								 咨询{{Number(item.remark)+5 || '0'}}次
							</view>
						</view>
						<view class="text-coten">
							擅 长 ： {{item.specialty || '-'}}
						</view>
					</view>
					<view style="padding:50rpx 0;">
						<gui-empty v-if="ysList.length <= 0">
							<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
								<!-- 请根据您的项目要求制作并更换为空图片 -->
								<image class="gui-empty-img" src="/static/kong.png"></image>
							</view>
							<text slot="text" class="gui-text-small gui-block-text gui-text-center gui-margin-top"
								style="font-size: 30rpx; color:#9DABFF;">暂无数据</text>
						</gui-empty>
					</view>
				</view>

				<view v-if="!showlist" class="cu-list menu-avatar gui-bg-white">
					<view class="cu-item" @click="toConsult(item)" v-for="(item,index) in sessionList" :key="index">
						<view class="cu-avatar round lg"
							:style="item.uface ? 'background-image:url('+ item.uface + ');' : 'background-image:url(https://img.starup.net.cn/xmkj/zwb/img/yisheng.png)' ">
						</view>
						<view class="content">
							<view class="flex flex-wrap">
								<view class="basis-lg">
									<view class="text-black text-xl">
										{{item.uname}} <span v-if="item.docTitle" style="padding-left: 5rpx">|<span
												style='color:#a2a2a2;padding-left: 5rpx;font-size: 28rpx;'>{{item.docTitle}}医生</span></span>
									</view>
								</view>
								<view class="basis-sm">
									<view class="text-right margin-right text-gray text-sm">
										{{$common.parseTime(item.lastTime,'{m}-{d} {h}:{i}')}} </view>
								</view>
							</view>

							<view class="text-grey text-sm margin-top-xs d-flex ai-center jc-between">
								<view v-if="item.contentType == 'img'" class="text-cut ellipsis-1 flex-1">[ 图片消息 ]</view>
								<view v-else-if="item.contentType == 'voice'" class="text-cut ellipsis-1 flex-1">[ 语音消息 ]</view>
								<view v-else-if="item.contentType == 'task'" class="text-cut ellipsis-1 flex-1">{{JSON.parse(item.lastContent).questionnaire_name || ''}}</view>
								<view v-else class="text-cut ellipsis-1 flex-1">{{item.lastContent}}</view>
								<view style="min-width: 70rpx;text-align: right;margin-right: 30rpx;">
									<text v-if="item.unRead > 0" class="gui-badge demo gui-bg-red gui-color-white"
										style="padding:4rpx 13rpx;">{{item.unRead}}</text>
								</view>
							</view>
						</view>
					</view>
					<view style="padding:50rpx 0;">
						<gui-empty v-if="sessionList.length <= 0">
							<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
								<!-- 请根据您的项目要求制作并更换为空图片 -->
								<image class="gui-empty-img" src="/static/kong.png"></image>
							</view>
							<text slot="text" class="gui-text-small gui-block-text gui-text-center gui-margin-top"
								style="font-size: 30rpx; color:#9DABFF;">暂无会话</text>
						</gui-empty>
					</view>
				</view>
				<view style="z-index: 10; position: fixed;" class="">
					<view class="grace-footer nav"
						style="background: #ffffff !important; border-top:1rpx solid #e2e2e2;">
						<view class="grace-grids nav_fu">
							<view class="grace-grids-items " style="position: relative;" v-for="(item, index) in footerItems2" :key="index"
								@tap="changeFooter2(index)">
								<text v-if="getMsgReadedNum && getMsgReadedNum>0 && index==1" class="gui-badge gui-bg-red gui-badge-absolute gui-color-white">{{getMsgReadedNum}}</text>
								<text v-if="index === 0" class="gui-icons fs-50"
									:class="[footerCurrent == index ? 'grace-footer-active' : '']">&#xe67b;</text>
								<text v-else class="gui-icons fs-50"
									:class="[footerCurrent == index ? 'grace-footer-active' : '']"
									style="position: relative;">&#xe6b8;
									<!-- <text class="Unread">95+</text> -->
								</text>
								<!-- <image :src="footerCurrent == index ? item[0]+'hl.png' : item[0]+'.png'" mode="widthFix" style="width: 60rpx;height: 60rpx;display:block; margin: 0 auto;"></image>				 -->
								<text class="grace-grids-text pt-10"
									:class="[footerCurrent == index ? 'grace-footer-active' : '']">{{item[1]}}</text>
							</view>
						</view>
					</view>
				</view>
				<gui-iphone-bottom></gui-iphone-bottom>

			</view>


			<!-- 医生信息弹窗 -->
			<gui-modal ref="guimodal" title="医生信息"
				titleStyle="line-height:100rpx; font-size:40rpx; font-weight:700; color:#2B2E3D;">
				<view slot="content" class="gui-padding">
					<view class="gui-list fs-30">
						<view class="gui-list-items">
							<view class="gui-relative">
								<image class="gui-grids-icon-img ucenter-face-image"
									:src="doctorList.showAvatar || 'https://img.starup.net.cn/xmkj/zwb/img/yisheng.png'">
								</image>
							</view>
							<view class="gui-list-body">
								<view class="gui-list-title">
									<text
										class="gui-list-title-text gui-primary-color fs-36">{{doctorList.nickName|| '医生'}}</text>
								</view>
								<view class="gui-color-gray">
									职 称 :<text class="ml-20">{{ doctorList.docTitle || '暂未填写'}}</text>
								</view>
								<!-- <view class="">2023-12-13 韦总决定隐藏
									管 理 室 :<text class="ml-20">{{ doctorList.manageRoom || '治未病科'}}</text>
								</view> -->
							</view>
						</view>
						<scroll-view scroll-y="true" style="height: 580rpx;">
							<view class="gui-flex gui-columns pb-20">
								<text class="p-10">专业擅长</text>
								<text class="gui-color-gray ">{{doctorList.specialty || '暂未填写'}}</text>
							</view>
							<view class="gui-flex gui-columns">
								<text class="p-10">医生简介</text>
								<text class="gui-color-gray ">{{doctorList.intro || '暂未填写'}}</text>
							</view>
						</scroll-view>
					</view>
				</view>
				<view slot="btns" class="gui-flex gui-rows gui-space-between" style="border-top:1rpx solid #e2e2e2;">
					<view class="modal-btns gui-flex1" style="margin-right:80rpx;">
						<text class="modal-btns gui-color-gray" @tap="close1">取消</text>
					</view>
					<view class="modal-btns gui-flex1" style="margin-left:80rpx;">
						<text class="modal-btns gui-color-blue" @tap="tabSelect(doctorList)">立即咨询</text>
					</view>
				</view>
			</gui-modal>
		</view>

	</gui-page>
</template>

<script>
	import {
		mapMutations
	} from 'vuex';
	import {
		getDoctorList,getListByDeptId,isIntake
	} from '@/api/home.js'
	import {
		mySessionList,
		getImUser
	} from '@/api/im.js'
import {
		mapState
	} from "vuex"

	export default {
		data() {
			return {
				departmentId:0,
				keyword:'',
				selectIndex2        : 0,
				selectMenu2FromApi  : [],
				selectMenu2         : [],
				footerCurrent: -1,
				footerItems2: [
					//数据格式
					// 图标样式 名称 消息数量
					['/static/img/zy_mer_mmer', '医生列表'],
					['/static/img/zy_bar_serv', '我的咨询']

				],
				showlist: false,
				ysList: [],
				TabCur: 0,
				scrollLeft: 0,
				pageLoading: true,
				imUser: {
					userId: uni.getStorageSync('user').userId, // 系统用户id 空
					userUid: '', //链接im返回的uuid
					userType: '2', // 用户类型  1：医生  2：患者
					uname: uni.getStorageSync('user').userName, // 用户名
					nickName: uni.getStorageSync('user').nickName, // 昵称
					uface: uni.getStorageSync('user').avatar // 头像
				},
				sessionList: [],
				doctorList: [],
				timer: null, //定时器
				imMsgNums: 0, //im信息
				msgNums: 0,
				key:null,
				doctorId:'',
				depId:'',
			}
		},
		onLoad(options) {
			this.key = options.key
			this.footerCurrent = -1
			if (options.scene) {
				const scene = decodeURIComponent(options.scene).split(',')
				this.key = 1;
				this.depId = scene[0];
				this.doctorId = scene[1];
				this.getDoctorList(this.depId)
			}
		},
		created: function() {

		},
		computed:{
			...mapState(['getMsgReadedNum','graceIMStatus'])
		},
		watch:{
			getMsgReadedNum(newValue,oldValue){
				this.mySessionList()
			}
		},
		onShow() {
			// console.log(this.key,this.footerCurrent,'this.key')
			if (this.key) {
				this.changeFooter2(this.footerCurrent>0?this.footerCurrent:0)
			}else{
				this.changeFooter2(this.footerCurrent>0?this.footerCurrent:1)
			}
			this.$store.dispatch('graceIMConnect', this.imUser);
		},
		methods: {
			confirm(e){
				this.keyword = e;
				this.pageLoading = true;
				this.getDoctorList(this.selectMenu2FromApi[this.selectIndex2].deptId)
			},
			clear(){
				this.keyword = "";
				this.pageLoading = true;
				this.getDoctorList(this.selectMenu2FromApi[this.selectIndex2].deptId)
			},
			select2    : function (index, val) {
				this.pageLoading = true;
				this.selectIndex2 = index
				console.log("选择了 " + val);
				// 如何对应获取后端 api 的值
				this.getDoctorList(this.selectMenu2FromApi[index].deptId)
			},
			changeFooter2(index) {
				this.footerCurrent = index
				switch (index) {
					case 0:
						this.showlist = true;
						this.getListDept();
						break;
					case 1:
						this.showlist = false;
						this.mySessionList();
						break;
				}
			},
			//科室名称
			getListDept(){
				isIntake({patientId: uni.getStorageSync("cardObj").patientId}).then(res =>{
					this.departmentId = res.data
					getListByDeptId({currentDeptId:this.departmentId}).then(res =>{
						this.selectMenu2FromApi = res.data;
						for(var i = 0; i < res.data.length; i++){
							if (res.data[i].deptId == this.departmentId) {
								this.selectIndex2 = i
							}
							this.selectMenu2.push(res.data[i].appName);
						}
						if (this.depId) {return}
						if (this.departmentId) {
							this.getDoctorList(this.departmentId);
						} else{
							this.getDoctorList(res.data[0].deptId);
						}
					})
				});
			},
			//医生列表
			getDoctorList(id) {
				getDoctorList({
					name:this.keyword,//医生姓名
					deptId:id,//科室id
					patientId: uni.getStorageSync("cardObj").patientId
				}).then(res => {
					this.pageLoading = false
					this.ysList = res.data
					if (this.doctorId) {
						let key = res.data.findIndex(item => item.userId == this.doctorId)
						this.open(res.data[key])
					}
				})
			},

			...mapMutations(['graceIMRegGroup', 'getMsgReaded', 'getFriendList']),

			// 判断信息类型
			chooseMsgType(str) {
				let imgZZ = new RegExp(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/);
				try {
				  let jsonData = JSON.parse(str);
				  console.log('进来了呵呵呵')
				  return 'task';
				} catch (error) {
					if (imgZZ.test(str)) {// 图片类型
					console.log('进来了呵呵呵img')
						return 'img';
					} else {
						return 'str';
					}
				}
			},
			// 获取会话列表
			mySessionList: function() {
				mySessionList({
					userUid: uni.getStorageSync("userUid")
				}).then(res => {
					this.pageLoading = false
					this.sessionList = res.data;
					console.log(res.data,'res.data')
					this.$store.dispatch('getMsgReaded')
				})
			},
			toConsult(item) {
				let imObj = {
					groupIndex: item.groupIndex, // 组ID
					friendName: item.uname, // 患者姓名
					friendUid: item.friendUid, // 患者UUid
					userUid: uni.getStorageSync("userUid") // 当前用户UUid
				};
				this.$common.navTo('/pages/home/<USER>/consult?imObj=' + encodeURIComponent(JSON.stringify(imObj)));
			},
			//医生信息展示
			open(item) {
				this.doctorList = item
				this.$refs.guimodal.open();
			},
			//弹窗关闭
			close1() {
				this.$refs.guimodal.close();
			},
			//立即咨询
			tabSelect(doctorList) {
				getImUser({
					userUid: doctorList.userId,
					userId: uni.getStorageSync('user').id
				}).then(res => {
					let imObj = {
						groupIndex: res.data.groupIndex, // 组ID
						friendName: doctorList.nickName, // 医生姓名
						friendUid: res.data.dcUser.userUid, // 医生UUid
						userUid: res.data.hzUser.userUid, // 当前用户UUid
						doctorId: doctorList.userId, // 医生id
						avatar: 'https://img.starup.net.cn/xmkj/zwb/2022/02/11/08050225c71d4722ab100b26c6dac5c9.png',
						name: doctorList.nickName,
						appId:this.selectMenu2FromApi[this.selectIndex2].id,
					};
					this.$refs.guimodal.close();
					this.$common.navTo('/pages/home/<USER>/consult?imObj=' + encodeURIComponent(JSON.stringify(imObj)));
				})
			}
		}
	}
</script>

<style scoped>
	page {
		background-color: #f9f9f9 !important;
	}

	/* 没调的父窗口 */
	.grace-grids-items {
		width: 150rpx;
		padding: 20rpx 0;
		text-align: center;
		display: flex;
		flex-direction: column;
	}

	/* .grace-grids-icon{height:60rpx; line-height:60rpx; font-size:50rpx; color:#B2B2B2;} */
	.grace-grids-text {
		line-height: 40rpx;
		font-size: 25rpx;
		color: #747474;
	}

	/* 文字颜色 */
	.grace-footer-active {
		color: #7ac6d8 !important;
	}

	.nav {
		position: fixed;
		width: 100%;
		height: 140rpx;
		bottom: 0rpx;
		padding-bottom: 50rpx;
	}

	.nav_fu {
		display: flex;
		justify-content: space-evenly;
		align-items: center;
	}

	.text-coten {
		-webkit-line-clamp: 2;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
		padding: 0 25rpx;
		color: #5b5b5b;
	}

	.car-bg {
		background-color: #ffffff;
		padding: 30rpx 10rpx;
		border-radius: 10rpx;
	}

	.modal-btns {
		line-height: 88rpx;
		font-size: 32rpx;
		text-align: center;
		width: 200rpx;
	}

	.nav .cu-item {
		position: relative;
		margin: 0 90rpx;
	}

	.nav .cu-item.cur {
		position: relative;
		border-bottom: none;
	}

	.nav .cu-item.cur::before {
		content: "";
		position: absolute;
		left: 50%;
		bottom: 5rpx;
		transform: translate(-50%, 0);
		width: 40%;
		height: 8rpx;
		background: #fff;
		display: block;
		border-radius: 10rpx;
	}

	.cu-list.menu-avatar>.cu-item .content {
		width: calc(100% - 150rpx);
	}

	.gui-grids-icon-img {
		width: 120rpx;
		height: 120rpx;
		border-radius: 100%;
	}

	/* 未读数 */
	.Unread {
		position: unset;
		width: 50rpx;
		/* height: 40rpx; */
		/* line-height: 40rpx; */
		z-index: 99;
		/* padding: 5rpx 10rpx 5rpx 10rpx;		 */
		padding: 5rpx 10rpx;
		align-items: center;
		border-radius: 50rpx;
		top: -15rpx;
		left: 90rpx;
		position: absolute;
		font-size: 28rpx !important;
		color: #eee;
		text-align: center;
		background-color: #ee0e4a;
	}
</style>
