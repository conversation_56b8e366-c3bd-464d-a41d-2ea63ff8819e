import Http from "../net/Http.js"
import noHttp from "../net/noHttp.js"

// 专栏列表
export function articleSpecialList(data){
	return noHttp.get('/wx/index/articleSpecialList',data)
}
// 专栏分类
export function specialType(){
	return noHttp.get('/wx/index/type/special_type')
}

// 科室应用专栏分类
export function channelType(data){
	return noHttp.get('/wx/index/channelType',data)
}
// 专栏列表
export function articleSpecialListByType(data){
	return noHttp.get('/wx/index/articleSpecialListByType',data)
}