<template>
	<gui-page ref="guipage" :isLoading="pageLoading"  :apiLoadingStatus="apiLoadingStatus" :refresh="true" @reload="reload"  :loadmore="true" @loadmorefun="getList"
	:refreshBgColor="['#ffffff','#ffffff','#ffffff','#c59f79']"
	statusBarStyle="background: linear-gradient(90deg,#ffffff,#ffffff);"
	headerStyle="background: linear-gradient(90deg,#c59f79,#c1c1c1);">
		<view slot="gBody" class="record gui-flex1" >
			<!-- 顶部导航栏 -->
			<view class="record-tab pos-fixed bg-white" style="top: 0;left: 0;width: 100%;z-index: 99;">
				<view style="font-size: 32rpx;" class="record-tab-item" @click="tabClick(item.id)" v-for="(item,index) in tablist" :key="item.id">
					<view :class="['record-tab-item-name',{'record-tab-item-name-i':tabindex == item.id}]">{{item.name}}
					</view>
				</view>
			</view>
			<scroll-view style="margin-top: 120rpx;" :show-scrollbar="false" :scroll-y="true" v-if="list.length > 0">
				<view style="width: 750rpx;" v-for=" (item,index) in list" :key="index">
					<view class="mx-20 gui-scroll-x-items gui-flex gui-columns gui-border mb-40">
						<image class="gui-scroll-image gui-img-in" :src="item.activityImgUrl" mode="aspectFill" @click="details(item)"></image>
						<view class="gui-color-black mt-20 " >
							<view class="gui-flex gui-rows gui-space-between gui-align-items-center px-20 mb-10"  @click="details(item)">
								<view class="" style="width: 510rpx;">
									<view class="fs-30 gui-bold" style="width: 510rpx; overflow: hidden;white-space: nowrap; text-overflow: ellipsis;">
										{{item.activityName}}
									</view>
									<view class="fs-28 mt-20">
										活动截止时间：{{item.activityDateStatus ==1?item.activityDate : '不限'}}
									</view>
								</view>
								<view class="">
									<image v-if="item.issueStatus == 1" class="gui-img-in" style="width: 120rpx;height: 120rpx;" src="/static/img/hz_ing.png" mode="aspectFill"></image>
									<image v-else class="gui-img-in" style="width: 120rpx;height: 120rpx;" src="/static/img/hz_end.png" mode="aspectFill"></image>
								</view>
							</view>  
							<view class="fs-28 gui-flex gui-justify-content-end" style="border-top: 1px solid #ededee;"  @click="details(item,item.applyStatus)">
								<view v-if="item.issueStatus == 1 && item.applyStatus == 0" class="my-15 mr-20 bg-zhuti gui-color-white w100 py-10 gui-border-radius gui-flex gui-justify-content-center fs-24">报 名</view>
								<view v-else-if="item.issueStatus == 1 && item.applyStatus == 1" class="my-15 mr-20 zhuti-border text-zhuti w100 py-10 gui-border-radius gui-flex gui-justify-content-center fs-24">已报名</view>
								<view v-else class="my-15 mr-20 zhuti-border text-zhuti w100 py-10 gui-border-radius gui-flex gui-justify-content-center fs-24">查 看</view>
								
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
			<view v-if="list.length <= 0">
				<gui-empty>
					<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
						<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
					</view>
					<text slot="text" 
					class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
				</gui-empty>
			</view>
			<!-- 底部占位 -->
			<view style="height:40rpx;"></view>
		</view>
	</gui-page>
</template>

<script>
	import { activityList,addActivityByPatient } from '@/api/activity.js'
	export default {
		data() {
			return {
				tablist: [{
					id: 3,
					name: '全部'
				}, {
					id: 0,
					name: '待报名'
				}, {
					id: 1,
					name: '已报名'
				}, {
					id: 2,
					name: '已结束'
				}],
				tabindex: 3,
				keyword:"",
				list: [],
				pageSize:10,//分页大小
				pageNum:1,//当前页数
				pageLoading: false,
				apiLoadingStatus : false,// 用于记录是否有 api 请求正在执行
				// img: "https://images.unsplash.com/photo-1660505465468-c898ea7ff674?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHx0b3BpYy1mZWVkfDQ2fHhqUFI0aGxrQkdBfHxlbnwwfHx8fA%3D%3D&auto=format&fit=crop&w=200&q=90",
			}
		},
		onLoad(e) {
			if (e.type) {
				this.pageNum = 1
				this.tabindex = e.type
			}
		},
		onShow() {
			this.getList()
		},
		methods: {
			confirm(e){
				this.keyword = e;
				this.init();
			},
			clear(){
				this.keyword = "";
				this.init();
			},
			// 初始化
			init(){
				this.pageNum = 1;
				this.getList();
			},
			reload() {
				//下拉刷新
				this.pageNum = 1;
				this.getList(this.tabindex,true);
			},
			getList(e,isReload){
				var requestData = {
					patientId:uni.getStorageSync('cardObj').patientId, //患者id
					userId:uni.getStorageSync('user').id, //用户id
					applyStatus:e?e:this.tabindex,//报名状态
					pageSize:this.pageSize,
					pageNum:this.pageNum,
				}
				this.apiLoadingStatus = true;
				// isReload 函数用于识别是不是下拉刷新执行的
				activityList(requestData).then(res =>{
					var zongPage = Math.ceil(Number(res.data.total / this.pageSize))
					if(this.pageNum >= 2){
						this.list = this.list.concat(res.data.records)
						// this.demoData = this.demoData.concat(demoArr);
						// 加载完成后停止加载动画
						this.$refs.guipage.stoploadmore();
						// 假定第3页加载了全部数据，通知组件不再加载更多
						// 实际开发由接口返回值来决定
						if(this.pageNum >= zongPage){
							this.$refs.guipage.nomore();
							return
						}
					} else{ // 第一页 有可能是第一次加载或者刷新
						// this.list  = [];
						this.list = res.data.records;
						this.pageLoading = false;
						// 刷新
						if(isReload){this.$refs.guipage.endReload();}
					}
					this.pageNum++;
					this.apiLoadingStatus = false;
				})
			},
			tabClick(index) {
				this.pageNum = 1
				this.tabindex = index
				this.getList(this.tabindex)
			},
			details(item,type){
				if (item) {
					let self = this
					if (item.issueStatus == 1 && type == 0) {//报名
						uni.showModal({
							title: '温馨提醒', //提示标题
							content: '您是否确定要报名此活动？', //提示内容
							cancelText: "取消", // 取消按钮的文字  
							confirmText: "确认", // 确认按钮的文字  
							confirmColor: '#C59F79',
							cancelColor: '#000',
							success: function(res) {
								if (res.confirm) { //confirm为ture，代表点击了确定
									// console.log('用户点击确定');
									addActivityByPatient({
										activityId:item.id,//活动id
										patientId:uni.getStorageSync('user').id,//用户id
									}).then(res =>{
										if (res.code == 200) {
											self.$common.msg("活动报名成功")
											self.list = []
											self.pageLoading = true;
											setTimeout(() => {
												self.pageLoading = false;
												self.tabClick(1)
											}, 600)
										}
									}) 
								} else if (res.cancel) { //cancel为ture，代表点击了取消
									// console.log('用户点击取消');
								}
							}
						});
					} else{
						self.$common.navTo("/pages/homeAc/activity/detail?id="+item.id)
					}
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	
	.gui-scroll-x-items{width: 710rpx; border-top-left-radius: 20rpx;border-top-right-radius: 20rpx; box-shadow: 10rpx 10rpx 20rpx #eee;}
	.gui-scroll-image{width:100%; height:200rpx; border-top-left-radius: 20rpx;border-top-right-radius: 20rpx;}
	// .demo-close {
	// 	width: 100rpx;
	// 	height: 100rpx;
	// 	line-height: 100rpx;
	// 	opacity: 0.88;
	// 	text-align: center;
	// 	font-size: 58rpx;
	// }

	.demo-lr {
		flex: 1;
		border-radius: 20rpx;
		padding: 20rpx;
		.code{
			padding-top: 20rpx;
			word-spacing:2;
			font-size: 80rpx;
			font-weight: bold;
			text-align: center;
			letter-spacing:10rpx
		}
	}

	// .demo-lr-items {
	// 	text-align: center;
	// 	overflow: hidden;
	// }

	.record {
		display: flex;
		flex-direction: column;

		.record-list {
			display: flex;
			flex-direction: column;
			padding: 40rpx;

			.gui-card-body {
				box-shadow: 6rpx 6rpx 20rpx #eee;
				padding: 40rpx;
				border-radius: 20rpx;
				margin-bottom: 30rpx;
				font-size: 30rpx;

				.btn {
					flex: 1;
					display: flex;
					flex-direction: row;
					justify-content: flex-end;
					margin-top: 20rpx;

					.btn-item {
						border-radius: 70rpx;
						border: 2rpx solid #C59F79;
						padding: 10rpx 30rpx;
						color: #C59F79;
					}
				}

				.num {
					text-align: right;
					display: flex;
					flex-direction: row;
					padding-top: 20rpx;
					font-size: 30rpx;
					align-items: center;
					justify-content: space-between;
				}

				.gui-cate-product-list {
					margin-top: 40rpx;

					.gui-cate-pimg {
						width: 150rpx;
						height: 150rpx;
						border-radius: 20rpx;
					}

					.gui-cate-pbody {
						margin-left: 30rpx;
						display: flex;
						flex: 1;
						flex-direction: column;
						justify-content: space-between;
						.gui-text{
							font-size: 30rpx;
						}
						.gui-cate-price{
							font-size: 30rpx;
						}
						.gui-space-between {
							align-items: center;
						}
					}
				}

				.card-top {
					display: flex;
					flex-direction: row;
					justify-content: space-between;
					.right{
						display: flex;
						flex-direction: row;
						align-items: center;
					}
					// align-items: center;

					.shop-row {}

					.gui-icons {
						line-height: 40rpx;
					}

					.card-top-name {
						margin-right: 20rpx;
					}
				}
			}
		}

		.record-tab {
			display: flex;
			flex-direction: row;
			justify-content: space-between;

			.record-tab-item {
				text-align: center;
				flex: 1;

				.record-tab-item-name-i {
					color: #C59F79;
					border-bottom: 2rpx solid #C59F79 !important;
				}

				.record-tab-item-name {
					padding: 20rpx 0;
					border-bottom: 2rpx solid #fff;
				}
			}
		}
	}
</style>
