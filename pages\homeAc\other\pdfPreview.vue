<template>
  <view class="pdf-content">
    <!-- 加载中提示 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-text">PDF生成中，请稍候...</view>
    </view>

    <!-- 错误提示 -->
    <view v-if="error" class="error-container">
      <view class="error-text">{{error}}</view>
      <button @click="retry" class="retry-btn">重试</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 患者ID和就诊ID
      patientId: "",
      visitId: "",
      // 加载状态
      loading: false,
      error: ""
    }
  },
  onLoad(option) {
    console.log('PDF预览页面接收到的参数:', option)

    if (option.patientId && option.visitId) {
      this.patientId = option.patientId
      this.visitId = option.visitId
      this.previewPDF()
    } else {
      this.error = '缺少必要参数'
    }

    uni.setNavigationBarTitle({
      title:'营养干预方案报告'
    })
  },
  methods: {
    // 预览PDF
    previewPDF() {
      if (!this.patientId || !this.visitId) {
        this.error = '缺少必要参数'
        return
      }

      this.loading = true
      this.error = ''

      // 构建PDF预览URL
      const baseUrl = this.$common.domain || ''
      const token = uni.getStorageSync('mbtoken') || ''
      const pdfUrl = `${baseUrl}/wx/heath/nutritionIntervention/previewPDF?patientId=${this.patientId}&visitId=${this.visitId}&Authorization=${encodeURIComponent('Bearer ' + token)}`

      console.log('PDF预览URL:', pdfUrl)

      // 根据平台选择不同的预览方式
      switch (uni.getSystemInfoSync().platform) {
        case "android":
        case "ios":
          // APP端使用下载后打开的方式
          this.openPDFInApp(pdfUrl)
          break
        default:
          // 小程序端使用下载后打开的方式
          this.openPDFInMiniProgram(pdfUrl)
          break
      }
    },

    // APP端打开PDF
    openPDFInApp(url) {
      uni.downloadFile({
        url: url,
        success: (res) => {
          this.loading = false
          if (res.statusCode === 200) {
            uni.openDocument({
              filePath: res.tempFilePath,
              success: () => {
                console.log("打开PDF成功")
                // 打开成功后返回上一页
                setTimeout(() => {
                  uni.navigateBack()
                }, 1000)
              },
              fail: (err) => {
                console.error("打开PDF失败:", err)
                this.error = '打开PDF失败，请稍后重试'
              }
            })
          } else {
            this.error = 'PDF下载失败，请稍后重试'
          }
        },
        fail: (err) => {
          this.loading = false
          console.error("下载PDF失败:", err)
          this.error = 'PDF下载失败，请检查网络连接'
        }
      })
    },

    // 小程序端打开PDF
    openPDFInMiniProgram(url) {
      uni.downloadFile({
        url: url,
        success: (res) => {
          this.loading = false
          if (res.statusCode === 200) {
            uni.openDocument({
              filePath: res.tempFilePath,
              success: () => {
                console.log("打开PDF成功")
                // 打开成功后返回上一页
                setTimeout(() => {
                  uni.navigateBack()
                }, 1000)
              },
              fail: (err) => {
                console.error("打开PDF失败:", err)
                this.error = '打开PDF失败，请稍后重试'
              }
            })
          } else {
            this.error = 'PDF下载失败，请稍后重试'
          }
        },
        fail: (err) => {
          this.loading = false
          console.error("下载PDF失败:", err)
          this.error = 'PDF下载失败，请检查网络连接'
        }
      })
    },

    // 重试
    retry() {
      this.previewPDF()
    }
  },

}
</script>

<style scoped>
.pdf-content {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.loading-container, .error-container {
  text-align: center;
}

.loading-text, .error-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.retry-btn {
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.retry-btn:active {
  background-color: #337ecc;
}
</style>
