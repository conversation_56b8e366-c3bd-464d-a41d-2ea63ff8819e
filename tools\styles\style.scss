@import 'config.scss';

// 字体大小
@each $key,$val in $sizes {
	.fs-#{$key}{
		font-size: $val+rpx;
	}
}

// 颜色
@each $key,$val in $colors {
	//字体颜色 .text-primary
	.text-#{$key} {
		color: $val ;
	}
	//背景颜色 .bg-10
	.bg-#{$key} {
		background-color: $val;
	}
}
//文本对齐 
@each $val in $text-align {
	.text-#{$val}{
		text-align: #{$val};
	}
}
// 宽高
@each $roomKey,$roomVal in $space-room {
	//百分比宽高 w-100 h-100
	@each $percentKey,$percentVal in $percent-sizes {
		.#{$roomKey}-#{$percentKey} {
			#{$roomVal}:#{$percentVal};
		}
	}
	//rpx宽高 w100 h100
	@each $size in $space-sizes {
		.#{$roomKey}#{$size} {
			#{$roomVal}:#{$size}rpx;
		}
		.b-radius-#{$size}{
		  border-radius: #{$size}rpx;
		}
	}
	
}

// 间距
@each $typeKey,$typeVal in $space-type {
	@each $dirKey,$dirVal in $space-direction {
		@each $size in $space-sizes {
			.#{$typeKey}#{$dirKey}-#{$size}{
				#{$typeVal}-#{$dirVal}:#{$size}rpx;
			}
			.#{$typeKey}-#{$size}{
				#{$typeVal}:#{$size}rpx;
			}
			.#{$typeKey}x-#{$size}{
				#{$typeVal}-left:#{$size}rpx;
				#{$typeVal}-right:#{$size}rpx;
			}
			.#{$typeKey}y-#{$size}{
				#{$typeVal}-top:#{$size}rpx;
				#{$typeVal}-bottom:#{$size}rpx;
			}
		}
	}
}

// 字体间距
@each $space in $space-letters {
	.letter-space-#{$space}{
		letter-spacing: #{$space}rpx;
	}
}

// 字体间距
@each $over in $over-flow {
	.over-#{$over}{
		overflow: #{$over};
	}
}

// 绝对定位
@each $pos in $position {
	.pos-#{$pos}{
		position:#{$pos};
	}
}

// flex主轴对齐
@each $key,$val in $flex-jc {
    .jc-#{$key}{
        justify-content: $val;
    }
}

// flex纵轴对齐
@each $key,$val in $flex-ai {
    .ai-#{$key}{
        align-items: $val;
    }
}

// 盒模型
@each $key,$val in $box-sizing {
    .box-size-#{$key}{
        box-sizing: $val;
    }
}

// 绝对定位
@each $val in $ellipsis {
	.ellipsis-#{$val}{
		text-overflow: -o-ellipsis-lastline;
		 overflow: hidden;
		 text-overflow: ellipsis;
		 display: -webkit-box;
		 -webkit-line-clamp: $val;
		 line-clamp: $val;
		 -webkit-box-orient: vertical;
	}
}


/* 圆形 */
.circle {
	border-radius: 50%;
}
//下边框
.border-b{
	border-bottom: 1rpx solid map-get($map: $colors, $key:'grey-ee');
}

.border-white{
	border: 1rpx solid map-get($map: $colors, $key:'white');
}
/* 加粗 */
.font-bold {
	font-weight: bold;
}
//阴影
.card-shadow{
	box-shadow: 0rpx 5rpx 60rpx #eee;
}
// 弹性布局
.d-iblock{
    display: inline-block;
}
.d-flex{
    display: flex;
}
.d-iflex{
    display: inline-flex
}
.flex-row{
    flex-direction: row;
}
.flex-coloum{
    flex-direction: column;
}
.flex-1{
    flex: 1;
}
.flex-wrap{
    flex-wrap: wrap;
}



