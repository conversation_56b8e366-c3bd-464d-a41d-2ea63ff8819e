<template>
	<view class="gui-padding">
		<!-- 测评 -->
		<view v-if="!completion">
			<view v-if="questionnaireArr.length > 0">
				<view class="py-20 fs-32 text-zhutis">注：请选择最符合您实际情况的选项，回答以下问题</view>
				<view class="fs-40 gui-bold gui-flex gui-justify-content-end mb-20 gui-bg-white">
					<view class="pageNumstyle">
						{{answerPage[answeKey].pageNum?(answerPage[answeKey].pageNum + 1) : 1}}/{{questionnaireArr.length}}
					</view>
				</view>
				<view :class="['animate', animations[animationIndex]]">
					<view class="fs-36 mb-30">{{questionnaireData.subjectName}}</view>
					<view class="fs-36 py-40 px-60 cpbg mx-30">
						<view @click="answer(subject,questionnaireData)"
							v-if="questionnaireData.subjectType == 'radio' && questionnaireData.subjectDict && String(questionnaireData.subjectDict).includes(',')"
							v-for="subject in String(questionnaireData.subjectDict).split(',')">
							<view class="answertext"
								:class="answerList[answeKey].answerAllList[Number(answerPage[answeKey].pageNum)].answer === subject?'saveanswer':''">
								{{subject}}
							</view>
						</view>
						<view class="my-40 mx-60 align-center"
							v-if="questionnaireData.subjectType == 'checkbox' && questionnaireData.subjectDict">
							<view v-for="subject in String(questionnaireData.subjectDict).split(/[,，]/g)">
								<view class="answertext" @click="answer(subject,questionnaireData)"
									:class="checkData.indexOf(subject) > -1?'saveanswer':''">
									{{subject}}
								</view>
							</view>
							<view class="fs-26 gui-color-gray text-center">
								提示：以上选项可多选
							</view>
						</view>
						<view class="w-100" v-if="questionnaireData.subjectType == 'text'">
							<input @blur="val =>{textareaBlur(val,questionnaireData)}"
								v-model="questionnaireData.answer" style="height: 120rpx;padding-left: 20rpx;"
								type="text" class="gui-border fs-30" placeholder="请输入"
								maxlength="100" /><!-- :disabled="obj.taskInfo.status != 0" -->
						</view>
						<view class="w-100 pos-relative" v-if="questionnaireData.subjectType == 'textarea'">
							<textarea @blur="val =>{textareaBlur(val,questionnaireData)}"
								v-model="questionnaireData.answer" placeholder="请输入" maxlength="200"
								class="box-size-border gui-border w-100 p-20 fs-30" placeholder-class="fs-28" />
							<view class="pos-absolute text-grey-b2" style="bottom: 50rpx;right: 50rpx;">
								{{questionnaireData.answer ? questionnaireData.answer.length : 0}}/200
							</view>
						</view>
					</view>
				</view>
				<view style="position: absolute;bottom: 70rpx;width: 690rpx;">
					<view class="gui-flex gui-justify-content-center">
						<view @click="previousPage" v-if="answerPage[answeKey].pageNum > 0"
							class="gui-flex gui-justify-content-center mt-40 mr-20">
							<view class="contentBnt bg-white">上一页</view>
						</view>
						<!-- obj.taskInfo.status == 1 || -->
						<view @click="nextPage"
							v-if="(showBnt || questionnaireData.subjectType == 'checkbox' || questionnaireData.subjectType == 'text' || questionnaireData.subjectType == 'textarea') && questionnaireArr.length != (answerPage[answeKey].pageNum + 1) "
							class="gui-flex gui-justify-content-center mt-40 mr-20">
							<view class="contentBnt bg-white">下一页</view>
						</view>
						<view @click="save"
							v-if="questionnaireArr.length && questionnaireArr.length === (answerPage[answeKey].pageNum+1) && !showBnt"
							class="gui-flex gui-justify-content-center mt-40 ">
							<view class="saveBnt bg-zhuti">提 交</view>
						</view>
					</view>
				</view>
			</view>
			<view v-else>
				<gui-empty>
					<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
						<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
					</view>
					<text slot="text" class="gui-text-small gui-block-text gui-text-center gui-margin-top"
						style="color:#B2B2B2;">暂无数据</text>
				</gui-empty>
			</view>
		</view>
		<view style="height: 60rpx;"></view>
		<!-- 结果 -->
		<view class="propose" v-if="completion">
			<view v-if="questionResult.proposalList.length">
				<view v-for="(proposal,index) in questionResult.proposalList">
					<!--结果标题-->
					<view>
						<view class="propose_result" v-if="questionResult.proposalList.length===1">
							<image src="../../../static/log/bookmark.svg" />
							<text> 测评结果：</text>
						</view>
						<view class="propose_result" v-if="questionResult.proposalList.length>1">
							<image src="../../../static/log/bookmark.svg" />
							<text>测评结果{{ titleArray[index] }}：</text>
						</view>
						<view class="propose_net">
							<view class="fs-32">
								根据测评您的体质{{ questionResult.type[index] && questionResult.type[index].replaceAll("评估问卷","") || '' }}。
							</view>
						</view>
						<!--结果内容 v-if="String(proposal).includes('\n\n')"-->
						<view v-for="item in String(proposal).split('\n\n')">
							<view v-if="String(item).includes('\n')" v-for="(content, index) in String(item).split('\n')">
								<view class="propose_result" v-if="index === 0">
									<text>{{ content || '' }}</text>
								</view>
								<view class="propose_net fs-32" v-if="index > 0">
									<view>
										<span>{{ content || '' }}</span>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<!-- @click="chat"$common.navTo('/pages/IM/chat?key=' + 1) -->
				<view class="end_text">
					<view>以上测评仅做参考</view>
					<!-- <view>进一步了解体质调理可<span class="end_text_span">【咨询医生】</span></view> -->
				</view>
				<!-- <view class="consult" @click="$common.navTo('/pages/IM/chat?key=' + 1)">
					<image src="https://img.starup.net.cn/xmkj/zwb/img/zy_h_onlcon.png"
						style="max-height: 90rpx;max-width: 90rpx;"></image>
					<view class="mt-10">在线咨询</view>
				</view> -->
			</view>
			<view v-else class="end_text">没有匹配结果，请重新测评！</view>
		</view>
	</view>
</template>

<script>
	var graceJS = require('@/GraceUI5/js/grace.js');
	import {
		constitutionSubjectContent,
		constitutionFillContent,
		constitutionSaveContent,
		appraisalSubjectContent,
		appraisalSaveContent,
		appraisalFillContent,
	} from '@/api/home.js'
	export default {
		data() {
			return {
				domainType: this.$common.domainType,
				animationIndex: 1,
				animations: ['fadeOutLeft · 左侧淡出', 'bounceInRight · 右侧飞入', 'bounceInLeft · 左侧飞入', 'fadeOutRight · 右侧淡出'],
				questionId: '',
				questionName: '',
				questionType:'',
				questionnaireArr: [],
				questionnaireData: [],
				/* 问卷内容 */
				questionnaire: {
					questionName: undefined,
					questionType: undefined,
					constitutionType: undefined,
					contentListen: [{
						criteriaType: undefined,
						subjectName: undefined,
						subjectType: undefined,
						isRequired: undefined,
						subjectDict: undefined,
						subjectOrder: undefined
					}]
				},
				/* 问卷内容集合 */
				questionnaireList: [],
				answerList: [], //所有问卷答案合集
				arrs: [],
				answerListKey: 0,
				// answerAllList: [],///* 所有答案集合 */用answerList替换
				answerPage: [],
				/* 下一页 */
				answeKey: 0,
				checkData: [], //多选项的数据
				// pageNum: 0,	/* 下一页 answerPage替换*/
				/* 完成状态 */
				completion: false,
				/* 测评结果 */
				questionResult: {
					type: undefined,
					proposalList: []
				},
				lock: false,
				titleArray: ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十"],
				// 判断重新测评
				type: '',
				answerBasicsList: ['没有', '很少', '有时', '经常', '总是'],
				showBnt:false,
			}
		},
		onShow() {
			if (uni.getStorageSync("answerList") && uni.getStorageSync("answerPage")) {
				this.$set(this, 'answerPage', uni.getStorageSync("answerPage"))
				this.$set(this, 'answerList', uni.getStorageSync("answerList"))
			}
		},
		onLoad(option) {
			var systemInfo = graceJS.system();
			if (Object.keys(option).length !== 0) {
				this.questionId = option.questionId
				this.questionName = option.questionName;
				this.type = option.type
				this.questionType = option.questionType
				if (option.questionName == '中医体质综合问卷测评') {
					if (this.type === '2') {
						this.completion = false
						this.appraisalContent()
					} else if (this.type === '1') {
						this.loadSubjectAnswer()
					}
				} else {
					this.completion = false
					if (this.type === '2') {
						this.loadSubjectContent()
					} else if(this.type === '1' && this.questionType == 1) {
						this.getquestionAnswer(option.questionId)
					}
				}
			}
		},
		methods: {
			chat() {
				if (this.$common.domainType == 3) {
					return
				}
				// this.$common.openDyMsg(0,1);
				this.$common.navTo('/pages/IM/chat?key=0')
			},
			/* 获取问卷题目内容 */
			appraisalContent() {
				appraisalSubjectContent(5).then(res => {
					this.questionnaireArr = [...res.data[0].contentListen, ...res.data[1].contentListen, ...res
						.data[2].contentListen, ...res.data[3].contentListen, ...res.data[4].contentListen, ...
						res.data[5].contentListen, ...res.data[6].contentListen, ...res.data[7].contentListen,
						...res.data[8].contentListen
					]
					if (this.questionnaireArr.length > 0) {
						var keyid = this.answerPage.findIndex(e => e.questionId == this.questionId)
						if (keyid == -1) {
							this.answerPage.push({
								questionId: this.questionId,
								pageNum: 0
							})
							this.$forceUpdate();
							this.$set(this, 'answeKey', this.answerPage.length - 1)
							this.$set(this, 'questionnaireData', this.questionnaireArr[0])
						} else {
							this.$set(this, 'answeKey', keyid)
							this.$set(this, 'questionnaireData', this.questionnaireArr[this.answerPage[keyid]
								.pageNum])
						}
					}
				})
			},
			/* 获取问卷题目内容 */
			loadSubjectContent() {
				constitutionSubjectContent(this.questionId).then(res => { //以前的体质测评问卷入参是 数字 5
					this.questionType = res.data[0].questionType
					this.questionnaireArr = res.data[0].contentListen
					if (this.questionnaireArr.length > 0) {
						var keyid = this.answerPage.findIndex(e => e.questionId == res.data[0].id)
						if (keyid == -1) {
							this.answerPage.push({
								questionId: res.data[0].id,
								pageNum: 0
							})
							this.$forceUpdate();
							this.$set(this, 'answeKey', this.answerPage.length - 1)
							this.$set(this, 'questionnaireData', this.questionnaireArr[0])
						} else {
							this.$set(this, 'answeKey', keyid)
							this.$set(this, 'questionnaireData', this.questionnaireArr[this.answerPage[keyid]
								.pageNum])
						}
					}
				})
			},
			textareaBlur(e, data) {
				this.answer(e.detail.value, data)
			},
			// 答案选择事件
			answer(value, data) {
				if (!this.lock) {
					this.lock = true
					if (!value) {
						this.$common.msg("请勾选再提交！")
						return
					}
					if (data.subjectType === 'checkbox') {
						if (typeof(this.checkData) == 'string') {
							this.checkData = this.checkData.split(',')
						}
						if (this.checkData.indexOf(value) > -1) {
							this.checkData.splice(this.checkData.indexOf(value), 1)
						} else {
							this.checkData.push(value)
						}
					}
					var checkDatas = this.checkData
					var answerAll = {
						...data,
						answer: data.subjectType === 'checkbox' ? checkDatas.join(',') : value,
						patientId: uni.getStorageSync("cardObj")?.patientId || '',
						fraction: this.answerBasicsList.findIndex(item => item === value) + 1
					}
					var answerkey = this.answerList.findIndex(e => e.questionId == this.questionId)
					if (answerkey == -1) {
						this.$set(this.arrs, this.answerPage[this.answeKey].pageNum, answerAll)
						this.$set(this.answerList, this.answerList.length, {
							questionId: this.questionId,
							answerAllList: this.arrs
						})
						this.$set(this, 'answerListKey', this.answerList.length - 1)
					} else {
						this.$set(this, 'answerListKey', answerkey)
						this.$set(this.answerList[this.answerListKey].answerAllList, this.answerPage[this.answeKey]
							.pageNum, answerAll)
					}
					this.$forceUpdate();
					if (data.subjectType == 'radio' && (Number(this.answerPage[this.answeKey].pageNum + 1) === this
							.questionnaireArr.length)) {
						setTimeout(() => {
							this.save();
						}, 200);
					}
					if (data.subjectType == 'radio' && (Number(this.answerPage[this.answeKey].pageNum + 1) !== this
							.questionnaireArr.length)) {
						setTimeout(() => {
							this.lock = false
							this.nextPage()
						}, 200)
					} else {
						this.lock = false
					}
				}
			},

			getquestionAnswer(typeId) {
				var patientId = uni.getStorageSync("cardObj")?.patientId || ''
				var memberId = patientId ? '' : uni.getStorageSync("user")?.id
				constitutionFillContent({
					patientId:patientId || '',
					questionId:typeId?typeId:this.questionId,
					memberId:patientId ? '' : memberId
				}).then(res=>{
					this.questionnaireArr = res.data.answerList
					this.showBnt = true;
					if (this.questionnaireArr.length > 0) {
						var keyid = this.answerPage.findIndex(e => e.questionId == this.questionId)
						if (keyid == -1) {
							this.answerPage.push({
								questionId: this.questionId,
								pageNum: 0
							})
							this.$forceUpdate();
							this.$set(this, 'answeKey', this.answerPage.length - 1)
							this.$set(this, 'questionnaireData', this.questionnaireArr[0])
						} else {
							this.$set(this, 'answeKey', keyid)
							this.$set(this, 'questionnaireData', this.questionnaireArr[this.answerPage[keyid]
								.pageNum])
						}
						var answerListKey = this.answerList.findIndex(e => e.questionId == this.questionId)
						if (answerListKey== -1) {
							this.answerList.push({
								questionId: this.questionId,
								answerAllList: res.data.answerList
							})
							this.$forceUpdate();
						} else{
							this.$set(this.answerList[answerListKey], 'answerAllList', res.data.answerList)
							this.$set(this.answerList[answerListKey], 'questionId', this.questionId)

						}
					}
				})
			},
			/* 获取体质问卷答案内容 */
			loadSubjectAnswer() {
				var patientId = uni.getStorageSync("cardObj")?.patientId || ''
				var memberId = patientId ? '' : uni.getStorageSync("user")?.id
				appraisalFillContent({
					patientId,
					memberId
				}).then(res => {
					this.ready = true
					if (res.data) {
						// 已测评
						this.completion = true
						this.questionResult = res.data?.questionResult || {
							type: undefined,
							proposalList: []
						}
						uni.setNavigationBarTitle({
							title: '测评报告'
						})
					} else {
						// 未测评
						this.completion = false
						this.appraisalContent()
					}
				})
			},
			/* 下一页 */
			nextPage() {
				this.$set(this, 'animationIndex', 0)
				if (this.showBnt) {
					this.$set(this.answerPage[this.answeKey], 'pageNum', this.answerPage[this.answeKey].pageNum + 1)
					this.$set(this, 'questionnaireData', this.questionnaireArr[this.answerPage[this.answeKey].pageNum])
					if (this.questionnaireData.subjectType === 'checkbox') {
						let checkarr = this.questionnaireData.answer.split(',')
						this.$set(this, 'checkData', checkarr || [])
					}
					setTimeout(() => {
						this.$set(this, 'animationIndex', 1)
					}, 300);
					return
				}
				this.$set(this.answerPage[this.answeKey], 'pageNum', this.answerPage[this.answeKey].pageNum + 1)
				uni.setStorageSync('answerPage', this.answerPage)
				uni.setStorageSync('answerList', this.answerList)
				this.$set(this, 'questionnaireData', this.questionnaireArr[this.answerPage[this.answeKey].pageNum])
				if (this.questionnaireData.subjectType !== 'radio' && this.answerList[this.answeKey].answerAllList
					.length >
					this.answerPage[this.answeKey].pageNum) {
					let answerarr = this.answerList[this.answeKey].answerAllList[Number(this.answerPage[this
							.answeKey]
						.pageNum)].answer
					if (this.questionnaireData.subjectType === 'checkbox') {
						let checkarr = answerarr.split(',')
						this.$set(this, 'checkData', checkarr || [])
					} else {
						this.$set(this.questionnaireData, 'answer', answerarr || '')
					}
				}
				setTimeout(() => {
					this.$set(this, 'animationIndex', 1)
				}, 100);
			},
			/* 上一页 */
			previousPage() {
				this.$set(this, 'animationIndex', 3)
				if (this.showBnt) {
					this.$set(this.answerPage[this.answeKey], 'pageNum', this.answerPage[this.answeKey].pageNum - 1)
					this.$set(this, 'questionnaireData', this.questionnaireArr[this.answerPage[this.answeKey].pageNum])
					if (this.questionnaireData.subjectType === 'checkbox') {
						let checkarr = this.questionnaireData.answer.split(',')
						this.$set(this, 'checkData', checkarr || [])
					}
					setTimeout(() => {
						this.$set(this, 'animationIndex', 2)
					}, 300);
					return
				}
				this.$set(this.answerPage[this.answeKey], 'pageNum', this.answerPage[this.answeKey].pageNum - 1)
				// uni.setStorageSync('pageNum', this.pageNum)
				uni.setStorageSync('answerPage', this.answerPage)
				uni.setStorageSync('answerList', this.answerList)
				this.$set(this, 'questionnaireData', this.questionnaireArr[this.answerPage[this.answeKey].pageNum])
				setTimeout(() => {
					this.$set(this, 'animationIndex', 2)
				}, 100);
				if (this.answerList[this.answeKey].answerAllList.length >= this.answerPage[this.answeKey]
					.pageNum) {
					let answerarr = this.answerList[this.answeKey].answerAllList[Number(this.answerPage[this
							.answeKey]
						.pageNum)].answer
					if (this.questionnaireData.subjectType === 'checkbox') {
						let checkarr = answerarr.split(',')
						this.$set(this, 'checkData', checkarr || [])
					} else {
						this.$set(this.questionnaireData, 'answer', answerarr || '')
					}
				}
			},
			/* 保存 */
			save() {
				var patientId = uni.getStorageSync("cardObj")?.patientId || ''
				var memberId = patientId ? '' : uni.getStorageSync("user")?.id
				this.answerList[this.answerListKey].answerAllList.map(item => {
					item.memberId = memberId
				})
				if (this.answerList[this.answerListKey].answerAllList.length < this.questionnaireArr.length) {
					this.$common.msg("请选择答案")
					return
				}
				uni.showModal({
					title: '提示',
					content: '测评内容已完成，确认提交问卷吗？',
					cancelText: "取消",
					confirmText: "提交",
					confirmColor: '#576B95',
					cancelColor: '#000000',
					success: res => {
						if (res.confirm) {
							if (this.questionName == '中医体质综合问卷测评') {
								appraisalSaveContent(
									JSON.parse(JSON.stringify(this.answerList[this.answerListKey]
										.answerAllList))
								).then(res => {
									if (res.code === 200) {
										this.$common.msg("问卷提交成功", "success")
										setTimeout(() => {
											uni.removeStorageSync('questionnaireList')
											this.answerList.splice(this.answerListKey,
												1)
											uni.setStorageSync('answerList', this
												.answerList)
											this.answerPage.splice(this.answeKey, 1)
											uni.setStorageSync('answerPage', this
												.answerPage)
											// this.pageNum = 0
											this.arrs = []
											this.loadSubjectAnswer();
											// this.completion = true
											this.$forceUpdate();
										}, 1000)
									}
								})
							} else {
								constitutionSaveContent(
									JSON.parse(JSON.stringify(this.answerList[this.answerListKey]
										.answerAllList))
								).then(res => {
									if (res.code === 200) {
										this.$common.msg("问卷提交成功", "success")
										setTimeout(() => {
											uni.removeStorageSync('questionnaireList')
											this.answerList.splice(this.answerListKey,
												1)
											uni.setStorageSync('answerList', this
												.answerList)
											this.answerPage.splice(this.answeKey, 1)
											uni.setStorageSync('answerPage', this
												.answerPage)
											// this.pageNum = 0
											this.arrs = []
											if (this.questionType == 1) {
												this.$common.navLaunch('/pages/homeAc/evaluation/list')
												// this.getquestionAnswer(this.questionId)
											}else{
												this.$common.navLaunch(
													'/pages/homeAc/evaluation/details?sum=' +
													res.data + '&questionId=' + this
													.questionId + '&memberId=' + memberId)
											}
										}, 1000)
									}
								})
							}

						}
					}
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	/* 引入动画库 */
	@import "@/GraceUI5/css/animate.css";

	/* 定义动画修饰样式 */
	.animate {
		animation-duration: 1s;
		animation-timing-function: linear;
	}

	.cpbg {
		border: 1px solid #C59F79;
		border-radius: 10rpx;
	}

	.pageNumstyle {
		padding: 10rpx 20rpx 10rpx 40rpx;
		border: 1px solid #C59F79;
		border-right: none;
		border-bottom-left-radius: 50rpx;
		border-top-left-radius: 50rpx;
	}

	.answertext {
		border: 1px solid #e5e5e6;
		border-radius: 50rpx;
		padding: 20rpx 40rpx;
		margin-bottom: 20rpx;
		text-align: center;
	}

	.saveanswer {
		background-color: #C59F79;
		color: #ffffff;
	}

	.contentBnt,
	.saveBnt {
		border: 1px solid #C59F79;
		bottom: 30rpx;
		width: 220rpx;
		height: 80rpx;
		font-size: 34rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 5rpx;
	}

	.saveBnt {
		color: #fff;
	}

	.propose {
		width: 100%;
		position: relative;

		.consult {
			background-color: #fff;
			// width:80rpx;
			padding: 10rpx;
			border-radius: 10rpx;
			// height:80rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			box-shadow: 0 15rpx 30rpx #d0d0d0;
			align-items: center;
			position: fixed;
			right: 80rpx;
			bottom: 20%;
		}

		.propose_result {
			padding: 15rpx 15rpx 15rpx 0;

			image {
				width: 24px;
				height: 24px;
				font-size: 24px;
				line-height: 24px;
				vertical-align: middle;
				color: rgba(0, 186, 173, 1);
			}

			text {
				width: 70px;
				height: 21px;
				font-size: 30rpx;
				text-align: left;
				font-weight: bold;
				line-height: 24px;
				padding-left: 8rpx;
				color: rgba(80, 80, 80, 1);
			}
		}

		.propose_net {
			width: 100%;
			height: auto;
			margin-top: 5rpx;
			padding: 0 10rpx;
			text-indent: 2em;
			line-height: 52rpx;
			overflow-x: scroll;

			text {
				width: 339px;
				height: 63px;
				font-size: 30rpx;
				text-align: left;
				text-indent: 2em;
				line-height: 52rpx;
				letter-spacing: 2px;
				color: rgba(80, 80, 80, 1);
			}

			span {
				display: block;
			}
		}

		.end_text {
			// width: 325px;
			height: 42px;
			font-size: 30rpx;
			text-align: center;
			margin: 20% auto 0;
			color: rgba(212, 48, 48, 1);

			.end_text_span {
				color: #0055ff;
				text-decoration: underline
			}
		}
	}
</style>
