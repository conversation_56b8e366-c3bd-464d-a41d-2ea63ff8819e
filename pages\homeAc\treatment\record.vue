<template>
	<gui-page ref="guipage" :isLoading="pageLoading"  :apiLoadingStatus="apiLoadingStatus" :refresh="true" @reload="reload"  :loadmore="true" @loadmorefun="getList"
	:refreshBgColor="['#ffffff','#ffffff','#ffffff','#c59f79']"
	statusBarStyle="background: linear-gradient(90deg,#ffffff,#ffffff);"
	headerStyle="background: linear-gradient(90deg,#c59f79,#c1c1c1);">
		<view slot="gBody" class="record gui-flex1" >
			<view class="record-tab pos-fixed bg-white" style="top: 0;left: 0;width: 100%;z-index: 99;">
				<view class="record-tab-item" @click="tabClick(item.id)" v-for="(item,index) in tablist" :key="item.id">
					<view :class="['record-tab-item-name',{'record-tab-item-name-i':tabindex == item.id}]">{{item.name}}
					</view>
				</view>
			</view>
			<view class="record-list mt-100" v-if="list.length > 0">
			<!-- @click="details(item)" -->
				<view class="gui-card-body" v-for="item in list" >
					<view class="mb-20 ellipsis-1">
						预约项目：{{item.projectName || '-'}}
					</view>
					<view class="" v-if="item.cureName">
						治疗师：{{item.cureName}}
					</view>
					<view class="gui-flex gui-align-items-center">
						<view class="fs-24 pt-30">
							预约时间 ：{{$common.parseTime(item.appointmentTime , '{y}-{m}-{d} {h}:{i}')}}
						</view>
						<view v-if="item.status == 2" class="btn btn-item mr-20 gui-color-gray" >已取消</view>
						<view v-else class="btn">
							<view v-if="item.status == 0" class="btn-item mr-20" @click.stop="delProject(item.id)">取消预约</view>
							<view class="btn-item" @click.stop="codeClick(item.writeOff)">核销码</view>
						</view>
						
					</view>
				</view>
			</view>
			<view v-if="list.length <= 0">
				<gui-empty>
					<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
						<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
					</view>
					<text slot="text" 
					class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
				</gui-empty>
			</view>
			<gui-popup ref="guipopup1">
				<view class="gui-relative gui-box-shadow gui-bg-white demo-lr">
					<view>核销码</view>
					<view class="code gui-color-red">{{writeOff}}</view>
				</view>
			</gui-popup>
		</view>
	</gui-page>
</template>

<script>
	import { getCureOrder,updateOrderStatus } from '@/api/treatment.js'
	import guiPopup from '@/GraceUI5/components/gui-popup.vue'
	export default {
		components:{guiPopup},
		data() {
			return {
				tablist: [{
					id: 3,
					name: '全部'
				}, {
					id: 0,
					name: '待治疗'
				}, {
					id: 1,
					name: '已治疗'
				}],
				tabindex: 3,
				list: [],
				writeOff:0,//核销码
				pageSize:10,//分页大小
				pageNum:1,//当前页数
				pageLoading: true,
				apiLoadingStatus : false,// 用于记录是否有 api 请求正在执行
				// img: "https://images.unsplash.com/photo-1660505465468-c898ea7ff674?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHx0b3BpYy1mZWVkfDQ2fHhqUFI0aGxrQkdBfHxlbnwwfHx8fA%3D%3D&auto=format&fit=crop&w=200&q=90",
			}
		},
		onLoad(e) {
			if (e.status) {
				this.tabindex = Number(e.status)
				this.pageNum = 1
			}
		},
		onShow() {
			this.getList()
		},
		methods: {
			reload() {
				//下拉刷新
				this.pageNum = 1;
				this.getList(this.tabindex,true);
			},
			getList(e,isReload){
				var requestData = {
					patientId:uni.getStorageSync('cardObj').patientId, //用户id
					status:e?e:this.tabindex,//兑换状态
					pageSize:this.pageSize,
					pageNum:this.pageNum,
				}
				this.apiLoadingStatus = true;
				// isReload 函数用于识别是不是下拉刷新执行的
				getCureOrder(requestData).then(res =>{
					var zongPage = Math.ceil(Number(res.total / this.pageSize))
					if(this.pageNum >= 2){
						this.list = this.list.concat(res.data.rows)
						// 加载完成后停止加载动画
						this.$refs.guipage.stoploadmore();
						// 假定第3页加载了全部数据，通知组件不再加载更多
						// 实际开发由接口返回值来决定
						if(this.pageNum >= zongPage){
							this.$refs.guipage.nomore();
							return
						}
					}
					// 第一页 有可能是第一次加载或者刷新
					else{
						// this.list  = [];
						this.list = res.data.rows;
						this.pageLoading = false;
						// 刷新
						if(isReload){this.$refs.guipage.endReload();}
					}
					this.pageNum++;
					this.apiLoadingStatus = false;
				})
			},
			tabClick(index) {//状态选择
				this.pageNum = 1
				this.tabindex = index
				this.getList(this.tabindex)
			},
			delProject(e){//取消项目
				updateOrderStatus(e).then(res => {
					if (res.code == 200) {
						this.pageLoading = true;
						this.pageNum = 1;
						this.getList(this.tabindex,true);
					}
				})
			},
			codeClick(e) {//查看核销码
				if (e) {
					this.writeOff = e
					this.$refs.guipopup1.open();
				}
			},
			// details(item){
			// 	if (item) {
			// 		this.$common.navTo("/pages/integral/record-details?item="+encodeURIComponent(JSON.stringify(item)))
			// 	}
			// }
		}
	}
</script>

<style scoped lang="scss">
	.demo-close {
		width: 100rpx;
		height: 100rpx;
		line-height: 100rpx;
		opacity: 0.88;
		text-align: center;
		font-size: 58rpx;
	}

	.demo-lr {
		flex: 1;
		border-radius: 20rpx;
		padding: 20rpx;
		.code{
			padding-top: 20rpx;
			word-spacing:2;
			font-size: 80rpx;
			font-weight: bold;
			text-align: center;
			letter-spacing:10rpx
		}
	}

	.demo-lr-items {
		text-align: center;
		overflow: hidden;
	}

	.record {
		display: flex;
		flex-direction: column;

		.record-list {
			display: flex;
			flex-direction: column;
			padding: 20rpx;

			.gui-card-body {
				box-shadow: 6rpx 6rpx 20rpx #eee;
				padding: 20rpx;
				border-radius: 20rpx;
				margin-bottom: 30rpx;
				font-size: 30rpx;

				.btn {
					flex: 1;
					display: flex;
					flex-direction: row;
					justify-content: flex-end;
					margin-top: 20rpx;

					.btn-item {
						font-size: 24rpx;
						border-radius: 70rpx;
						border: 2rpx solid #C59F79;
						padding: 10rpx 30rpx;
						color: #C59F79;
					}
				}

				.num {
					text-align: right;
					display: flex;
					flex-direction: row;
					padding-top: 20rpx;
					font-size: 30rpx;
					align-items: center;
					justify-content: space-between;
				}

				.gui-cate-product-list {
					margin-top: 40rpx;

					.gui-cate-pimg {
						width: 150rpx;
						height: 150rpx;
						border-radius: 20rpx;
					}

					.gui-cate-pbody {
						margin-left: 30rpx;
						display: flex;
						flex: 1;
						flex-direction: column;
						justify-content: space-between;
						.gui-text{
							font-size: 30rpx;
						}
						.gui-cate-price{
							font-size: 30rpx;
						}
						.gui-space-between {
							align-items: center;
						}
					}
				}

				.card-top {
					display: flex;
					flex-direction: row;
					justify-content: space-between;
					.right{
						display: flex;
						flex-direction: row;
						align-items: center;
					}
					// align-items: center;

					.shop-row {}

					.gui-icons {
						line-height: 40rpx;
					}

					.card-top-name {
						margin-right: 20rpx;
					}
				}
			}
		}

		.record-tab {
			display: flex;
			flex-direction: row;
			justify-content: space-between;

			.record-tab-item {
				text-align: center;
				flex: 1;

				.record-tab-item-name-i {
					color: #C59F79;
					border-bottom: 2rpx solid #C59F79 !important;
				}

				.record-tab-item-name {
					padding: 20rpx 0;
					border-bottom: 2rpx solid #fff;
				}
			}
		}
	}
</style>
