<template>
	<gui-page :customHeader="false">
		<view slot="gBody" >
			<!-- 视频播放区域 -->
			<view v-show="model.videoUrl">
				<video id="myVideo" 
				class="gui-course-video" 
				:src="model.videoUrl" 
				:poster="model.filePath" controls></video>
			</view>
			<view class="px-30">
				<view class="font-bold text-black fs-36 mb-20 mt-30" id="title_value" >
					{{model.articleName}}
				</view>
				<view class="row-model mb-30">
					<text class="text-grey-b2 fs-24">{{model.createTime}}</text>
					<view class="gui-flex ml-30">
						<text class="gui-icons">&#xe609;</text>
						<text class="ml-10">{{model.readNum || 0}}</text>
					</view>
					<view class="gui-flex ml-30">
						<text class="gui-icons">&#xe622;</text>
						<text class="ml-10">{{model.shareNum || 0}}</text>
					</view>
				</view>
				<mp-html :content="model.content" />
			</view>
		</view>
	</gui-page>
</template>

<script>
	import {getArticle,taskgetArticle1,taskgetArticletoken} from '@/api/task.js'
	import uParse from '@/components/u-parse/u-parse.vue'
	export default {
		components:{uParse},
		data() {
			return {
				id:null,
				type:null,
				model:{
					articleName:'',
					content:'',
					createTime:"",
					readNum:'',
					articleId:''
				}
			}
		},
		onShow() {

		},
		onLoad(option) {
			wx.showShareMenu({
			    withShareTicket:true,
			    menus:["shareAppMessage","shareTimeline"]
			})
			let self = this;
			// this.model = option.item?JSON.parse(decodeURIComponent(option.item)):this.model
			this.model.articleId = option.articleId?option.articleId:this.model.articleId
      this.taskgetArticle1()
			// if(uni.getLaunchOptionsSync().scene == 1154){
			// 	this.taskgetArticle1()
			// }else{
			// 	this.taskgetArticletoken()
			// }
			// 积分
			if(uni.getStorageSync("token")){
				this.getIntegral()
			}
			// this.$common.isRegAndLogin(res => {
			// 	if (res === 0) {
			// 		//跳转授权注册页
			// 		self.$common.noLoginBox();
			// 	} else if (res > 0) {
			// 		this.getIntegral()
			// 	}
			// },fail=>{
			// })
			// option.item = option.item.replace(/%/g, '%25');

		},
		onShareAppMessage: function( options ){
			if(uni.getStorageSync("token")){
				this.getIntegral(1)
			}
			// 设置菜单中的转发按钮触发转发事件时的转发内容
			var shareObj = {
		    title: this.model.articleName,    // 默认是小程序的名称(可以写slogan等)
		    path: '/pages/science/detail?articleId='+this.model.articleId,    // 默认是当前页面，必须是以‘/'开头的完整路径
		    imageUrl: this.model.filePath   //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
			}
			// 返回shareObj
			return shareObj;
		},
		//分享到朋友圈
		onShareTimeline(res) {
			if(uni.getStorageSync("token")){
				this.getIntegral(1)
			}
			var shareObj1 = {
				title: this.model.articleName,
				query: 'articleId='+this.model.articleId,
				// path: '/pages/science/detail?articleId='+this.model.articleId,
				imageUrl: this.model.filePath
			}
		    return shareObj1
		},
		methods:{
			// 文章详情
			taskgetArticletoken(){
				taskgetArticletoken({
					id:this.model.articleId,
				}).then(res=>{
					this.model = res.data
					this.model.articleId = res.data.id
				})
			},
			// 文章详情 无token
			taskgetArticle1(){
				taskgetArticle1({
					id:this.model.articleId,
				}).then(res=>{
					this.model = res.data
					this.model.articleId = res.data.id
				})
			},
			// 积分
			getIntegral(i){
				getArticle({
					id:this.model.articleId,
					patientId:uni.getStorageSync('cardObj').patientId,
					status:i == 1 ? i : ''
				}).then(res=>{
					this.model.readNum = res.data.readNum
				})
			},
			// waibulian(){
			// 	var aboutus = this.model.content.replace(/<[^>]+>/g, '')
			// 	uni.setStorageSync('aboutus',aboutus);
			// 	setTimeout(()=>{
			// 		this.$common.navTo('/pages/task/other/aboutus')
			// 	},300)
			// },
			//调整富文本图片大小
			adjustRichTextImageSize(text){
				return text;
			}
		}
	}
</script>

<style>
	@import url("@/components/u-parse/u-parse.css");
	page{
		background: #fff;
	}
	.gui-course-video{width:750rpx;}
	.row-model{
		display: flex;
		flex-direction: row;
		align-items: center;
	}
</style>
