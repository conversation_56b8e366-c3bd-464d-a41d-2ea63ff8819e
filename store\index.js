import Vue from 'vue';
import Vuex from 'vuex';
import url from "../common.js"
import {msgCount} from '@/api/im.js'
Vue.use(Vuex);

// socket 服务配置
var socketConfig = {
	'serverUrl': url.domain + "/wx/im/", // http 服务器地址
	'socketUrl': url.socketUrl // socket 服务地址
};
const store = new Vuex.Store({
	state: {
		hasLogin: false,
		userInfo:'',
		graceIMMsgs: [],
		graceIMStatus: 'wait', // wait - 等待 ing - 连接中 success - 已经连接
		graceIMToken: '',
		graceIMScTop: 0,
		graceIMHeight: 0,
		graceIMUIndex: '',
		graceIMConfig: socketConfig,
		graceIMFD: 0,
		graceIMUID: "",
		graceIMFriends: [],
		graceIMPage: {},
		interFunction: null,
		openedCaseTaskList: [], //案件详情任务展开的缓存
		openedCaseTaskAnchorId: null,
		loginOutNotice: false,
		getMsgReadedNum:'',
		isReadMsg:{},
		graceIMMsgsCH:1
	},
	mutations: {
		// 更改已读状态
		readMsgisRead(state,n){
			state.isReadMsg = n
		},
		// 清空聊天记录
		clearGraceIMMsgs(state) {
			state.graceIMMsgs = [];
		},
		// 关闭socket链接
		closeSocket(state){
			state.graceIMStatus = '断开链接中...';
			if (state.interFunction) {
				clearInterval(state.interFunction);
				console.log("清除心跳包")
			}
			uni.closeSocket({
				code:1000,
				success (res) {
				  state.graceIMStatus = 'WebSocket 已断开';
				  console.log('WebSocket 已关闭！');
				}
			});
		},
		setOpenedCaseTask(state, payload) {
			const {
				list,
				id
			} = payload;
			if (list) {
				state.openedCaseTaskList = payload.list.map(v => v.show);
			}
			if (id) {
				state.openedCaseTaskAnchorId = payload.id;
			}
		},
		clearOpenedCaseTaskList(state) {
			state.openedCaseTaskList.length = 0;
			state.openedCaseTaskAnchorId = null;
		},
		setLoginOutNotice(state, status) {
			state.loginOutNotice = status;
		},


		// 商城
		login(state, provider) {
			if (provider && provider!=undefined){
		        state.hasLogin = true;
				uni.setStorage({//缓存用户登陆状态
					key: 'userInfo',
					data: provider.userInfo
				})
				state.userInfo = provider;
			}
		},
		logout(state) {
			state.hasLogin = false;
			state.userInfo = '';
			uni.removeStorage({
		        key: 'userInfo'
		    })
		},
		graceIMUIndex: (state, uid) => {
			state.graceIMUIndex = uid
		},
		graceIMHeight:(state, windowHeight)=>{
			state.graceIMHeight = windowHeight
		},
		graceIMStatus:(state, n)=>{
			state.graceIMStatus = n
		},
		graceIMToken:(state, n)=>{
			state.graceIMToken = n
		},
		graceIMUID:(state, n)=>{
			state.graceIMUID = n
		},
		// 接收到撤回消息时候触发
		graceIMMsgsCH:(state, n)=>{
			console.log('我在进来这里')
			state.graceIMMsgsCH = n
		},
		graceIMMsgs:(state, n)=>{
			if(!n.isRevoke){
				state.graceIMMsgs.push(n)
			}
		},
		graceIMScTop:(state, n)=>{
			state.graceIMScTop = n
		},
		getMsgReadedNum:(state, n)=>{
			state.getMsgReadedNum = n
		},
		interFunction:(state,n)=>{
			clearInterval(state.interFunction);
		}
	},
	actions: {
		// 关闭socket链接
		closeSocket({ commit,state}){
			// state.graceIMStatus = '断开链接中...';
			commit('graceIMStatus','断开链接中...')
			if (state.interFunction) {
				commit('interFunction')
				// clearInterval(state.interFunction);
				console.log("清除心跳包")
			}
			uni.closeSocket({
				code:1000,
				success (res) {
					commit('graceIMStatus','WebSocket 已断开')
				  // state.graceIMStatus = 'WebSocket 已断开';
				  console.log('WebSocket 已关闭！');
				}
			});
		},
		// 连接并注册用户分组
		async graceIMConnect({ commit,state,dispatch },user,successClick) {
			var that = this;
			console.log(state.graceIMStatus,'链接状态')
			commit('graceIMUIndex', user.uid)
			// state.graceIMUIndex = user.uid;
			// uni.closeSocket();
			// dispatch('closeSocket')
			// 初始化窗口
			const res = uni.getSystemInfoSync();
			commit('graceIMHeight', res.windowHeight - 50)
			// state.graceIMHeight = res.windowHeight - 50;
			// 判断是否连接
			if (uni.getStorageSync('user') && state.graceIMStatus!=='success') {
				// 连接服务器获取连接 token
				console.log('IM 开始连接 ...',state.graceIMStatus);
				// state.graceIMStatus = 'ing';
				// commit('graceIMStatus', 'ing')
				// state.graceIMToken = 'testtoken123456';
				commit('graceIMToken', 'testtoken123456')
				// 连接 socket 服务器
				// uni.connectSocket({url : socketConfig.socketUrl + '?gchatToken=' + state.graceIMToken});

					await uni.connectSocket({
						url: socketConfig.socketUrl,
						success(result) {
							console.log(result);
						},
						fail(error) {
							console.log(error);
						}
					});

				// 连接成功
				uni.onSocketOpen(function(res) {
					console.log('IM 服务连接成功 ...');
					commit('graceIMStatus', 'success')
					// state.graceIMStatus = 'success';
					// 获取系统信息
					try {
						const res = uni.getSystemInfoSync();
						user.system = res.model;
						user.token = state.graceIMToken; //传token
						user.type = 'reg'; //消息类型(reg：注册认证  keepConnect：心跳  msg：消息  system：系统消息)
						user.userId = uni.getStorageSync('user').id;
						console.log('发送认证信息：' + JSON.stringify(user))
						// state.graceIMUID = user.userUid;
						commit('graceIMUID', user.userUid)
						uni.sendSocketMessage({
							data: JSON.stringify(user),
							fail: function(e) {
								console.log('用户连接认证失败');
							}
						});
					} catch (e) {
						console.log(e)
					}
					if (state.interFunction) {
						console.log('清空心跳')
						clearInterval(state.interFunction);
					}
					// 刷新 tiken 30 秒一次保持心跳
					state.interFunction = setInterval(function() {
						user.type = 'keepConnect'; //消息类型(reg：注册认证  keepConnect：心跳  msg：消息  system：系统消息)
						user.token = state.graceIMToken;
						user.fd = state.graceIMFD;
						user.userUid = state.graceIMUID;
						console.log('心跳用户为：' + JSON.stringify(user))
						uni.sendSocketMessage({
							data: JSON.stringify(user),
							fail: function(e) {
								console.log('心跳发送失败，重新发起ws连接');
								uni.connectSocket({
									url: socketConfig.socketUrl,
									success(result) {
										console.log(result);
									},
									fail(error) {
										console.log(error);
									}
								});
							}
						});
					}, 30000);
				});

				// 接收消息
				uni.onSocketMessage(function(res) {
					console.log('接收消息判断==>' + res.data);
					var msg = JSON.parse(res.data);
					//消息类型(reg：注册认证  keepConnect：心跳  msg：消息  system：系统消息)
					// 注册消息
					if(msg.isRead == 1){
						commit('readMsgisRead',msg)
					}
					if (msg.type == 'reg') {
						state.graceIMToken = msg.token;
						state.graceIMFD = msg.fd;
						state.graceIMUID = msg.userUid;
						uni.setStorageSync("userUid",msg.userUid)
						dispatch('getMsgReaded',successClick)
						// that.getMsgReaded(successClick);
					}
					// 对话消息
					else if (msg.type == 'msg') {
						msg.date = url.parseTime(new Date(msg.date), "{m}-{d} {h}:{i}");
						// state.graceIMMsgs.push(msg);
						console.log('msgmsgmsg',msg)
						if(msg.isRevoke){
							console.log('我只能进来这里')
							commit('graceIMMsgsCH',msg)
						}else{
							console.log('我不可以能进来这里')
							commit('graceIMMsgs',msg)
						}
						commit('graceIMScTop',9999999 + Math.random())
						// state.graceIMScTop = 9999999 + Math.random();
						// 记录最后一条信息到本地
						try {
							var recContent = '';
							//contentType 消息内容类型（txt：普通文字消息，img：图片消息，2：文件消息，voice：语音消息，4：视频消息）
							if (msg.contentType == 'txt') {
								// recContent = msg.content.substring(0, 10);
								recContent = msg.content;
							} else if (msg.contentType == 'img') {
								recContent = '[ 图片消息 ]';
							} else if (msg.contentType == 'product') {
								recContent = '[ 订单信息 ]';
							} else if (msg.contentType == 'order') {
								recContent = '[ 商品链接 ]';
							} else {
								recContent = '[ 语音消息 ]';
							}
							var history = {
								lastcontent: recContent,
								lasttime: msg.date
							}
							// uni.setStorageSync("graceIM_"+msg.group, JSON.stringify(history));
							uni.setStorageSync("graceIM_" + msg.groupIndex, JSON.stringify(history));
							dispatch('getMsgReaded',successClick)
						} catch (e) {}
					}
				});

				// 连接断开
				uni.onSocketError(function(res) {
					console.log('IM 服务断开 ...');
					// state.graceIMStatus = 'wait';
					commit('graceIMStatus','wait')
				})
				// 监听连接关闭close
				uni.onSocketClose((e) => {
					console.log('连接关闭！');

				})
				// 	},
				// 	fail:function(e){
				// 		console.log(e);
				// 	}
				// });
			} else {
				// try {
				// 	const res = uni.getSystemInfoSync();
				// 	user.userUid = state.graceIMUID;
				// 	// user.uid    =  11; //到时传用户id过去
				// 	user.system = res.model;
				// 	user.token = state.graceIMToken;
				// 	user.type = 'reg';
				// 	uni.sendSocketMessage({
				// 		data: JSON.stringify(user),
				// 		fail: function(e) {
				// 			console.log('用户连接认证失败 ...');
				// 		},
				// 	});
				// } catch (e) {}
			}
		},
		// 获取消息总数
		getMsgReaded({commit},successClick) {
			if (uni.getStorageSync("userUid")) {
				msgCount({
					userUid: uni.getStorageSync("userUid")
				}).then(res=>{
					let num = res.data;
					commit('getMsgReadedNum',num)
					// successClick(num)
				})
			}
		},
	},
	getters: {
		openedCaseTaskList(state) {
			return state.openedCaseTaskList
		},
		openedCaseTaskAnchorId(state) {
			return state.openedCaseTaskAnchorId
		},
		loginOutNotice(state) {
			return state.loginOutNotice
		}
	}
})
export default store;
