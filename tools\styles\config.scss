$colors:(
"orange":#F68512,
"white":#fff,
"black":#111,
"black-6":#666,
"red":#F14545,
"grey-b2":#b2b2b2,
"grey-ee":#eee,
"grey-f9":#f9f9f9,
"grey-dc":#dcdcdc,
"grey-74":#747474,
"blue":#3291d9,
"yellow":#FFFF00,
"egg": #f3cfa9,
"egg-white": #fff8ed,
"green": #00baad,
"green-light": #cdead7,
"green-white": #f4fdf8,
"purple": #d46fe5);


$sizes:(
	"10":10,
	"12":12,
	"14":14,
	"16":16,
	"20":20,
	"24":24,
	"28":28,
	"30":30,
	"32":32,
	"36":36,
	"40":40,
	"50":50,
	"60":60,
	"70":70,
	"100":100);

$space-letters:(2,4,6,8,10);

$percent-sizes:( 
	10:10%,
    20:20%,
    25:25%,
    30:30%,
    40:40%,
    50:50%,
    60:60%,
    70:70%,
    80:80%,
    90:90%,
    100:100%);
$space-sizes:(5,10,15,20,30,40,50,60,70,80,90,100,120,150,200);
$space-room:(
"w":width,
"h":height
);
$space-type:(
"m":margin,
"p":padding
);
$space-direction:(
"t":top,
"b":bottom,
"r":right,
"l":left
);
$text-align:('left','right','center','justify');
$position:(
"absolute",
"relative",
"fixed",
"sticky"
);

$flex-jc:(
    'start':flex-start,
    'end':flex-end,
    'center':center,
    'between':space-between,
    'around':space-around
);
$flex-ai:(
    'start':flex-start,
    'end':flex-end,
    'center':center,
    'stretch':stretch
);

$box-sizing:('border':border-box,'content':content-box);

$over-flow:('hidden','scroll');

$ellipsis:(1,2,3,4);