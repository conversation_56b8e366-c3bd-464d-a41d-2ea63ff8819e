<template>
	<view class="gui-padding mt-10">
		<view class="gui-form-item gui-border-b" style="margin-top: 10rpx;">
			<text class="gui-form-label text-center" style="width:100px;">记录时间 &ensp; :</text>
			<view class="gui-form-body">
				<input type="text" class="gui-form-input" v-model="infoData.measureDate" name="height" placeholder="" />
			</view>
		</view>
		<view class="gui-form-item gui-border-b" style="margin-top: 10rpx;">
			<text class="gui-form-label text-center " style="width:100px;">年龄（岁）:</text>
			<view class="gui-form-body">
				<input type="text" class="gui-form-input" v-model="infoData.age" name="height" placeholder="" />
			</view>
		</view>
		<view class="gui-form-item gui-border-b" style="margin-top: 10rpx;">
			<text class="gui-form-label  text-center" style="width:100px;">身高（厘米）:</text>
			<view class="gui-form-body">
				<input type="text" class="gui-form-input" v-model="infoData.height" name="height" placeholder="" />
			</view>
		</view>
		<view class="gui-form-item gui-border-b" style="margin-top: 10rpx;">
			<text class="gui-form-label text-center " style="width:100px;">体重（公斤）:</text>
			<view class="gui-form-body">
				<input type="text" class="gui-form-input" v-model="infoData.weight" name="height" placeholder="" />
			</view>
		</view>
		
	
	</view>
</template>

<script>
	export default {
		data() {
			return {
				infoData: {}
			}
		},
		onLoad(option) {
			this.infoData = JSON.parse(decodeURIComponent(option.info))
			this.infoData.measureDate = this.$common.parseTime(this.infoData.measureDate,'{y}-{m}-{d}') 


		},
		methods: {

		}
	}
</script>

<style>

</style>
