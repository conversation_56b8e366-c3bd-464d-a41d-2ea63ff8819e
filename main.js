import Vue from 'vue'
import App from './App'

// import share from "@/tools/utils/share.js"

// 引入 im
import store from './store'
Vue.prototype.$store = store;

// 注册
import common from '@/common.js';
Vue.prototype.$common = common;

// import iconJson from './static/js/icon.js'
// Vue.prototype.$iconJson= iconJson.data;
Vue.prototype.isEmpty=function(obj){
	if(obj=='') return true;
	if(obj==null) return true;
	if(obj==undefined) return true;
	if(obj=={}) return true;
	if(obj==[]) return true;
	return false;
}
Vue.prototype.systemeven = function (name) {
	var i = Vue.prototype.$common.systemlist.includes(name)
	return i
}
Vue.config.productionTip = false
App.mpType = 'app'

const app = new Vue({
    ...App,
	// share,
	beforeCreate() {
	        Vue.prototype.$bus = this//安装全局事件总线
	    }
})
app.$mount()