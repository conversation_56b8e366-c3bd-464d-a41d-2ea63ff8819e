<template>
	<gui-page :customHeader="true" statusBarStyle="background-color:#C59F79;" headerStyle="background-color:#C59F79;">
		<view slot="gHeader">
			<view class="bg-zhuti w-100 bgs gui-flex" style="color: #E8E8E8;padding-bottom:15rpx; ">
				<view class="logosize" style="margin-top: 21rpx;">
					<image src="https://img.starup.net.cn/xmkj/zwb/img/zyLogo.jpg" style="width: 100%;height: 100%; border-radius: 30rpx;"
						mode=""></image>
				</view>
				<view class="ml-20 fs-30 mt-20 text-bold" @click="edit()">
					经期设置
				</view>
			</view>
		</view>
	
		<!-- 页面主体 -->
		<view slot="gBody">
			<view class="gui-margin-top ml-20">
				 <gui-schedule ref="child" :currentDate="todays" :switype='swtype'
				 @scheduleTap="scheduleTap" @chooseDate="chooseDate" 
				 @selectDate="selectDate" ></gui-schedule>
			</view>
			<view class="ucenter-line"></view>
			<view v-if="listshow" class="gui-padding">
				<view class="gui-list-items">
					<text class="gui-list-icon gui-icons gui-color-red fs-30">&#xe649;</text>
					<view class="gui-list-body">
						<view class="gui-list-title">
							<text v-if="swtype == -1" class="gui-list-title-text">大姨妈来了</text>
							<text v-else class="gui-list-title-text">大姨妈走喽~</text>
						</view>
					</view>
					<view class="">
						<switch :checked="swtype == 1" v-model="swtype" name="swtype" @change="switchChange" color="#ff3078" />
					</view>
				</view>
				<view class="gui-list-items" @click="gotodetail">
					<text class="gui-list-icon gui-icons gui-color-red fs-30">&#xe605;</text>
					<view class="gui-list-body">
						<view class="gui-list-title">
							<text class="gui-list-title-text">爱爱</text>
						</view>
					</view>
					<text class="gui-list-arrow-right gui-icons">&#xe601;</text>
				</view>
				<view class="gui-list-items" @click="gotodetail">
					<text class="gui-list-icon gui-icons gui-color-blue fs-30">&#xe613;</text>
					<view class="gui-list-body">
						<view class="gui-list-title">
							<text class="gui-list-title-text">症状</text>
						</view>
					</view>
					<text class="gui-list-arrow-right gui-icons">&#xe601;</text>
				</view>
				<view class="gui-list-items" @click="gotodetail">
					<text class="gui-list-icon gui-icons gui-color-orange fs-30">&#xe686;</text>
					<view class="gui-list-body">
						<view class="gui-list-title">
							<text class="gui-list-title-text">基础体温</text>
						</view>
					</view>
					<text class="gui-list-arrow-right gui-icons">&#xe601;</text>
				</view>
				<view class="gui-list-items" @click="gotodetail">
					<text class="gui-list-icon gui-icons gui-color-purple fs-30">&#xe685;</text>
					<view class="gui-list-body">
						<view class="gui-list-title">
							<text class="gui-list-title-text">排卵试纸</text>
						</view>
					</view>
					<text class="gui-list-arrow-right gui-icons">&#xe601;</text>
				</view>
				<view class="gui-list-items" @click="gotodetail">
					<text class="gui-list-icon gui-icons gui-color-pink fs-30">&#xe629;</text>
					<view class="gui-list-body">
						<view class="gui-list-title">
							<text class="gui-list-title-text">早孕试纸</text>
						</view>
					</view>
					<text class="gui-list-arrow-right gui-icons">&#xe601;</text>
				</view>
				<view class="gui-list-items" @click="gotodetail">
					<text class="gui-list-icon gui-icons gui-color-green fs-30">&#xe6cc;</text>
					<view class="gui-list-body">
						<view class="gui-list-title">
							<text class="gui-list-title-text">习惯</text>
						</view>
					</view>
					<text class="gui-list-arrow-right gui-icons">&#xe601;</text>
				</view>
				<view class="gui-list-items" @click="gotodetail">
					<text class="gui-list-icon gui-icons gui-color-gray fs-30">&#xe624;</text>
					<view class="gui-list-body">
						<view class="gui-list-title">
							<text class="gui-list-title-text">备注</text>
						</view>
					</view>
					<text class="gui-list-arrow-right gui-icons">&#xe601;</text>
				</view>
			</view>
			<view v-else class="mt-80 gui-padding gui-flex gui-justify-content-center gui-align-items-center gui-columns">
				<image src="https://img.starup.net.cn/xmkj/zwb/img/jq.png" mode="" style="height: 180rpx; width: 200rpx;"></image>
				<view class="mt-10">你不能预测未来哦~</view>
				<view class="mt-30 gotoToday" @click="toToday()">回到今天</view>
			</view>
			<view class="mt-120"></view>
		</view>
	</gui-page>
	
</template>
<script>
export default {
	data() {
		return {
			todays:'',
			listshow:true,
			swtype:-1
		}
	},
	onReady(){
		this.todays = this.$common.parseTime(new Date(), '{y}-{m}-{d}')
		this.$refs.child.getList(this.todays)
	},
	  props:{
		isRefresh:{
		  type : Number,
		  default: 0
		}
	  },
	  watch:{
		isRefresh:{
		  deep:true,
		  handler(newValue,oldValue){
			if(newValue != oldValue){
			  //子组件中监控值变化做 相关业务功能
			  // console.info("子组件更新")
			  this.$refs.child.getList(this.todays)
			}
		  }
		}
	  },
	methods:{
		//经期设置
		edit(){
			this.$common.navTo('/pages/index/period')
			uni.setStorageSync('timedata',this.todays)
		},
		scheduleTap : function(id){
			uni.showToast({title:"id "+ id, icon:"none"});
		},
		gotodetail(){
			uni.showToast({title:"功能暂未开放~", icon:"none"});
		},
		// switch 开关
		switchChange : function (e) {
			if (e.detail.value == true) {
				this.swtype = 1
			} else{
				this.swtype = -1
			}
			this.$refs.child.getdata(e.detail.value)
		},
		chooseDate : function(e){
			// console.log("客户选择了日期 : " +e );
			this.listshow = this.isFuture(e)
			this.$refs.child.getList(e)
		},
		selectDate : function (e) {
			 // console.log("客户从日期picker选择日期 : " + e);
			this.listshow = this.isFuture(e)
			this.$refs.child.getList(e)
		},
		toToday(){
			//调用子页面“回到今天”方法
		    this.$refs.child.gotoToday()
			var today = uni.getStorageSync('today')
			this.listshow = this.isFuture(today)
			uni.removeStorageSync('today')
		},
	    isFuture(str) {
		  let d = new Date(str.replace(/-/g, '/'))
		  let todaysDate = new Date()
		  if (d.setHours(0, 0, 0, 0) > todaysDate.setHours(0, 0, 0, 0)) {
			return false
		  } else {
			return true
		  }
		}
	}
}
</script>
<style scoped>
	.ucenter-face{width:100rpx; height:100rpx;}
	.ucenter-face-image{width:100rpx; height:100rpx;}
	.ucenter-line{height:20rpx; background-color:#F6F7F8; margin:16rpx 0;}
	.logoff{line-height:88rpx; font-size:28rpx;}
	.gui-list-title-text{line-height:60rpx;}
	.gotoToday{
		border: 1px solid #ffaaff;
		padding: 6rpx 40rpx;
		border-radius: 10rpx;
		color: #ff00ff;
	}
</style>