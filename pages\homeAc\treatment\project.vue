<template>
	<view>
		<view class="gui-grids gui-flex gui-rows gui-wrap demo-nav2">
			<view class="px-40"> 正在进行：{{productName}}的时间预约 </view>
		</view>
		<view>
			<view v-if="currentIndex == 0" class="pb-30">
				<lx-calendar v-if="currentIndex == 0" v-model="date" @change="changeDate"></lx-calendar>
			</view>
			<view v-if="list.length > 0">
				<view v-for="(item,index) in list" :key="index" class="mb-40" >
					<view class="gui-flex gui-space-between mx-40 gui-border-b">
						<view class="gui-flex">
							<view class="">
								<img class="gui-grids-icon-img" style="height: 120rpx; width: 120rpx;"
									:src="item.showAvatar || 'https://img.starup.net.cn/xmkj/zwb/img/yisheng.png'">
							</view>
							<view class="gui-flex gui-columns ml-10 mt-10">
								<view class="gui-flex gui-align-items-end">
									<view class="fs-36 mr-10">{{item.name}} <text
											class="gui-color-gray pl-10">|</text></view>
									<view class="mr-20 gui-color-gray">治疗师</view>
								</view>
								<view class="mt-10 gui-color-gray ellipsis-1">
									擅长：{{item.explain}}
								</view>
							</view>
						</view>
						<view class="fs-28 gui-color-white gui-flex gui-justify-content-center gui-align-items-center" @click="popupOpen(item)">
							<view class="bg-zhuti gui-flex gui-justify-content-center" style="width: 150rpx; height: 50rpx;border-radius: 40rpx; line-height: 50rpx;">
								余 {{Number(item.count)}} <text class=" gui-icons fs-24 gui-color-white ml-20">&#xe601;</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view v-if="list.length<= 0">
			<gui-empty>
				<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
					<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
				</view>
				<text slot="text"
				class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
			</gui-empty>
		</view>
		<view class="h50"></view>
		<!-- 底部弹出 -->
		<gui-popup ref="guipopup" position="bottom" style="z-index: 88;">
			<view class="gui-relative gui-box-shadow gui-bg-gray pt-40 px-20" style="min-height: 100rpx; border-top-left-radius: 40rpx !important;border-top-right-radius: 40rpx !important;">
				<view class="mb-10 gui-flex gui-justify-content-center gui-bold"> 选择 {{date}} 预约时间点 </view>
				<view class="gui-flex gui-rows gui-wrap fs-30" >
					<view class="m-20 py-20 gui-flex gui-justify-content-center gui-color-white " v-for="(item , index) in dropTimes" :key="index"
						style="width: 135rpx; position: relative; " :style="timekey == index?'background-color: #C59F79 !important;' : 'color: #C59F79 !important; border: 1px solid #C59F79 !important;'" @click="changeTime(index,item)"> 
						{{item.layoutTime}} 
						<text v-if="timekey == index" class="gui-icons" style="position: absolute; right: 10rpx; bottom: 8rpx; z-index: 1;">&#xe60f;</text>
					</view>
				</view>
				<view class="gui-flex gui-justify-content-center mt-40" @click="submit">
					<text class="bg-zhuti gui-color-white py-20 px-80 fs-30 gui-border-radius">确定预约</text>
				</view>
				<view style="height: 80rpx;"></view>
				<!-- iphone 底部操作按钮躲避 -->
				<gui-iphone-bottom></gui-iphone-bottom>
				<!-- 关闭按钮 -->
				<text class="gui-block-text demo-close gui-icons gui-color-orange gui-absolute-rt" style="top: 8rpx; right: 20rpx;"
				@click="popupClose">&#xe78a;</text>
			</view>
		</gui-popup>
	</view>
</template>

<script>
	import { getCureTherapist,getTherapistTime,addSubmitOrder } from '@/api/treatment.js'
	import lxCalendar from '@/components/lx-calendar/lx-calendar.vue'
	export default {
		components: {
			lxCalendar,
		},
		data() {
			return {
				// listType:'',//监测项目类型
				timekey:0,
				date:"",
				// 选中选项的 索引
				currentIndex: 0,
				loadingStatus:false, //  ， true： 请求中   false :请求结束
				loadState: 0 ,  // 0 : 默认0，有下一页   1 ：请求中  2： 加载完毕
				page:1,
				size:20,
				zongSize:0,
				list:[],//治疗师数据
				productName:'',
				productId:[],//项目id
				dropTimes:[],//时间点数据
				doctordata:[],//选择的医生数据
				cureId:'',//治疗师id
			}
		},
		created() {
			this.date = this.$common.parseTime(new Date(),'{y}-{m}-{d}');
		},
		onLoad(option) {
			var productList = option.productArr?JSON.parse(decodeURIComponent(option.productArr)):this.productList
			var arrId = []
			var arrName = []
			productList.forEach(item =>{
				arrId.push(item.id)
				arrName.push(item.name)
			})
			this.productName = arrName?arrName.join('、'):'';
			this.productId = arrId;
			this.getList();
		},
		onShow() {
			// this.$common.role();
			// // if (!uni.getStorageSync("user").wxOpenId ) {
			// // 	this.getGzh();
			// // }
			// // 如果是从首页消息那边跳过来的就切换成待执行
			// if (uni.getStorageSync('index')) {
			// 	this.currentIndex = 1
			// 	uni.removeStorageSync('index')
			// }
			// this.inits();
		},
		//下拉刷新
		onPullDownRefresh() {
			this.init();
			setTimeout(function() {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		//上拉加载
		onReachBottom: function() {
			//避免多次触发
			if(this.loadingStatus || this.loadState == 2){return}
			// this.getList();
		},
		methods: {
			changeTime(e,val){
				this.timekey = e
				this.doctordata = val
			},
			//03 底部弹出模式
			popupOpen(val){
				this.cureId = val.id
				getTherapistTime({
					startTime: this.currentIndex == 0 ? this.date : "",
					id:val.id,
					projectIds:this.productId.join(',')
				}).then(res => {
					if (res.data.length <= 0) {return this.$common.msg('今日暂无余号')}
					this.dropTimes = res.data
					this.$refs.guipopup.open();
				})
			},
			popupClose(){this.$refs.guipopup.close();},
			changeDate(e){
				this.date = e.fulldate;
				this.getList();
				// this.init();
			},
			getList() {
				this.loadingStatus  = true;
				getCureTherapist({
					startTime: this.currentIndex == 0 ? this.date : "",
					projectId:JSON.stringify(this.productId),
				}).then(res=>{
					this.list = res.data
					// this.list = this.page == 1 ? res.data : this.list.concat(res.data);
					// this.zongSize = res.total;
					// if(this.list.length >= this.zongSize){
					// 	this.loadState = 2;
					// 	this.loadingStatus  = false;
					// 	return;
					// }
					// this.page++;
					// this.loadState = 0;
					// this.loadingStatus  = false;
				})
				// this.$common.getNoReads();
			},
			submit(){
				addSubmitOrder({
					patientId:uni.getStorageSync("cardObj").patientId || '', //患者id
					patientName: uni.getStorageSync("cardObj").userName || '', //患者姓名
					projectId:this.productId.join(','),  //项目id
					cureId: this.doctordata.cureId?this.doctordata.cureId:this.cureId, //治疗师id
					appointmentTime:this.doctordata.layoutTime?this.doctordata.layoutTime:this.dropTimes[0].layoutTime, //时间点
					time:this.date ,//选择的日期
				}).then(res => {
					if (res.code == 200) {
						this.$common.msg('预约成功！')
						setTimeout(() =>{
							this.dropTimes = [];
							this.$common.navCloseTo('/pages/homeAc/treatment/record')
						},600)
					}
					
				})
			},
			// 跳转详情页
			detail(item) {
				this.$common.navTo('/pages/task/other/missionary?id='+item.id+'&taskType='+item.taskType)
			}
		}
	}
</script>

<style scoped>
	.shadow {
		box-shadow: 0px 3rpx 10rpx 0rpx #F1F1F1;
	}
	.shadow2 {
		box-shadow: 0px 20rpx 20rpx 0rpx #f7f7f7;
	}
	.sub-title {
		border: 1px solid #41bcb3;
		padding: 4rpx 6rpx;
		color: #41bcb3;
		border-radius: 10rpx;
	}

	.tab-card-demo-text {
		line-height: 388rpx;
		font-size: 26rpx;
	}
	.insetbnt{
		/* display: flex; */
		align-items: center;
		width: 110rpx;
		height: 110rpx;
		/* line-height: 100rpx; */
		border-radius: 110rpx;
		background-color: #C59F79;
		box-shadow:5rpx 10rpx 10rpx gainsboro;
		position: fixed;
		bottom: 180rpx;
		right: 40rpx;
	}
	.demo-close{width:70rpx; height:70rpx; line-height:70rpx; opacity:0.88; text-align:center; font-size:50rpx;}
</style>
