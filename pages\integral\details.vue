<template>
	<gui-page>
		<template v-slot:gBody>
			<!-- 轮播图  -->
			<view>
				<gui-swiper 
				:swiperItems="swiperItems" 
				:spacing="0" :padding="0" 
				borderRadius="0rpx" 
				:width="750" 
				:height="500"></gui-swiper>
			</view>
			<!-- 商品标题 分享按钮 -->
			<!-- 价格 -->
			<view  class="gui-padding gui-bg-white gui-dark-bg-level-3 mt-10" >
				<view class="detail-list">
					<viwe class="gui-flex mb-10 gui-space-between" >
						<view class="fs-36 ellipsis-1" style="width: 560rpx;"> {{product.name}} </view>
						<view style="width: 180rpx;">
							<view class="gui-flex gui-row gui-nowrap gui-justify-content-end">
								<text v-if="product.giftStatus == 1 && product.integral" class="product-price gui-color-red">{{product.integral}} 积分</text>
								<text v-if="product.giftStatus == 0" class="gui-color-red fs-32" >不支持兑换</text>
							</view>
						</view>
					</viwe>
					<view class="gui-flex gui-space-between">
						<viwe class="gui-text-small small-text">库存： {{product.stocks || '-'}} 件</viwe>
						<viwe class="gui-text-small small-text">销量： {{product.exchangeSize ||'0'}} 件</viwe>
					</view>
				</view>
				
			</view>
			<view class="gui-bg-gray" style="width: 100%; height: 20rpx;"></view>
			<!-- 切换导航 -->
			<view >
				<gui-switch-navigation 
				:items="navItems" 
				:isCenter="true" 
				:size="200"
				activeFontSize = "32rpx"
				lineHeight="80rpx" 
				textAlign="center"
				activeLineWidth="200rpx" 
				activeLineHeight="2rpx" 
				activeLineBg = "linear-gradient(to right, #d6ad84,#C59F79)"
				:margin="10" ></gui-switch-navigation>
			</view>
			<!-- 详情 请根据项目情况自行改进 可以使用 富文本-->
			<view class="mt-20 px-40 " style="word-wrap:break-word" v-html="$common.adjustRichTextImageSize(product.contants)"></view>
			
			<!-- 底部 -->
			<view class="product-footer gui-dark-bg-level-3">
				<!-- 底部按钮栏 -->
				<view class=" gui-flex gui-columns gui-align-items-center">
					<!-- 2个底部按钮 -->
					<!-- <view class="gui-footer-large-buttons gui-flex1 gui-flex gui-row gui-nowrap"> -->
						<view v-if="product.giftStatus == 1" class="bg-zhuti" style="text-align:center;width: 60%;border-radius: 80rpx;" hover-class="gui-tap" @tap="buynow(product)">
							<text class="gui-text gui-text-center gui-block gui-color-white gui-footer-large-button-text" style="font-size: 32rpx;">立即兑换</text>
						</view>
						<view style="height:40rpx;"></view>
					<!-- </view> -->
				</view>
				<gui-iphone-bottom></gui-iphone-bottom>
			</view>
			<!-- 底部占位 -->
			<view style="height:120rpx;"></view>
		</template>
	</gui-page>
</template>
<script>
	import { giftId,giftOrderAdd } from '@/api/integral.js'
	import guiSwiper from '@/GraceUI5/components/gui-swiper.vue'
	import guiSwitchNavigation  from '@/GraceUI5/components/gui-switch-navigation.vue'
export default {
	components:{guiSwiper,guiSwitchNavigation},
	data() {
		return {
			// 轮播图 
			swiperItems : [],
			// 商品信息
			product : {},
			// 切换导航
			navItems : [{id:0, dictLabel:'商品详情'}],
			// 切换索引
			active   : 0,
		}
	},
	onLoad(e) {
		if (e.id) {
			this.getDetail(Number(e.id))
		}
	},
	methods: {
		getDetail(e){
			giftId(e).then(res =>{
				this.product = res.data
				
				//头部轮播图
				var dataArr = res.data.imgUrl?.split(',')
				for( let i of dataArr){
					var obj ={
						img : i
					}
					this.swiperItems.push(obj)
				}
			})
		},
		buynow(e){
			if (uni.getStorageSync('cardObj') && uni.getStorageSync('cardObj').patientId) {
				let self = this
				uni.showModal({
					title: '温馨提醒', //提示标题
					content: '商品将抵扣 ' + e.integral + ' 积分，请确认是否兑换！', //提示内容
					cancelText: "取消", // 取消按钮的文字  
					confirmText: "确认", // 确认按钮的文字  
					confirmColor: '#C59F79',
					cancelColor: '#000',
					success: function(res) {
						if (res.confirm) { //confirm为ture，代表点击了确定
							// console.log('用户点击确定');
							giftOrderAdd({
								 giftName:e.name,  //礼品名称
								 giftId:e.id, //礼品id
								 userName:uni.getStorageSync('cardObj').userName, //用户名称
								 userId:uni.getStorageSync('cardObj').patientId, //用户id
								 exchangeNumber:"1", //兑换数量
								 integral:e.integral //总积分
							}).then(res =>{
								if (res.code === 200) {
									self.$common.navTo("/pages/integral/exchange?item="+encodeURIComponent(JSON.stringify(res.data)))
								}
							})
						} else if (res.cancel) { //cancel为ture，代表点击了取消
							// console.log('用户点击取消');
						}
					}
				});
				
			} else {
				this.$common.msg("请先绑定就诊卡")
			}
		}
	}
}
</script>
<style scoped>
	.detail-list{
		display: flex;
		flex-direction: column;
	}
	.small-text{
		color: #999;
		line-height: 50rpx;
		font-size: 30rpx;
	}
.product-name{width:560rpx; line-height:50rpx;}
.product-share{width:80rpx; height:80rpx; text-align:center; font-size:50rpx; color:#FF7900; line-height:80rpx;}
.product-price{color:#FF7900; font-size:36rpx; }
.gui-common-line{height:18rpx;}
.product-footer{position:fixed; z-index:99; left:0; bottom:0; width:100%;}
.product-attr{width:700rpx; margin:25rpx; height:580rpx;}
.gui-padding{padding:20rpx 25rpx;}
.gui-footer-icon-buttons{width:100rpx;}
</style>
