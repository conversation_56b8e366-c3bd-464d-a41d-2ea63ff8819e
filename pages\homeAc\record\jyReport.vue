<template>
	<view>
		<view class="mx-40 my-20" style="height:  100vh;">
			<view class="gui-text-center gui-bold fs-30"> <text>{{jylist.docTitle}}</text></view>
			<view style="margin-top:15rpx" class="gui-flex gui-space-between">
				<view class="gui-text-small ">姓名：{{jylist.patientName}}</view>
				<view class="gui-text-small   ">性别：{{jylist.gender== 1 ?'男':'女'}}</view>
				<view class="gui-text-small ">年龄：{{jylist.age}}</view>
			</view>
			<view style="margin-top:15rpx" class="gui-flex gui-space-between">
				<view class="gui-text-small ">科室：{{jylist.applyDept}}</view>
				<view class="gui-text-small   ">医生：{{jylist.applyDoctor}}</view>
				<view class="gui-text-small "></view>
			</view>
			<view class="gui-table  gui-border-t" style="margin-top:50rpx; ">
				<view class=" gui-flex gui-rows gui-nowrap  ">

					<text
						class="gui-td1 fs-30   p-20  td gui-border-r gui-border-b gui-td-text gui-align-items-center gui-border-l gui-flex gui-justify-content-center gui-bold   gui-text-center">项目</text>
					<text class="shsj gui-td2 gui-border-r gui-border-b gui-td-text py-40  px-20 fs-28 "><text
							v-for="item in jylist.executItems">{{item.itemName}}</text></text>
				</view>
				<view class=" gui-flex gui-rows gui-nowrap  ">
					<text
						class="gui-td1 fs-30   p-20  td gui-border-r gui-border-b gui-td-text gui-align-items-center gui-border-l gui-flex gui-justify-content-center gui-bold   gui-text-center">检查结果</text>
					<text class="shsj gui-td2 gui-border-r gui-border-b gui-td-text py-40  px-20 fs-28 "> <text
							v-for="item in jylist.executItems">{{item.itemResult}}</text> </text>
				</view>
				<view class=" gui-flex gui-rows gui-nowrap  ">
					<text
						class="gui-td1 fs-30   p-20  td gui-border-r gui-border-b gui-td-text gui-align-items-center gui-border-l gui-flex gui-justify-content-center gui-bold   gui-text-center">参考值</text>
					<text class="shsj gui-td2 gui-border-r gui-border-b gui-td-text py-40  px-20 fs-28 "> <text
							v-for="item in jylist.executItems">{{item.itemValue|| '-'}}</text> </text>
				</view>
				<view class=" gui-flex gui-rows gui-nowrap  ">
					<text
						class="gui-td1 fs-30   p-20  td gui-border-r gui-border-b gui-td-text gui-align-items-center gui-border-l gui-flex gui-justify-content-center gui-bold  gui-text-center">单位</text>
					<text class="shsj gui-td2 gui-border-r gui-border-b gui-td-text py-40  px-20 fs-28 "> <text
							v-for="item in jylist.executItems">{{item.itemUnit|| '-'}}</text> </text>
				</view>
				<view style="margin-top:15rpx" class="gui-flex gui-space-between">
					<view class="gui-text-small ">检查员：{{jylist.gatherer|| '-'}}</view>
					<view class="gui-text-small   "></view>
					<view class="gui-text-small ">检查日期：{{jylist.executeTime|| '-'}}</view>
				</view>
				<view style="margin-top:15rpx" class="gui-flex gui-space-between">
					<view class="gui-text-small ">报告人：{{jylist.reportor|| '-'}}</view>
					<view class="gui-text-small   "></view>
					<view class="gui-text-small ">报告日期：{{jylist.reportTime,'{y}-{m}-{d}'}}</view>
				</view>


			</view>

		</view>
	</view>
</template>

<script>
	import {
		getExaminationInfo
	} from '@/api/home.js'
	export default {
		data() {
			return {
				info: '',
				jylist: [],
				jyapplyItem: [],
			}
		},
		onLoad: function(option) {
			this.info = JSON.parse(option.info);
			this.report();

		},

		methods: {
			report() {
				getExaminationInfo(this.info.id).then(res => {
					this.jylist = res.data;
					this.jyapplyItem = res.data.executItems;
				})
			},
		}
	}
</script>

<style scoped>
	.demo {
		width: 210rpx;
		height: 88rpx;
		line-height: 88rpx;
		text-align: center;
		margin: 10rpx;
	}

	.demo-auto-width {
		height: 88rpx;
		line-height: 88rpx;
		text-align: center;
		margin: 10rpx;
	}

	.gui-text-small {
		font-size: 20rpx;
	}

	.gui-td1 {
		width: 30%;
		display: flexbox;
	}

	.gui-td2 {
		width: 70%;
		display: flexbox;
	}

	.td {
		color: #7784eb;
	}

	.shsj {
		text-indent: 2em
	}
</style>
