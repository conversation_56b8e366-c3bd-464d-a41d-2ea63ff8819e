<template>
	<view class="gui-padding" style="min-height:100vh; background-color:#FFFFFF;">
		<!-- 添加就诊卡 -->
		<view class="list-card-row">
			<view class="addc" style="margin-right: 10rpx;" @tap="addPatient(1)">
				<view style="margin-right: 10rpx;">
					<image style="width: 80rpx;height: 80rpx;" src="@/static/img/bi.png"></image>
				</view>
				<view class=" py-30">
					<view style="font-size: 32rpx;">手工添加</view>
					<view class="fs-24">还可以邦定{{6-cardList.length}}人</view>
				</view>
				<!-- <view class="addc-ion"><text class="gui-icons gui-color-gray font-bold">&#xe601;</text></view>	 -->
			</view>
			<view class="addc" style="margin-left: 10rpx;" @tap="addPatient(0)">
				<view style="margin-right: 10rpx;">
					<image style="width: 80rpx;height: 80rpx;" src="@/static/img/saoma.png"></image>
				</view>
				<view class="addc-text  py-30">
					<view >扫一扫添加</view>
					<!-- <view class="mt-10 gui-h6">还可以邦定{{6-cardList.length}}人</view> -->
				</view>
				<!-- <view class="addc-ion"><text class="gui-icons gui-color-gray font-bold">&#xe601;</text></view>	 -->
			</view>
		</view>
		<!-- <view class="mt-10 gui-h6">（还可以邦定{{6-cardList.length}}人）</view> -->
		<!-- 院内就诊卡卡片 -->
		<view v-for="(item,index) in cardList" :key="index" @tap="card(item)">
			<view class="gui-card-view gui-margin-top gui-card-view2">
				<view class="gui-card-body  gui-flex gui-rows gui-nowrap">
					<view class="gui-card-desc">
						<view class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center">
							<text class="gui-card-text">{{appName || '治未病科'}}</text>
							<!-- <text class="gui-card-text" v-if="domainType == 2">贵阳市中医名医会馆</text>
							<text class="gui-card-text" v-else-if="domainType == 3">灵山县中医医院</text>
							<text class="gui-card-text" v-else>广西中医药大学第一附属医院</text> -->
						</view>
						<view class="gui-flex gui-rows gui-nowrap gui-space-between">
							<view class="gui-card-footer">
								<view class="gui-color-white gui-block-text font-bold" style="margin:10rpx 0;">{{item.userName}}
									<text v-if="item.relationship" style="margin-left: 10rpx;font-size: 28rpx;">【{{item.relationship}}】</text>
									<text v-if="item.isDefaultPatient==1" style="margin-left: 10rpx;font-size: 28rpx;">(默认卡)</text>
								</view>
								<text class="gui-block-text fs-50 gui-color-white">{{item.visitCardNum}}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="gui-card-footer gui-flex gui-justify-content-center">
					<text class="gui-card-text">中华人民共和国国家卫生健康委员会监制</text>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	import {selectVisitCardInfo} from '@/api/my.js'
	import {getCard} from '@/api/home.js'
	export default {
		data() {
			return {
				cardList:[],
				type:1   ,//  1:切换   2：查看
				domainType:this.$common.domainType,//  1:广中医   2：贵阳
				appName:this.$common.appName,
			}
		},
		onLoad(option) {
			if(option.type){this.type = option.type}
		},
		onShow() {
			this.getCard();
		},
		methods: {
			getCard(){
				var self=this;
				getCard().then(res=>{
					this.cardList = res.data.cardList;
				})
			},
			// 添加就诊人
			addPatient(e) {
				if(this.cardList.length==6){
					this.$common.msg('最多只能绑6张就诊卡！');
					return;
				}
				if(e){
					// 手动
					this.$common.navTo('/pages/myAc/other/addPatient')
					return
				}
				var that = this
				//扫码
				uni.scanCode({
					success: (res) => {
						if (res.result || res.path) {
							var val = '';
							val = res.path.split("=")[1]
							if(val&&val[0] == 'p'){
								var visitCardNum = val.slice(2)
								getCard().then(res=>{
									var cardList = res.data.cardList || []
									let item = cardList.findIndex(item => {
										return item.visitCardNum == visitCardNum || item.idCard == visitCardNum;
									});
									if(item<0){
										that.$common.navTo('/pages/myAc/other/addPatient?visitCardNum=' + visitCardNum)
									}else{
										setTimeout(() => {
											that.$common.msg('已有绑定就诊卡','',3000)
										}, 500);
									}
								})
								return
							}
							that.$common.msg('请重新扫描','',3000)
						} else {
							that.$common.msg('请重新扫描','',3000)
							return false;
						}
					},fail: (res) => {
					}
				})
				
			},
			card(item) {
				if (this.type == 1) {
					selectVisitCardInfo({
						patientId: item.patientId || '',
						memberId: uni.getStorageSync('user')?.id || ''
					}).then(res=>{
						uni.setStorageSync('cardObj', item)
						this.$common.role();
						uni.setStorageSync('showlegao', 1)
						this.$common.navBack(1)
					})
				} else {
					this.$common.navTo('./card-id?item=' + encodeURIComponent(JSON.stringify(item)))
				}
			}
		}
	}
</script>
<style>
	.list-card-row{
		display: flex;
		flex-direction: row;
		justify-content: space-around;
	}
	.addc{
		display: flex;
		justify-content: center;
		align-items: center;
		border: 1px dashed #C59F79;
		background: #fbf7f5;
		border-radius: 10rpx;
		flex:1;
		margin-top: 40rpx;
		color: #C59F79;
	}
	.addc-ion{font-size: 40rpx;padding-right: 10rpx;}
	.addc-text{
		/* width: 90%; */
		text-align: center;
		font-size: 32rpx;
	}
	/* 卡片视图 */
	.gui-text {
		font-size: 34rpx;
	}
	
	.gui-card-view {
		padding: 25rpx;
		border-radius: 10rpx;
	}
	
	.gui-card-view2 {
		background-image: url(https://img.starup.net.cn/xmkj/zwb/img/zy_pre_jjkbg.png);
		background-size: 100%;
		background-repeat: no-repeat;
	}

	.gui-card-view-firet {
		/* background: url(https://img.starup.net.cn/xmkj/zwb/img/zy_pre_jjkbg.png); */
	}

	.gui-card-body {
		padding-bottom: 25rpx;
	}

	.gui-card-img {
		width: 150rpx;
		height: 150rpx;
		border-radius: 10rpx;
	}

	.gui-card-desc {
		margin-top: 40rpx;
		width: 400rpx;
		margin-left: 25rpx;
		flex: 1;
	}

	.gui-card-name {
		font-size: 28rpx;
		color: #333333;
		line-height: 40rpx;
		margin-right: 20rpx;
	}

	.gui-card-text {
		line-height: 40rpx;
		font-size: 30rpx;
		color: rgba(255,255,255,0.5);
	}

	.gui-card-footer {
		margin-top: 20rpx;
	}

	.gui-card-footer-item {
		width: 100rpx;
		text-align: center;
		flex: 1;
		line-height: 38rpx;
		font-size: 26rpx;
		color: #ffffff;
	}

	/* #ifndef APP-NVUE */
	.gui-card-footer-item {
		display: block;
	}

	/* #endif */
</style>
